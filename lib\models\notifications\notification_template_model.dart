import 'package:motorcycle_parts_shop/models/notifications/notification_model.dart';

class NotificationTemplateModel {
  final String id;
  final String type;
  final String? notificationTypeId;
  final String titleTemplate;
  final String messageTemplate;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationTemplateModel({
    required this.id,
    required this.type,
    this.notificationTypeId,
    required this.titleTemplate,
    required this.messageTemplate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationTemplateModel.fromJson(Map<String, dynamic> json) {
    return NotificationTemplateModel(
      id: json['id'] as String,
      type: json['type'] as String,
      notificationTypeId: json['notification_type_id'] as String?,
      titleTemplate: json['title_template'] as String,
      messageTemplate: json['message_template'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'notification_type_id': notificationTypeId,
      'title_template': titleTemplate,
      'message_template': messageTemplate,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  NotificationTemplateModel copyWith({
    String? id,
    String? type,
    String? notificationTypeId,
    String? titleTemplate,
    String? messageTemplate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationTemplateModel(
      id: id ?? this.id,
      type: type ?? this.type,
      notificationTypeId: notificationTypeId ?? this.notificationTypeId,
      titleTemplate: titleTemplate ?? this.titleTemplate,
      messageTemplate: messageTemplate ?? this.messageTemplate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  NotificationModel createNotification({
    required String userId,
    required Map<String, String> variables,
  }) {
    String parsedTitle = titleTemplate;
    String parsedMessage = messageTemplate;

    variables.forEach((key, value) {
      parsedTitle = parsedTitle.replaceAll('{$key}', value);
      parsedMessage = parsedMessage.replaceAll('{$key}', value);
    });

    return NotificationModel(
      id: '',
      userId: userId,
      templateId: id, // Using the template's id as templateId
      title: parsedTitle,
      body: parsedMessage,
      type: type,
      createdAt: DateTime.now(),
      channel: 'in_app', // القناة الافتراضية هي داخل التطبيق
      isDelivered: false,
      isActionable: false,
      isArchived: false,
    );
  }
}
