import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/gradients.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/core/widgets/product_grid_item.dart';
import 'package:provider/provider.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
/// شاشة المنتجات الجديدة
class NewProductsScreen extends StatefulWidget {
  const NewProductsScreen({super.key});

  @override
  State<NewProductsScreen> createState() => _NewProductsScreenState();
}

class _NewProductsScreenState extends State<NewProductsScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late ProductService _productService;
  List<ProductModel> _newProducts = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;
  int _currentPage = 0;
  final int _pageSize = 20;

  // متغيرات للرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  int _selectedNavIndex = 0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuint),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeService();
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// تهيئة الخدمة
  Future<void> _initializeService() async {
    final authService = Provider.of<AuthSupabaseService>(
      context,
      listen: false,
    );
    _productService = ProductService(authService.client);
    await _loadNewProducts();
  }

  /// تحميل المنتجات الجديدة
  Future<void> _loadNewProducts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _currentPage = 0;
    });

    try {
      final products = await _productService.getNewProducts(
        page: _currentPage,
        pageSize: _pageSize,
      );

      setState(() {
        _newProducts = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// تحميل المزيد من المنتجات عند التمرير
  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMoreProducts();
    }
  }

  /// تحميل المزيد من المنتجات
  Future<void> _loadMoreProducts() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;
      final moreProducts = await _productService.getNewProducts(
        page: _currentPage,
        pageSize: _pageSize,
      );

      setState(() {
        _newProducts.addAll(moreProducts);
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildAnimatedAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildAnimatedBottomNavigationBar(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  /// بناء شريط التطبيق المتحرك والجذاب
  PreferredSizeWidget _buildAnimatedAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight + 10),
      child: ResponsiveBuilder(
        builder: (context, constraints) {
          final padding = ResponsiveHelper.getPadding(
            context,
            mobile: 8.0,
            tablet: 12.0,
            desktop: 16.0,
          );

          return Container(
            decoration: BoxDecoration(
              gradient: AppGradients.blueWaveGradient,
              boxShadow: AppTheme.cardShadow,
            ),
            child: SafeArea(
              child: ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: padding,
                      vertical: 4,
                    ),
                    child: Row(
                      children: [
                        // زر الرجوع مع تأثير زجاجي
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.arrow_back_ios_new_rounded),
                            color: Colors.white,
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ),

                        const SizedBox(width: 12),

                        // عنوان الصفحة مع تأثيرات متحركة
                        Expanded(
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: SlideTransition(
                              position: _slideAnimation,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'وصل حديثاً',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: ResponsiveHelper.getFontSize(
                                        context,
                                        ResponsiveHelper.isMobile(context)
                                            ? 20
                                            : 24,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    'اكتشف أحدث المنتجات في متجرنا',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.8),
                                      fontSize: ResponsiveHelper.getFontSize(
                                        context,
                                        ResponsiveHelper.isMobile(context)
                                            ? 12
                                            : 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        // أزرار الإجراءات مع تأثيرات زجاجية
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.refresh_rounded),
                            color: Colors.white,
                            onPressed: _loadNewProducts,
                            tooltip: 'تحديث',
                          ),
                        ),

                        const SizedBox(width: 8),

                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.search_rounded),
                            color: Colors.white,
                            onPressed: () {
                              // إظهار مربع البحث
                              showSearch(
                                context: context,
                                delegate: ProductSearchDelegate(_newProducts),
                              );
                            },
                            tooltip: 'بحث',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء شريط التنقل السفلي المتحرك والجذاب
  Widget _buildAnimatedBottomNavigationBar() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final isTabletOrLarger = !ResponsiveHelper.isMobile(context);
        final bottomPadding = ResponsiveHelper.getPadding(
          context,
          mobile: 8.0,
          tablet: 12.0,
          desktop: 16.0,
        );

        return Container(
          height: isTabletOrLarger ? 80 : 70,
          decoration: BoxDecoration(
            gradient: AppGradients.blueWaveGradient,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Padding(
                padding: EdgeInsets.only(
                  left: bottomPadding,
                  right: bottomPadding,
                  bottom: bottomPadding,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildNavItem(
                      icon: Icons.home_rounded,
                      label: 'الرئيسية',
                      isSelected: _selectedNavIndex == 0,
                      onTap: () {
                        setState(() {
                          _selectedNavIndex = 0;
                        });
                        Navigator.of(context).pushReplacementNamed('/home');
                      },
                    ),
                    _buildNavItem(
                      icon: Icons.category_rounded,
                      label: 'الفئات',
                      isSelected: _selectedNavIndex == 1,
                      onTap: () {
                        setState(() {
                          _selectedNavIndex = 1;
                        });
                        // التنقل إلى صفحة الفئات
                      },
                    ),
                    // مساحة للزر العائم
                    const SizedBox(width: 40),
                    _buildNavItem(
                      icon: Icons.shopping_cart_rounded,
                      label: 'السلة',
                      isSelected: _selectedNavIndex == 2,
                      onTap: () {
                        setState(() {
                          _selectedNavIndex = 2;
                        });
                        Navigator.of(context).pushNamed('/cart');
                      },
                    ),
                    _buildNavItem(
                      icon: Icons.person_rounded,
                      label: 'حسابي',
                      isSelected: _selectedNavIndex == 3,
                      onTap: () {
                        setState(() {
                          _selectedNavIndex = 3;
                        });
                        // التنقل إلى صفحة الحساب
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء عنصر في شريط التنقل السفلي
  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? Colors.white.withOpacity(0.3)
                      : Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              boxShadow:
                  isSelected
                      ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                      : null,
            ),
            child: Icon(
              icon,
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر العمل العائم
  Widget _buildFloatingActionButton() {
    return Container(
      height: 65,
      width: 65,
      margin: const EdgeInsets.only(top: 30),
      child: FloatingActionButton(
        backgroundColor: AppTheme.secondaryColor,
        elevation: 8,
        onPressed: () {
          // إظهار قائمة الإجراءات السريعة
          _showQuickActionsMenu();
        },
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: AppGradients.secondaryGradient,
            boxShadow: [
              BoxShadow(
                color: AppTheme.secondaryColor.withOpacity(0.3),
                blurRadius: 15,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Icon(Icons.add_rounded, color: Colors.white, size: 32),
        ),
      ),
    );
  }

  /// إظهار قائمة الإجراءات السريعة
  void _showQuickActionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppGradients.blueWaveGradient,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'إجراءات سريعة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildQuickAction(
                      icon: Icons.search,
                      label: 'بحث',
                      onTap: () {
                        Navigator.pop(context);
                        showSearch(
                          context: context,
                          delegate: ProductSearchDelegate(_newProducts),
                        );
                      },
                    ),
                    _buildQuickAction(
                      icon: Icons.favorite,
                      label: 'المفضلة',
                      onTap: () {
                        Navigator.pop(context);
                        // التنقل إلى صفحة المفضلة
                      },
                    ),
                    _buildQuickAction(
                      icon: Icons.notifications,
                      label: 'الإشعارات',
                      onTap: () {
                        Navigator.pop(context);
                        // التنقل إلى صفحة الإشعارات
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildQuickAction(
                      icon: Icons.shopping_bag,
                      label: 'طلباتي',
                      onTap: () {
                        Navigator.pop(context);
                        // التنقل إلى صفحة الطلبات
                      },
                    ),
                    _buildQuickAction(
                      icon: Icons.support_agent,
                      label: 'الدعم',
                      onTap: () {
                        Navigator.pop(context);
                        // التنقل إلى صفحة الدعم
                      },
                    ),
                    _buildQuickAction(
                      icon: Icons.settings,
                      label: 'الإعدادات',
                      onTap: () {
                        Navigator.pop(context);
                        // التنقل إلى صفحة الإعدادات
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white.withOpacity(0.2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 40,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text('إغلاق'),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  /// بناء إجراء سريع
  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading && _newProducts.isEmpty) {
      return const LoadingIndicator();
    }

    if (_error != null && _newProducts.isEmpty) {
      return _buildErrorState();
    }

    if (_newProducts.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadNewProducts,
      child: Column(
        children: [
          _buildHeader(_newProducts.length),
          Expanded(
            child: ResponsiveBuilder(
              builder: (context, constraints) {
                // تحديد عدد الأعمدة بناءً على حجم الشاشة
                final crossAxisCount = ResponsiveHelper.getGridColumns(
                  context,
                  mobileColumns: 2,
                  tabletColumns: 3,
                  desktopColumns: 4,
                );

                // تحديد نسبة العرض إلى الارتفاع
                final aspectRatio = ResponsiveHelper.getAspectRatio(
                  context,
                  mobile: 0.75,
                  tablet: 0.85,
                  desktop: 0.95,
                );

                // تحديد المسافات بين العناصر
                final spacing = ResponsiveHelper.getPadding(
                  context,
                  mobile: 16.0,
                  tablet: 20.0,
                  desktop: 24.0,
                );

                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: GridView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.all(
                      ResponsiveHelper.getPadding(context),
                    ),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      childAspectRatio: aspectRatio,
                      crossAxisSpacing: spacing,
                      mainAxisSpacing: spacing,
                    ),
                    itemCount:
                        _newProducts.length +
                        (_isLoadingMore ? crossAxisCount : 0),
                    itemBuilder: (context, index) {
                      if (index >= _newProducts.length) {
                        return const LoadingIndicator();
                      }

                      final product = _newProducts[index];
                      return ProductGridItem(product: product);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final iconSize = ResponsiveHelper.getIconSize(
          context,
          mobile: 80.0,
          tablet: 100.0,
          desktop: 120.0,
        );

        final spacing = ResponsiveHelper.getSpacing(
          context,
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        );

        final buttonPadding = EdgeInsets.symmetric(
          horizontal: ResponsiveHelper.getPadding(
            context,
            mobile: 24.0,
            tablet: 32.0,
            desktop: 40.0,
          ),
          vertical: ResponsiveHelper.getPadding(
            context,
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
        );

        return Center(
          child: Padding(
            padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: iconSize,
                      color: Colors.red[400],
                    ),
                    SizedBox(height: spacing),
                    Text(
                      'حدث خطأ',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.red[600],
                        fontSize: ResponsiveHelper.getFontSize(
                          context,
                          ResponsiveHelper.isMobile(context) ? 20 : 24,
                        ),
                      ),
                    ),
                    SizedBox(height: spacing / 2),
                    Text(
                      _error ?? 'خطأ غير معروف',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                        fontSize: ResponsiveHelper.getFontSize(
                          context,
                          ResponsiveHelper.isMobile(context) ? 14 : 16,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: spacing * 1.5),
                    ElevatedButton.icon(
                      onPressed: _loadNewProducts,
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: buttonPadding,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader(int productCount) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        );

        final iconSize = ResponsiveHelper.getIconSize(
          context,
          mobile: 24.0,
          tablet: 28.0,
          desktop: 32.0,
        );

        final spacing = ResponsiveHelper.getSpacing(
          context,
          mobile: 8.0,
          tablet: 12.0,
          desktop: 16.0,
        );

        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor.withOpacity(0.15),
                Theme.of(context).primaryColor.withOpacity(0.05),
              ],
            ),
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1.0,
              ),
            ),
          ),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.new_releases,
                        color: Theme.of(context).primaryColor,
                        size: iconSize,
                      ),
                      SizedBox(width: spacing),
                      Text(
                        'المنتجات الجديدة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                          fontSize: ResponsiveHelper.getFontSize(
                            context,
                            ResponsiveHelper.isMobile(context) ? 20 : 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: spacing),
                  Text(
                    'تم العثور على $productCount منتج جديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        ResponsiveHelper.isMobile(context) ? 14 : 16,
                      ),
                    ),
                  ),
                  SizedBox(height: spacing),
                  Text(
                    'اكتشف أحدث قطع الغيار والإكسسوارات المضافة حديثاً لمتجرنا',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        ResponsiveHelper.isMobile(context) ? 12 : 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء حالة عدم وجود منتجات
  Widget _buildEmptyState() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final iconSize = ResponsiveHelper.getIconSize(
          context,
          mobile: 80.0,
          tablet: 100.0,
          desktop: 120.0,
        );

        final spacing = ResponsiveHelper.getSpacing(
          context,
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        );

        final buttonPadding = EdgeInsets.symmetric(
          horizontal: ResponsiveHelper.getPadding(
            context,
            mobile: 24.0,
            tablet: 32.0,
            desktop: 40.0,
          ),
          vertical: ResponsiveHelper.getPadding(
            context,
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
        );

        return Center(
          child: Padding(
            padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.new_releases_outlined,
                      size: iconSize,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: spacing),
                    Text(
                      'لا توجد منتجات جديدة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.grey[600],
                        fontSize: ResponsiveHelper.getFontSize(
                          context,
                          ResponsiveHelper.isMobile(context) ? 20 : 24,
                        ),
                      ),
                    ),
                    SizedBox(height: spacing / 2),
                    Text(
                      'لم يتم إضافة أي منتجات جديدة مؤخراً',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                        fontSize: ResponsiveHelper.getFontSize(
                          context,
                          ResponsiveHelper.isMobile(context) ? 14 : 16,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: spacing * 1.5),
                    ElevatedButton.icon(
                      onPressed: _loadNewProducts,
                      icon: const Icon(Icons.refresh),
                      label: const Text('تحديث'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: buttonPadding,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// مندوب البحث في المنتجات
class ProductSearchDelegate extends SearchDelegate<ProductModel?> {
  final List<ProductModel> products;

  ProductSearchDelegate(this.products);

  @override
  String get searchFieldLabel => 'ابحث عن منتج...';

  @override
  TextStyle? get searchFieldStyle =>
      const TextStyle(color: Colors.white70, fontSize: 16);

  @override
  ThemeData appBarTheme(BuildContext context) {
    return ThemeData(
      appBarTheme: AppBarTheme(
        backgroundColor: AppTheme.primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        toolbarTextStyle:
            const TextTheme(
              titleLarge: TextStyle(color: Colors.white, fontSize: 18),
            ).bodyMedium,
        titleTextStyle:
            const TextTheme(
              titleLarge: TextStyle(color: Colors.white, fontSize: 18),
            ).titleLarge,
      ),
      inputDecorationTheme: InputDecorationTheme(
        hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
        border: InputBorder.none,
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: Colors.white,
        selectionColor: Colors.white.withOpacity(0.3),
        selectionHandleColor: Colors.white,
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            query = '';
            showSuggestions(context);
          },
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: AnimatedIcon(
        icon: AnimatedIcons.menu_arrow,
        progress: transitionAnimation,
      ),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    final results =
        products
            .where(
              (product) =>
                  product.name.toLowerCase().contains(query.toLowerCase()) ||
                  (product.description?.toLowerCase() ?? '').contains(
                    query.toLowerCase(),
                  ),
            )
            .toList();

    if (results.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off_rounded, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج لـ "$query"',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'حاول البحث بكلمات مختلفة',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(gradient: AppGradients.backgroundGradient),
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: results.length,
        itemBuilder: (context, index) {
          final product = results[index];
          return ProductGridItem(product: product);
        },
      ),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return Container(
        decoration: BoxDecoration(gradient: AppGradients.backgroundGradient),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_rounded,
                size: 80,
                color: AppTheme.primaryColor.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'ابحث عن منتجاتك المفضلة',
                style: TextStyle(
                  fontSize: 18,
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'اكتب اسم المنتج أو وصفه للبحث',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textTertiaryColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final suggestions =
        products
            .where(
              (product) =>
                  product.name.toLowerCase().contains(query.toLowerCase()) ||
                  (product.description?.toLowerCase() ?? '').contains(
                    query.toLowerCase(),
                  ),
            )
            .toList();

    return Container(
      decoration: BoxDecoration(gradient: AppGradients.backgroundGradient),
      child: ListView.builder(
        itemCount: suggestions.length,
        itemBuilder: (context, index) {
          final product = suggestions[index];
          return ListTile(
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image:
                    product.imageUrl != null
                        ? DecorationImage(
                          image: NetworkImage(product.imageUrl!),
                          fit: BoxFit.cover,
                        )
                        : null,
              ),
            ),
            title: Text(
              product.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '${product.price} ج.م',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios_rounded, size: 16),
            onTap: () {
              query = product.name;
              showResults(context);
            },
          );
        },
      ),
    );
  }
}
