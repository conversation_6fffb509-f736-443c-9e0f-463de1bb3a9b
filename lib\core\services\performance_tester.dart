import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'optimized_product_service.dart';
import 'optimized_cart_service.dart';
import 'secure_data_manager.dart';

/// خدمة اختبار الأداء للتحسينات المطبقة
class PerformanceTester {
  static final PerformanceTester _instance = PerformanceTester._internal();
  factory PerformanceTester() => _instance;
  PerformanceTester._internal();

  /// اختبار شامل للأداء
  Future<PerformanceTestResults> runFullPerformanceTest() async {
    debugPrint('🧪 بدء اختبار الأداء الشامل...');
    
    final results = PerformanceTestResults();
    
    try {
      // اختبار تحميل البيانات
      results.dataLoadingTest = await _testDataLoading();
      
      // اختبار الذاكرة المؤقتة
      results.cacheTest = await _testCaching();
      
      // اختبار التخزين الآمن
      results.secureStorageTest = await _testSecureStorage();
      
      // اختبار عمليات السلة
      results.cartOperationsTest = await _testCartOperations();
      
      // اختبار إدارة الذاكرة
      results.memoryManagementTest = await _testMemoryManagement();
      
      debugPrint('✅ تم إكمال اختبار الأداء الشامل');
      
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الأداء: $e');
      results.hasErrors = true;
      results.errorMessage = e.toString();
    }
    
    return results;
  }

  /// اختبار تحميل البيانات
  Future<TestResult> _testDataLoading() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // محاكاة خدمة المنتجات
      final productService = OptimizedProductService(null as dynamic);
      
      // اختبار تحميل المنتجات المميزة
      final featuredProducts = await _simulateProductLoading(10);
      
      // اختبار تحميل الأكثر مبيعاً
      final bestSelling = await _simulateProductLoading(10);
      
      // اختبار تحميل العروض
      final offers = await _simulateProductLoading(8);
      
      stopwatch.stop();
      
      return TestResult(
        testName: 'تحميل البيانات',
        duration: stopwatch.elapsedMilliseconds,
        success: true,
        details: {
          'featured_products': featuredProducts.length,
          'best_selling': bestSelling.length,
          'offers': offers.length,
        },
      );
      
    } catch (e) {
      stopwatch.stop();
      return TestResult(
        testName: 'تحميل البيانات',
        duration: stopwatch.elapsedMilliseconds,
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// اختبار الذاكرة المؤقتة
  Future<TestResult> _testCaching() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final cache = <String, dynamic>{};
      
      // اختبار كتابة الذاكرة المؤقتة
      for (int i = 0; i < 100; i++) {
        cache['key_$i'] = 'value_$i';
      }
      
      // اختبار قراءة الذاكرة المؤقتة
      int readCount = 0;
      for (int i = 0; i < 100; i++) {
        if (cache.containsKey('key_$i')) {
          readCount++;
        }
      }
      
      // اختبار تنظيف الذاكرة المؤقتة
      cache.clear();
      
      stopwatch.stop();
      
      return TestResult(
        testName: 'الذاكرة المؤقتة',
        duration: stopwatch.elapsedMilliseconds,
        success: readCount == 100,
        details: {
          'write_operations': 100,
          'read_operations': readCount,
          'cache_size_after_clear': cache.length,
        },
      );
      
    } catch (e) {
      stopwatch.stop();
      return TestResult(
        testName: 'الذاكرة المؤقتة',
        duration: stopwatch.elapsedMilliseconds,
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// اختبار التخزين الآمن
  Future<TestResult> _testSecureStorage() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // محاكاة عمليات التخزين الآمن
      final testData = {
        'user_token': 'test_token_123',
        'user_preferences': {'theme': 'dark', 'language': 'ar'},
        'sensitive_data': 'encrypted_sensitive_information',
      };
      
      // اختبار الكتابة
      int writeOperations = 0;
      for (final entry in testData.entries) {
        // محاكاة التشفير والحفظ
        await _simulateEncryption(entry.value.toString());
        writeOperations++;
      }
      
      // اختبار القراءة
      int readOperations = 0;
      for (final key in testData.keys) {
        // محاكاة القراءة وفك التشفير
        await _simulateDecryption();
        readOperations++;
      }
      
      stopwatch.stop();
      
      return TestResult(
        testName: 'التخزين الآمن',
        duration: stopwatch.elapsedMilliseconds,
        success: writeOperations == testData.length && readOperations == testData.length,
        details: {
          'write_operations': writeOperations,
          'read_operations': readOperations,
          'encryption_test': 'passed',
        },
      );
      
    } catch (e) {
      stopwatch.stop();
      return TestResult(
        testName: 'التخزين الآمن',
        duration: stopwatch.elapsedMilliseconds,
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// اختبار عمليات السلة
  Future<TestResult> _testCartOperations() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // محاكاة عمليات السلة
      final cartItems = <String, int>{};
      
      // اختبار إضافة عناصر للسلة
      for (int i = 0; i < 20; i++) {
        cartItems['product_$i'] = Random().nextInt(5) + 1;
      }
      
      // اختبار تحديث الكميات
      int updateOperations = 0;
      for (final key in cartItems.keys.take(10)) {
        cartItems[key] = cartItems[key]! + 1;
        updateOperations++;
      }
      
      // اختبار حذف عناصر
      int deleteOperations = 0;
      final keysToDelete = cartItems.keys.take(5).toList();
      for (final key in keysToDelete) {
        cartItems.remove(key);
        deleteOperations++;
      }
      
      // حساب الإجمالي
      final totalItems = cartItems.values.fold(0, (sum, quantity) => sum + quantity);
      
      stopwatch.stop();
      
      return TestResult(
        testName: 'عمليات السلة',
        duration: stopwatch.elapsedMilliseconds,
        success: true,
        details: {
          'add_operations': 20,
          'update_operations': updateOperations,
          'delete_operations': deleteOperations,
          'final_items_count': cartItems.length,
          'total_quantity': totalItems,
        },
      );
      
    } catch (e) {
      stopwatch.stop();
      return TestResult(
        testName: 'عمليات السلة',
        duration: stopwatch.elapsedMilliseconds,
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// اختبار إدارة الذاكرة
  Future<TestResult> _testMemoryManagement() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // محاكاة استخدام الذاكرة
      final largeList = <String>[];
      
      // إنشاء بيانات كبيرة
      for (int i = 0; i < 10000; i++) {
        largeList.add('data_item_$i' * 10);
      }
      
      // محاكاة معالجة البيانات
      final processedData = largeList.where((item) => item.contains('5')).toList();
      
      // تنظيف الذاكرة
      largeList.clear();
      
      stopwatch.stop();
      
      return TestResult(
        testName: 'إدارة الذاكرة',
        duration: stopwatch.elapsedMilliseconds,
        success: largeList.isEmpty,
        details: {
          'initial_items': 10000,
          'processed_items': processedData.length,
          'memory_cleared': largeList.isEmpty,
        },
      );
      
    } catch (e) {
      stopwatch.stop();
      return TestResult(
        testName: 'إدارة الذاكرة',
        duration: stopwatch.elapsedMilliseconds,
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// محاكاة تحميل المنتجات
  Future<List<Map<String, dynamic>>> _simulateProductLoading(int count) async {
    await Future.delayed(Duration(milliseconds: Random().nextInt(100) + 50));
    
    return List.generate(count, (index) => {
      'id': 'product_$index',
      'name': 'منتج $index',
      'price': Random().nextDouble() * 1000,
      'rating': Random().nextDouble() * 5,
    });
  }

  /// محاكاة التشفير
  Future<String> _simulateEncryption(String data) async {
    await Future.delayed(Duration(milliseconds: Random().nextInt(10) + 5));
    return 'encrypted_$data';
  }

  /// محاكاة فك التشفير
  Future<String> _simulateDecryption() async {
    await Future.delayed(Duration(milliseconds: Random().nextInt(10) + 5));
    return 'decrypted_data';
  }

  /// طباعة نتائج الاختبار
  void printTestResults(PerformanceTestResults results) {
    if (kDebugMode) {
      print('📊 نتائج اختبار الأداء:');
      print('=====================================');
      
      _printTestResult(results.dataLoadingTest);
      _printTestResult(results.cacheTest);
      _printTestResult(results.secureStorageTest);
      _printTestResult(results.cartOperationsTest);
      _printTestResult(results.memoryManagementTest);
      
      if (results.hasErrors) {
        print('❌ أخطاء: ${results.errorMessage}');
      }
      
      print('=====================================');
      print('إجمالي وقت الاختبار: ${results.totalDuration} ms');
      print('معدل النجاح: ${results.successRate.toStringAsFixed(1)}%');
    }
  }

  void _printTestResult(TestResult result) {
    final status = result.success ? '✅' : '❌';
    print('$status ${result.testName}: ${result.duration} ms');
    
    if (result.details != null) {
      result.details!.forEach((key, value) {
        print('   - $key: $value');
      });
    }
    
    if (!result.success && result.errorMessage != null) {
      print('   خطأ: ${result.errorMessage}');
    }
  }
}

/// نتائج اختبار الأداء
class PerformanceTestResults {
  TestResult dataLoadingTest = TestResult.empty();
  TestResult cacheTest = TestResult.empty();
  TestResult secureStorageTest = TestResult.empty();
  TestResult cartOperationsTest = TestResult.empty();
  TestResult memoryManagementTest = TestResult.empty();
  
  bool hasErrors = false;
  String? errorMessage;

  int get totalDuration {
    return dataLoadingTest.duration +
           cacheTest.duration +
           secureStorageTest.duration +
           cartOperationsTest.duration +
           memoryManagementTest.duration;
  }

  double get successRate {
    final tests = [dataLoadingTest, cacheTest, secureStorageTest, cartOperationsTest, memoryManagementTest];
    final successCount = tests.where((test) => test.success).length;
    return (successCount / tests.length) * 100;
  }
}

/// نتيجة اختبار واحد
class TestResult {
  final String testName;
  final int duration;
  final bool success;
  final Map<String, dynamic>? details;
  final String? errorMessage;

  TestResult({
    required this.testName,
    required this.duration,
    required this.success,
    this.details,
    this.errorMessage,
  });

  factory TestResult.empty() => TestResult(
    testName: '',
    duration: 0,
    success: false,
  );
}
