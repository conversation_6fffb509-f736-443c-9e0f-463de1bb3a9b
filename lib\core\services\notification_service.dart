import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/vibration_service.dart';
import 'package:motorcycle_parts_shop/models/notifications/notification_model.dart';

/// فئة إجراء الإشعار
class NotificationAction {
  final String id;
  final String title;
  final String action;
  final Map<String, dynamic>? data;

  NotificationAction({
    required this.id,
    required this.title,
    required this.action,
    this.data,
  });

  Map<String, dynamic> toJson() {
    return {'id': id, 'title': title, 'action': action, 'data': data};
  }

  factory NotificationAction.fromJson(Map<String, dynamic> json) {
    return NotificationAction(
      id: json['id'],
      title: json['title'],
      action: json['action'],
      data: json['data'],
    );
  }
}

/// خدمة الإشعارات المحسنة
class NotificationService extends ChangeNotifier {
  static final NotificationService _instance = NotificationService._internal();
  final AuthSupabaseService _authService = AuthSupabaseService();
  VibrationService? _vibrationService;

  List<NotificationModel> _notifications = [];
  Map<String, bool> _settings = {};

  bool _isInitialized = false;

  factory NotificationService() => _instance;
  NotificationService._internal();

  /// تعيين خدمة الاهتزاز
  void setVibrationService(VibrationService vibrationService) {
    _vibrationService = vibrationService;
  }

  // Getters
  List<NotificationModel> get notifications => _notifications;
  Map<String, bool> get notificationSettings => _settings;
  bool get isInitialized => _isInitialized;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (!_authService.isInitialized) {
        await _authService.initialize();
      }

      await _loadNotifications();
      await _loadSettings();

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// تحميل الإشعارات
  Future<void> _loadNotifications() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      final response = await _authService.client
          .from(AppConstants.notificationsTable)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final previousNotificationsCount = _notifications.length;
      final previousUnreadCount = _notifications.where((n) => !n.isRead).length;

      _notifications =
          response
              .map<NotificationModel>(
                (json) => NotificationModel.fromJson(json),
              )
              .toList();

      // تحديث إحصائيات الإشعارات في قاعدة البيانات
      await _updateNotificationStats();

      // التحقق من وجود إشعارات جديدة وتفعيل الاهتزاز
      final currentNotificationsCount = _notifications.length;
      final currentUnreadCount = _notifications.where((n) => !n.isRead).length;

      if (currentNotificationsCount > previousNotificationsCount ||
          currentUnreadCount > previousUnreadCount) {
        // هناك إشعارات جديدة، تفعيل الاهتزاز
        _vibrationService?.vibrateOnNotification();
      }

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات: $e');
    }
  }

  /// تحديث إحصائيات الإشعارات في قاعدة البيانات
  Future<void> _updateNotificationStats() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      final unreadCount = _notifications.where((n) => !n.isRead).length;
      final totalCount = _notifications.length;

      // تحديث إحصائيات المستخدم
      try {
        await _authService.client
            .from(AppConstants.userNotificationStatsTable)
            .upsert({
              'user_id': userId,
              'total_notifications': totalCount,
              'unread_notifications': unreadCount,
              'last_notification_at':
                  _notifications.isNotEmpty
                      ? _notifications.first.createdAt.toIso8601String()
                      : null,
              'last_updated': DateTime.now().toIso8601String(),
            }, onConflict: 'user_id');

        debugPrint('✅ تم تحديث إحصائيات الإشعارات بنجاح');
      } catch (e) {
        debugPrint('❌ خطأ في تحديث إحصائيات الإشعارات: $e');
        // لا نرمي الخطأ لتجنب توقف العملية الأساسية
      }

      // تحديث إحصائيات عامة للنظام
      await _updateSystemNotificationStats();
    } catch (e) {
      debugPrint('❌ خطأ في تحديث إحصائيات الإشعارات: $e');
    }
  }

  /// تحديث إحصائيات النظام العامة
  Future<void> _updateSystemNotificationStats() async {
    try {
      // تجميع إحصائيات حسب نوع الإشعار
      final typeStats = <String, int>{};
      for (final notification in _notifications) {
        typeStats[notification.type] = (typeStats[notification.type] ?? 0) + 1;
      }

      // حفظ الإحصائيات في قاعدة البيانات
      for (final entry in typeStats.entries) {
        await _authService.client
            .from(AppConstants.notificationAnalyticsTable)
            .upsert({
              'notification_type': entry.key,
              'count': entry.value,
              'date':
                  DateTime.now().toIso8601String().split('T')[0], // تاريخ فقط
              'updated_at': DateTime.now().toIso8601String(),
            }, onConflict: 'notification_type,date');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث إحصائيات النظام: $e');
    }
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      // إعدادات افتراضية في حالة فشل الوصول لقاعدة البيانات
      final fallbackSettings = <String, bool>{
        'order_updates': true,
        'product_restocks': true,
        'price_drops': true,
        'promotions': true,
        'system_messages': true,
        'new_products': false,
        'support_updates': true,
        'account_security': true,
        'newsletter': false,
        'maintenance': true,
      };

      Map<String, bool> defaultSettings = {};

      try {
        // محاولة جلب أنواع الإشعارات المتاحة من قاعدة البيانات
        final availableTypesResponse = await _authService.client
            .from(AppConstants.notificationTypesTable)
            .select('type_key, default_enabled')
            .eq('is_active', true);

        // إنشاء خريطة الإعدادات الافتراضية من قاعدة البيانات
        for (final type in availableTypesResponse) {
          defaultSettings[type['type_key']] = type['default_enabled'] ?? true;
        }

        debugPrint(
          '✅ تم تحميل أنواع الإشعارات من قاعدة البيانات: ${defaultSettings.length}',
        );
      } catch (e) {
        debugPrint(
          '⚠️ فشل في تحميل أنواع الإشعارات، استخدام الإعدادات الافتراضية: $e',
        );
        defaultSettings = fallbackSettings;
      }

      // جلب إعدادات المستخدم المخصصة
      final userSettingsResponse =
          await _authService.client
              .from(AppConstants.notificationSettingsTable)
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      if (userSettingsResponse != null) {
        // دمج الإعدادات الافتراضية مع إعدادات المستخدم
        _settings = Map<String, bool>.from(defaultSettings);
        for (final key in defaultSettings.keys) {
          if (userSettingsResponse.containsKey(key)) {
            _settings[key] = userSettingsResponse[key] ?? defaultSettings[key]!;
          }
        }
      } else {
        // استخدام الإعدادات الافتراضية فقط
        _settings = defaultSettings;
      }

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الإشعارات: $e');
      // في حالة الفشل، استخدام إعدادات افتراضية محدودة
      _settings = {
        'order_updates': true,
        'product_restocks': true,
        'price_drops': true,
        'promotions': true,
        'system_messages': true,
      };
    }
  }

  /// وضع علامة مقروء على إشعار
  Future<void> markAsRead(String notificationId) async {
    try {
      await _authService.client
          .from('notifications')
          .update({'is_read': true})
          .eq('id', notificationId);

      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('خطأ في وضع علامة مقروء: $e');
    }
  }

  /// وضع علامة مقروء على جميع الإشعارات
  Future<void> markAllAsRead() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      await _authService.client
          .from('notifications')
          .update({'is_read': true})
          .eq('user_id', userId)
          .eq('is_read', false);

      for (int i = 0; i < _notifications.length; i++) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في وضع علامة مقروء على جميع الإشعارات: $e');
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _authService.client
          .from(AppConstants.notificationsTable)
          .delete()
          .eq('id', notificationId);

      _notifications.removeWhere((n) => n.id == notificationId);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في حذف الإشعار: $e');
    }
  }

  /// تحديث الإعدادات
  Future<void> updateNotificationSettings(Map<String, bool> newSettings) async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      await _authService.client
          .from(AppConstants.notificationSettingsTable)
          .upsert({'user_id': userId, ...newSettings});

      _settings = Map<String, bool>.from(newSettings);
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات الإشعارات: $e');
    }
  }

  /// إرسال إشعار (للمسؤولين)
  Future<void> sendNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
    bool vibrate = true,
  }) async {
    try {
      await _authService.client.from(AppConstants.notificationsTable).insert({
        'user_id': userId,
        'title': title,
        'body': body,
        'type': type,
        'data': data,
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      // تفعيل الاهتزاز عند إرسال الإشعار
      if (vibrate && _vibrationService != null) {
        switch (type) {
          case 'order':
          case 'order_update':
            // اهتزاز قوي للطلبات وتحديثاتها
            await _vibrationService!.heavyVibrate();
            break;
          case 'stock_alert':
          case 'price_drop':
            // نمط اهتزاز للتنبيهات المهمة
            await _vibrationService!.patternVibrate();
            break;
          default:
            // اهتزاز عادي لباقي الإشعارات
            await _vibrationService!.vibrate();
        }
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار: $e');
      rethrow;
    }
  }

  /// تحديث الإشعارات
  Future<void> refresh() async {
    await _loadNotifications();
  }

  /// إشعار طلب جديد
  Future<void> notifyNewOrder(String orderId, double amount) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: 'طلب جديد!',
      body: 'تم استلام طلب جديد بقيمة ${amount.toStringAsFixed(2)} ريال',
      type: 'order',
      data: {'order_id': orderId, 'amount': amount},
    );
  }

  /// إشعار تحديث حالة الطلب
  Future<void> notifyOrderStatusUpdate(String orderId, String status) async {
    final statusText = _getStatusText(status);

    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: 'تحديث الطلب',
      body: 'تم تحديث حالة طلبك إلى: $statusText',
      type: 'order_update',
      data: {'order_id': orderId, 'status': status},
    );
  }

  /// إشعار عرض خاص
  Future<void> notifySpecialOffer(
    String title,
    String description,
    String productId,
  ) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '🎉 $title',
      body: description,
      type: 'promotion',
      data: {'product_id': productId},
    );
  }

  /// إشعار نفاد المخزون
  Future<void> notifyLowStock(String productName, int quantity) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: 'تنبيه المخزون',
      body: 'المنتج "$productName" أوشك على النفاد (متبقي: $quantity)',
      type: 'stock_alert',
      data: {'product_name': productName, 'quantity': quantity},
    );
  }

  /// إشعار منتج مفضل متوفر
  Future<void> notifyFavoriteProductAvailable(
    String productName,
    String productId,
  ) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: 'منتج مفضل متوفر!',
      body: 'المنتج "$productName" أصبح متوفراً الآن',
      type: 'stock_alert',
      data: {'product_id': productId, 'product_name': productName},
    );
  }

  /// إشعار خصم على منتج
  Future<void> notifyPriceDrop(
    String productName,
    String productId,
    double oldPrice,
    double newPrice,
  ) async {
    final discount = ((oldPrice - newPrice) / oldPrice * 100).round();

    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: 'خصم على منتج مفضل!',
      body: 'المنتج "$productName" متوفر الآن بخصم $discount%',
      type: 'price_drop',
      data: {
        'product_id': productId,
        'product_name': productName,
        'old_price': oldPrice,
        'new_price': newPrice,
        'discount_percentage': discount,
      },
    );
  }

  /// إشعار تذكير بالسلة المهجورة
  Future<void> notifyAbandonedCart(int itemsCount) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '🛒 لا تنس سلتك!',
      body: 'لديك $itemsCount منتج في السلة ينتظر إتمام الطلب',
      type: 'cart_reminder',
      data: {'items_count': itemsCount},
    );
  }

  /// إشعار منتج جديد وصل
  Future<void> notifyNewProductArrival({
    required String productName,
    required String productId,
    required String category,
    double? price,
  }) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '🆕 منتج جديد وصل!',
      body:
          '$productName في قسم $category${price != null ? ' بسعر ${price.toStringAsFixed(2)} ريال' : ''}',
      type: 'new_product',
      data: {
        'product_id': productId,
        'product_name': productName,
        'category': category,
        'price': price,
      },
    );
  }

  /// إشعار عرض خاص محدود الوقت
  Future<void> notifyLimitedTimeOffer({
    required String title,
    required String description,
    required String productId,
    required double discountPercentage,
    required DateTime expiresAt,
  }) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '⏰ $title',
      body: '$description - خصم ${discountPercentage.toInt()}% لفترة محدودة!',
      type: 'limited_offer',
      data: {
        'product_id': productId,
        'discount_percentage': discountPercentage,
        'expires_at': expiresAt.toIso8601String(),
      },
    );
  }

  /// إشعار تحديث حالة التوصيل
  Future<void> notifyDeliveryUpdate({
    required String orderId,
    required String status,
    String? estimatedTime,
    String? driverName,
    String? driverPhone,
  }) async {
    String statusText = _getDeliveryStatusText(status);
    String body = 'حالة توصيل طلبك: $statusText';

    if (estimatedTime != null) {
      body += '\nالوقت المتوقع: $estimatedTime';
    }

    if (driverName != null) {
      body += '\nالمندوب: $driverName';
    }

    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '🚚 تحديث التوصيل',
      body: body,
      type: 'delivery_update',
      data: {
        'order_id': orderId,
        'status': status,
        'estimated_time': estimatedTime,
        'driver_name': driverName,
        'driver_phone': driverPhone,
      },
    );
  }

  /// إشعار وصول المندوب
  Future<void> notifyDriverArrival({
    required String orderId,
    required String driverName,
    required String driverPhone,
  }) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '📍 المندوب وصل!',
      body: 'المندوب $driverName وصل لتسليم طلبك\nرقم الهاتف: $driverPhone',
      type: 'driver_arrival',
      data: {
        'order_id': orderId,
        'driver_name': driverName,
        'driver_phone': driverPhone,
      },
    );
  }

  /// إشعار تأكيد الاستلام
  Future<void> notifyDeliveryConfirmation({
    required String orderId,
    required DateTime deliveredAt,
  }) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '✅ تم التسليم بنجاح',
      body: 'تم تسليم طلبك بنجاح. شكراً لثقتك بنا!',
      type: 'delivery_confirmed',
      data: {
        'order_id': orderId,
        'delivered_at': deliveredAt.toIso8601String(),
      },
    );
  }

  /// إشعار عرض شخصي مخصص
  Future<void> notifyPersonalizedOffer({
    required String title,
    required String description,
    required List<String> productIds,
    required double discountPercentage,
  }) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: '🎁 عرض خاص لك',
      body:
          '$description - خصم ${discountPercentage.toInt()}% على منتجات مختارة خصيصاً لك',
      type: 'personalized_offer',
      data: {
        'product_ids': productIds,
        'discount_percentage': discountPercentage,
        'title': title,
      },
    );
  }

  // تم حذف دالة تقييم المنتج حسب المتطلبات

  /// إشعار رسالة من الدعم
  Future<void> notifySupportMessage(String message) async {
    await sendNotification(
      userId: _authService.currentUser?.id ?? '',
      title: 'رسالة من فريق الدعم',
      body: message,
      type: 'support',
      data: {'message': message},
    );
  }

  /// الحصول على نص الحالة
  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'قيد المعالجة';
      case 'confirmed':
        return 'مؤكد';
      case 'processing':
        return 'قيد التحضير';
      case 'shipped':
        return 'تم الشحن';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  /// الحصول على نص حالة التوصيل
  String _getDeliveryStatusText(String status) {
    switch (status) {
      case 'preparing':
        return 'جاري تحضير الطلب';
      case 'ready_for_pickup':
        return 'جاهز للاستلام';
      case 'picked_up':
        return 'تم استلامه من المتجر';
      case 'in_transit':
        return 'في الطريق إليك';
      case 'nearby':
        return 'المندوب قريب منك';
      case 'arrived':
        return 'المندوب وصل';
      case 'delivered':
        return 'تم التسليم';
      case 'failed_delivery':
        return 'فشل في التسليم';
      case 'returned':
        return 'تم إرجاعه للمتجر';
      default:
        return status;
    }
  }

  /// إرسال إشعارات متقدمة مع تخصيص حسب تفضيلات المستخدم
  Future<void> sendSmartNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
    bool isUrgent = false,
    List<String>? tags,
  }) async {
    try {
      // التحقق من إعدادات المستخدم للنوع المحدد
      if (!_shouldSendNotification(type)) {
        debugPrint('تم تجاهل الإشعار حسب إعدادات المستخدم: $type');
        return;
      }

      // إضافة معلومات إضافية للإشعار
      final enhancedData = {
        ...?data,
        'is_urgent': isUrgent,
        'tags': tags,
        'sent_at': DateTime.now().toIso8601String(),
        'notification_id': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      await _authService.client.from('notifications').insert({
        'user_id': userId,
        'title': title,
        'body': body,
        'type': type,
        'data': enhancedData,
        'is_read': false,
        'is_urgent': isUrgent,
        'created_at': DateTime.now().toIso8601String(),
      });

      // إرسال إشعار فوري للمستخدم النشط
      if (userId == _authService.currentUser?.id) {
        await _loadNotifications();
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار الذكي: $e');
      rethrow;
    }
  }

  /// التحقق من إمكانية إرسال الإشعار حسب إعدادات المستخدم
  bool _shouldSendNotification(String type) {
    return _settings[type] ?? true;
  }

  /// إرسال إشعار تفاعلي مع أزرار
  Future<void> sendInteractiveNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    required List<NotificationAction> actions,
    Map<String, dynamic>? data,
  }) async {
    try {
      final enhancedData = {
        ...?data,
        'actions': actions.map((action) => action.toJson()).toList(),
        'is_interactive': true,
      };

      await sendSmartNotification(
        userId: userId,
        title: title,
        body: body,
        type: type,
        data: enhancedData,
      );
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار التفاعلي: $e');
      rethrow;
    }
  }

  /// إرسال إشعار مجمع للأنشطة المتشابهة
  Future<void> sendGroupedNotification({
    required String userId,
    required String groupKey,
    required String title,
    required String body,
    required String type,
    required int count,
    Map<String, dynamic>? data,
  }) async {
    try {
      // البحث عن إشعارات مشابهة غير مقروءة
      final existingNotifications = await _authService.client
          .from('notifications')
          .select('id, data')
          .eq('user_id', userId)
          .eq('type', type)
          .eq('is_read', false)
          .order('created_at', ascending: false)
          .limit(5);

      // تحديث الإشعار الموجود أو إنشاء جديد
      if (existingNotifications.isNotEmpty) {
        final existingId = existingNotifications.first['id'];
        await _authService.client
            .from('notifications')
            .update({
              'title': title,
              'body': body,
              'data': {
                ...?data,
                'group_key': groupKey,
                'count': count,
                'updated_at': DateTime.now().toIso8601String(),
              },
            })
            .eq('id', existingId);
      } else {
        await sendSmartNotification(
          userId: userId,
          title: title,
          body: body,
          type: type,
          data: {...?data, 'group_key': groupKey, 'count': count},
        );
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار المجمع: $e');
      rethrow;
    }
  }

  /// إرسال إشعارات جماعية
  Future<void> sendBulkNotifications({
    required List<String> userIds,
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notifications =
          userIds
              .map(
                (userId) => {
                  'user_id': userId,
                  'title': title,
                  'body': body,
                  'type': type,
                  'data': data,
                  'is_read': false,
                  'created_at': DateTime.now().toIso8601String(),
                },
              )
              .toList();

      await _authService.client.from('notifications').insert(notifications);
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعارات الجماعية: $e');
      rethrow;
    }
  }

  /// جدولة إشعار
  Future<void> scheduleNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    required DateTime scheduledAt,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _authService.client.from('scheduled_notifications').insert({
        'user_id': userId,
        'title': title,
        'body': body,
        'type': type,
        'data': data,
        'scheduled_at': scheduledAt.toIso8601String(),
        'is_sent': false,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('خطأ في جدولة الإشعار: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات الإشعارات
  Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return {};

      final response = await _authService.client
          .from('notifications')
          .select('type, is_read')
          .eq('user_id', userId);

      final stats = <String, dynamic>{
        'total': response.length,
        'unread': response.where((n) => !n['is_read']).length,
        'by_type': <String, int>{},
      };

      for (final notification in response) {
        final type = notification['type'] as String;
        stats['by_type'][type] = (stats['by_type'][type] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الإشعارات: $e');
      return {};
    }
  }
}
