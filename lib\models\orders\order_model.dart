import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/models/address_model.dart';
import 'package:motorcycle_parts_shop/models/orders/order_item_model.dart';

class OrderModel {
  final String id;
  final String? orderNumber;
  final String userId;
  final String addressId;
  final double totalAmount;
  final double? taxAmount;
  final double? discountAmount;
  final double? shippingCost;
  final double? subtotal;
  final String status;
  final String? shippingMethodId;
  final String? notes;
  final String? trackingNumber;
  final DateTime? estimatedDelivery;
  final DateTime? deliveredAt;
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<OrderItemModel> items;
  final AddressModel address;

  OrderModel({
    required this.id,
    this.orderNumber,
    required this.userId,
    required this.addressId,
    required this.totalAmount,
    this.taxAmount,
    this.discountAmount,
    this.shippingCost,
    this.subtotal,
    required this.status,
    this.shippingMethodId,
    this.notes,
    this.trackingNumber,
    this.estimatedDelivery,
    this.deliveredAt,
    this.cancelledAt,
    this.cancellationReason,
    required this.createdAt,
    required this.updatedAt,
    required this.items,
    required this.address,
  }) : assert(
         AppConstants.isValidOrderStatus(status),
         'حالة الطلب غير صالحة: $status',
       );

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'] as String,
      orderNumber: json['order_number'] as String?,
      userId: json['user_id'] as String,
      addressId: json['address_id'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      taxAmount: (json['tax_amount'] as num?)?.toDouble(),
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
      shippingCost: (json['shipping_cost'] as num?)?.toDouble(),
      subtotal: (json['subtotal'] as num?)?.toDouble(),
      status: json['status'] as String,
      shippingMethodId: json['shipping_method_id'] as String?,
      notes: json['notes'] as String?,
      trackingNumber: json['tracking_number'] as String?,
      estimatedDelivery:
          json['estimated_delivery'] != null
              ? DateTime.parse(json['estimated_delivery'] as String)
              : null,
      deliveredAt:
          json['delivered_at'] != null
              ? DateTime.parse(json['delivered_at'] as String)
              : null,
      cancelledAt:
          json['cancelled_at'] != null
              ? DateTime.parse(json['cancelled_at'] as String)
              : null,
      cancellationReason: json['cancellation_reason'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      items:
          (json['items'] as List<dynamic>?)
              ?.map(
                (item) => OrderItemModel.fromJson(item as Map<String, dynamic>),
              )
              .toList() ??
          [],
      address:
          json['address'] != null
              ? AddressModel.fromJson(json['address'] as Map<String, dynamic>)
              : throw const FormatException('حقل العنوان مفقود أو غير صالح'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'user_id': userId,
      'address_id': addressId,
      'total_amount': totalAmount,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'shipping_cost': shippingCost,
      'subtotal': subtotal,
      'status': status,
      'shipping_method_id': shippingMethodId,
      'notes': notes,
      'tracking_number': trackingNumber,
      'estimated_delivery': estimatedDelivery?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
      'cancellation_reason': cancellationReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'address': address.toJson(),
    };
  }

  OrderModel copyWith({
    String? id,
    String? orderNumber,
    String? userId,
    String? addressId,
    double? totalAmount,
    double? taxAmount,
    double? discountAmount,
    double? shippingCost,
    double? subtotal,
    String? status,
    String? shippingMethodId,
    String? notes,
    String? trackingNumber,
    DateTime? estimatedDelivery,
    DateTime? deliveredAt,
    DateTime? cancelledAt,
    String? cancellationReason,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<OrderItemModel>? items,
    AddressModel? address,
  }) {
    return OrderModel(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      userId: userId ?? this.userId,
      addressId: addressId ?? this.addressId,
      totalAmount: totalAmount ?? this.totalAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      shippingCost: shippingCost ?? this.shippingCost,
      subtotal: subtotal ?? this.subtotal,
      status: status ?? this.status,
      shippingMethodId: shippingMethodId ?? this.shippingMethodId,
      notes: notes ?? this.notes,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
      address: address ?? this.address,
    );
  }

  // خصائص مساعدة
  bool get isActive => status != AppConstants.orderStatusDelivered;
  bool get canCancel => status == AppConstants.orderStatusPending;
  bool get canTrack =>
      trackingNumber != null &&
      (status == AppConstants.orderStatusShipped ||
          status == AppConstants.orderStatusDelivered);
  bool get isPending => status == AppConstants.orderStatusPending;
  bool get isProcessing => status == AppConstants.orderStatusProcessing;
  bool get isShipped => status == AppConstants.orderStatusShipped;
  bool get isDelivered => status == AppConstants.orderStatusDelivered;
  bool get isCancelled => status == AppConstants.orderStatusCancelled;
  bool get hasShippingMethod => shippingMethodId != null;

  // استخدام الدوال المساعدة من AppConstants
  Color get statusColor => AppConstants.getOrderStatusColor(status);
  String get statusText => AppConstants.getOrderStatusText(status);

  // حساب المبالغ
  double get calculatedSubtotal =>
      subtotal ??
      (totalAmount -
          (taxAmount ?? 0) -
          (shippingCost ?? 0) +
          (discountAmount ?? 0));
  double get calculatedTotal =>
      (subtotal ?? 0) +
      (taxAmount ?? 0) +
      (shippingCost ?? 0) -
      (discountAmount ?? 0);
}
