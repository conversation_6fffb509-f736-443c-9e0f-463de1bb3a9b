import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../monitoring/app_logger.dart';

/// مدير النسخ الاحتياطي المتقدم
/// يدعم النسخ الاحتياطي التلقائي والاستعادة مع التشفير
class BackupManager {
  static final BackupManager _instance = BackupManager._internal();
  factory BackupManager() => _instance;
  BackupManager._internal();

  late final SupabaseClient _supabase;
  late final AppLogger _logger;
  
  bool _isInitialized = false;
  static const String _backupPrefix = 'backup_';
  static const String _encryptionKey = 'motorcycle_parts_backup_key_2024';

  /// تهيئة مدير النسخ الاحتياطي
  Future<void> initialize(SupabaseClient supabase) async {
    if (_isInitialized) return;
    
    _supabase = supabase;
    _logger = AppLogger();
    _isInitialized = true;
    
    await _scheduleAutoBackup();
    _logger.info('تم تهيئة مدير النسخ الاحتياطي');
  }

  /// إنشاء نسخة احتياطية شاملة
  Future<BackupResult> createFullBackup({String? description}) async {
    try {
      _logger.info('بدء إنشاء نسخة احتياطية شاملة');
      
      final backupData = BackupData(
        timestamp: DateTime.now(),
        version: await _getAppVersion(),
        description: description ?? 'نسخة احتياطية تلقائية',
        data: {},
      );

      // جمع البيانات من جداول مختلفة
      final tables = [
        'products', 'categories', 'orders', 'customers', 
        'inventory', 'suppliers', 'user_preferences'
      ];

      for (final table in tables) {
        try {
          final data = await _supabase.from(table).select('*');
          backupData.data[table] = data;
          _logger.info('تم نسخ بيانات $table: ${data.length} سجل');
        } catch (e) {
          _logger.warning('تعذر نسخ بيانات $table: $e');
        }
      }

      // نسخ الإعدادات المحلية
      backupData.data['local_settings'] = await _getLocalSettings();
      
      // تشفير البيانات
      final encryptedData = _encryptData(jsonEncode(backupData.toJson()));
      
      // حفظ محلياً
      final localFile = await _saveBackupLocally(encryptedData, backupData.timestamp);
      
      // رفع إلى السحابة
      final cloudUrl = await _uploadToCloud(encryptedData, backupData.timestamp);
      
      // حفظ معلومات النسخة الاحتياطية
      await _saveBackupInfo(BackupInfo(
        id: _generateBackupId(backupData.timestamp),
        timestamp: backupData.timestamp,
        description: backupData.description,
        localPath: localFile.path,
        cloudUrl: cloudUrl,
        size: encryptedData.length,
        checksum: _calculateChecksum(encryptedData),
      ));

      _logger.info('تم إنشاء النسخة الاحتياطية بنجاح');
      return BackupResult.success('تم إنشاء النسخة الاحتياطية بنجاح');
      
    } catch (e, stackTrace) {
      _logger.error('خطأ في إنشاء النسخة الاحتياطية', error: e, stackTrace: stackTrace);
      return BackupResult.error('فشل في إنشاء النسخة الاحتياطية: $e');
    }
  }

  /// استعادة نسخة احتياطية
  Future<RestoreResult> restoreBackup(String backupId, {bool preview = false}) async {
    try {
      _logger.info('بدء استعادة النسخة الاحتياطية: $backupId');
      
      // الحصول على معلومات النسخة الاحتياطية
      final backupInfo = await _getBackupInfo(backupId);
      if (backupInfo == null) {
        return RestoreResult.error('النسخة الاحتياطية غير موجودة');
      }

      // تحميل البيانات
      String encryptedData;
      try {
        encryptedData = await _loadBackupData(backupInfo);
      } catch (e) {
        return RestoreResult.error('فشل في تحميل البيانات: $e');
      }

      // التحقق من سلامة البيانات
      if (!_verifyChecksum(encryptedData, backupInfo.checksum)) {
        return RestoreResult.error('البيانات تالفة - فشل في التحقق');
      }

      // فك التشفير
      final decryptedData = _decryptData(encryptedData);
      final backupData = BackupData.fromJson(jsonDecode(decryptedData));

      if (preview) {
        return RestoreResult.preview(_generatePreview(backupData));
      }

      // إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
      await createFullBackup(description: 'نسخة احتياطية تلقائية قبل الاستعادة');

      // استعادة البيانات
      final restoredTables = <String>[];
      for (final entry in backupData.data.entries) {
        final tableName = entry.key;
        final tableData = entry.value;

        if (tableName == 'local_settings') {
          await _restoreLocalSettings(tableData as Map<String, dynamic>);
          continue;
        }

        try {
          // حذف البيانات الحالية
          await _supabase.from(tableName).delete().neq('id', '00000000-0000-0000-0000-000000000000');
          
          // إدراج البيانات المستعادة
          if (tableData is List && tableData.isNotEmpty) {
            await _supabase.from(tableName).insert(tableData);
            restoredTables.add(tableName);
            _logger.info('تم استعادة $tableName: ${tableData.length} سجل');
          }
        } catch (e) {
          _logger.warning('تعذر استعادة $tableName: $e');
        }
      }

      _logger.info('تم استعادة النسخة الاحتياطية بنجاح');
      return RestoreResult.success(
        'تم استعادة النسخة الاحتياطية بنجاح\nالجداول المستعادة: ${restoredTables.join(', ')}'
      );
      
    } catch (e, stackTrace) {
      _logger.error('خطأ في استعادة النسخة الاحتياطية', error: e, stackTrace: stackTrace);
      return RestoreResult.error('فشل في استعادة النسخة الاحتياطية: $e');
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية
  Future<List<BackupInfo>> getBackupsList() async {
    try {
      final response = await _supabase
          .from('backup_info')
          .select('*')
          .order('timestamp', ascending: false);
      
      return response.map<BackupInfo>((data) => BackupInfo.fromJson(data)).toList();
    } catch (e) {
      _logger.error('خطأ في جلب قائمة النسخ الاحتياطية', error: e);
      return [];
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(String backupId) async {
    try {
      final backupInfo = await _getBackupInfo(backupId);
      if (backupInfo == null) return false;

      // حذف الملف المحلي
      if (backupInfo.localPath != null) {
        final file = File(backupInfo.localPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // حذف من السحابة
      if (backupInfo.cloudUrl != null) {
        await _deleteFromCloud(backupInfo.cloudUrl!);
      }

      // حذف من قاعدة البيانات
      await _supabase.from('backup_info').delete().eq('id', backupId);
      
      _logger.info('تم حذف النسخة الاحتياطية: $backupId');
      return true;
    } catch (e) {
      _logger.error('خطأ في حذف النسخة الاحتياطية', error: e);
      return false;
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  Future<void> cleanupOldBackups({int keepCount = 10}) async {
    try {
      final backups = await getBackupsList();
      if (backups.length <= keepCount) return;

      final backupsToDelete = backups.skip(keepCount).toList();
      for (final backup in backupsToDelete) {
        await deleteBackup(backup.id);
      }

      _logger.info('تم تنظيف ${backupsToDelete.length} نسخة احتياطية قديمة');
    } catch (e) {
      _logger.error('خطأ في تنظيف النسخ الاحتياطية القديمة', error: e);
    }
  }

  /// جدولة النسخ الاحتياطي التلقائي
  Future<void> _scheduleAutoBackup() async {
    // هنا يمكن تنفيذ جدولة النسخ الاحتياطي باستخدام WorkManager أو مهام مجدولة
    _logger.info('تم جدولة النسخ الاحتياطي التلقائي');
  }

  /// جدولة نسخة احتياطية
  Future<bool> scheduleBackup({
    required String cronExpression,
    String? backupName,
  }) async {
    try {
      _logger.info('جدولة نسخة احتياطية: $cronExpression');
      
      // حفظ إعدادات الجدولة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('backup_schedule_cron', cronExpression);
      if (backupName != null) {
        await prefs.setString('backup_schedule_name', backupName);
      }
      
      // هنا يمكن تنفيذ الجدولة الفعلية باستخدام WorkManager
      // await Workmanager().registerPeriodicTask(
      //   'backup_task',
      //   'backupTask',
      //   frequency: Duration(hours: 24),
      //   constraints: Constraints(
      //     networkType: NetworkType.connected,
      //   ),
      // );
      
      _logger.info('تم جدولة النسخ الاحتياطي بنجاح');
      return true;
    } catch (e) {
      _logger.error('خطأ في جدولة النسخ الاحتياطي', error: e);
      return false;
    }
  }

  /// تشفير البيانات
  String _encryptData(String data) {
    // تنفيذ بسيط للتشفير - يمكن تحسينه باستخدام مكتبات تشفير متقدمة
    final bytes = utf8.encode(data + _encryptionKey);
    final digest = sha256.convert(bytes);
    final encrypted = base64.encode(utf8.encode(data));
    return '$digest:$encrypted';
  }

  /// فك تشفير البيانات
  String _decryptData(String encryptedData) {
    final parts = encryptedData.split(':');
    if (parts.length != 2) throw Exception('صيغة البيانات المشفرة غير صحيحة');
    
    final originalDigest = parts[0];
    final encrypted = parts[1];
    final decrypted = utf8.decode(base64.decode(encrypted));
    
    // التحقق من سلامة البيانات
    final bytes = utf8.encode(decrypted + _encryptionKey);
    final calculatedDigest = sha256.convert(bytes).toString();
    
    if (originalDigest != calculatedDigest) {
      throw Exception('فشل في التحقق من سلامة البيانات');
    }
    
    return decrypted;
  }

  /// حساب المجموع الاختباري
  String _calculateChecksum(String data) {
    return sha256.convert(utf8.encode(data)).toString();
  }

  /// التحقق من المجموع الاختباري
  bool _verifyChecksum(String data, String expectedChecksum) {
    return _calculateChecksum(data) == expectedChecksum;
  }

  /// حفظ النسخة الاحتياطية محلياً
  Future<File> _saveBackupLocally(String data, DateTime timestamp) async {
    final directory = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${directory.path}/backups');
    
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    final fileName = '$_backupPrefix${timestamp.millisecondsSinceEpoch}.bak';
    final file = File('${backupDir.path}/$fileName');
    
    await file.writeAsString(data);
    return file;
  }

  /// رفع إلى السحابة
  Future<String?> _uploadToCloud(String data, DateTime timestamp) async {
    try {
      final fileName = '$_backupPrefix${timestamp.millisecondsSinceEpoch}.bak';
      // هنا يمكن تنفيذ الرفع إلى خدمة تخزين سحابية
      // مثل Supabase Storage أو Firebase Storage
      return 'cloud://backups/$fileName';
    } catch (e) {
      _logger.warning('تعذر الرفع إلى السحابة: $e');
      return null;
    }
  }

  /// حذف من السحابة
  Future<void> _deleteFromCloud(String cloudUrl) async {
    try {
      // تنفيذ حذف الملف من السحابة
      _logger.info('تم حذف الملف من السحابة: $cloudUrl');
    } catch (e) {
      _logger.warning('تعذر حذف الملف من السحابة: $e');
    }
  }

  /// إنشاء معرف فريد للنسخة الاحتياطية
  String _generateBackupId(DateTime timestamp) {
    return 'backup_${timestamp.millisecondsSinceEpoch}';
  }

  /// الحصول على إصدار التطبيق
  Future<String> _getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return '${packageInfo.version}+${packageInfo.buildNumber}';
    } catch (e) {
      return 'unknown';
    }
  }

  /// الحصول على الإعدادات المحلية
  Future<Map<String, dynamic>> _getLocalSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final settings = <String, dynamic>{};
      
      for (final key in keys) {
        final value = prefs.get(key);
        settings[key] = value;
      }
      
      return settings;
    } catch (e) {
      _logger.warning('تعذر الحصول على الإعدادات المحلية: $e');
      return {};
    }
  }

  /// استعادة الإعدادات المحلية
  Future<void> _restoreLocalSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      for (final entry in settings.entries) {
        final key = entry.key;
        final value = entry.value;
        
        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is List<String>) {
          await prefs.setStringList(key, value);
        }
      }
    } catch (e) {
      _logger.warning('تعذر استعادة الإعدادات المحلية: $e');
    }
  }

  /// حفظ معلومات النسخة الاحتياطية
  Future<void> _saveBackupInfo(BackupInfo info) async {
    await _supabase.from('backup_info').insert(info.toJson());
  }

  /// الحصول على معلومات النسخة الاحتياطية
  Future<BackupInfo?> _getBackupInfo(String id) async {
    try {
      final response = await _supabase
          .from('backup_info')
          .select('*')
          .eq('id', id)
          .single();
      
      return BackupInfo.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  /// تحميل بيانات النسخة الاحتياطية
  Future<String> _loadBackupData(BackupInfo info) async {
    // محاولة التحميل من المحلي أولاً
    if (info.localPath != null) {
      final file = File(info.localPath!);
      if (await file.exists()) {
        return await file.readAsString();
      }
    }

    // التحميل من السحابة
    if (info.cloudUrl != null) {
      // تنفيذ تحميل من السحابة
      throw Exception('التحميل من السحابة غير متاح حالياً');
    }

    throw Exception('لا يمكن العثور على بيانات النسخة الاحتياطية');
  }

  /// إنشاء معاينة للنسخة الاحتياطية
  String _generatePreview(BackupData backupData) {
    final buffer = StringBuffer();
    buffer.writeln('معاينة النسخة الاحتياطية:');
    buffer.writeln('التاريخ: ${backupData.timestamp}');
    buffer.writeln('الإصدار: ${backupData.version}');
    buffer.writeln('الوصف: ${backupData.description}');
    buffer.writeln();
    buffer.writeln('البيانات المضمنة:');
    
    for (final entry in backupData.data.entries) {
      final tableName = entry.key;
      final data = entry.value;
      
      if (data is List) {
        buffer.writeln('- $tableName: ${data.length} سجل');
      } else if (data is Map) {
        buffer.writeln('- $tableName: ${data.length} عنصر');
      }
    }
    
    return buffer.toString();
  }
}

/// نتيجة عملية النسخ الاحتياطي
class BackupResult {
  final bool success;
  final String message;

  BackupResult._(this.success, this.message);
  
  factory BackupResult.success(String message) => BackupResult._(true, message);
  factory BackupResult.error(String message) => BackupResult._(false, message);
}

/// نتيجة عملية الاستعادة
class RestoreResult {
  final bool success;
  final String message;
  final bool isPreview;

  RestoreResult._(this.success, this.message, this.isPreview);
  
  factory RestoreResult.success(String message) => RestoreResult._(true, message, false);
  factory RestoreResult.error(String message) => RestoreResult._(false, message, false);
  factory RestoreResult.preview(String message) => RestoreResult._(true, message, true);
}

/// بيانات النسخة الاحتياطية
class BackupData {
  final DateTime timestamp;
  final String version;
  final String description;
  final Map<String, dynamic> data;

  BackupData({
    required this.timestamp,
    required this.version,
    required this.description,
    required this.data,
  });

  Map<String, dynamic> toJson() => {
    'timestamp': timestamp.toIso8601String(),
    'version': version,
    'description': description,
    'data': data,
  };

  factory BackupData.fromJson(Map<String, dynamic> json) => BackupData(
    timestamp: DateTime.parse(json['timestamp']),
    version: json['version'],
    description: json['description'],
    data: json['data'],
  );
}

/// معلومات النسخة الاحتياطية
class BackupInfo {
  final String id;
  final DateTime timestamp;
  final String description;
  final String? localPath;
  final String? cloudUrl;
  final int size;
  final String checksum;

  BackupInfo({
    required this.id,
    required this.timestamp,
    required this.description,
    this.localPath,
    this.cloudUrl,
    required this.size,
    required this.checksum,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'timestamp': timestamp.toIso8601String(),
    'description': description,
    'local_path': localPath,
    'cloud_url': cloudUrl,
    'size': size,
    'checksum': checksum,
  };

  factory BackupInfo.fromJson(Map<String, dynamic> json) => BackupInfo(
    id: json['id'],
    timestamp: DateTime.parse(json['timestamp']),
    description: json['description'],
    localPath: json['local_path'],
    cloudUrl: json['cloud_url'],
    size: json['size'],
    checksum: json['checksum'],
  );
}
