import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../models/products/product_model.dart';

/// خدمة السلة المحسنة مع إدارة أفضل للحالة والأداء
class OptimizedCartService extends ChangeNotifier {
  final SupabaseClient _client;
  final Map<String, CartServiceItem> _items = {};
  bool _isLoading = false;
  Timer? _syncTimer;
  String? _currentUserId;

  OptimizedCartService(this._client);

  // Getters
  List<CartServiceItem> get items => _items.values.toList();
  int get itemCount => _items.length;
  bool get isLoading => _isLoading;
  bool get isEmpty => _items.isEmpty;
  bool get isNotEmpty => _items.isNotEmpty;

  /// إجمالي قيمة السلة
  double get totalAmount {
    return _items.values.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  /// إجمالي عدد القطع
  int get totalQuantity {
    return _items.values.fold(0, (sum, item) => sum + item.quantity);
  }

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      _currentUserId = _client.auth.currentUser?.id;
      if (_currentUserId != null) {
        await _loadCartFromServer();
      }
      debugPrint('✅ تم تهيئة خدمة السلة المحسنة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة السلة: $e');
    }
  }

  /// إضافة منتج للسلة مع تحسين الأداء
  Future<void> addItem(ProductModel product, {int quantity = 1}) async {
    // التحقق من صحة المدخلات
    if (quantity <= 0) {
      throw ArgumentError('الكمية يجب أن تكون أكبر من صفر');
    }

    if (product.stockQuantity < quantity) {
      throw InsufficientStockException('الكمية المطلوبة غير متوفرة في المخزون');
    }

    final existingItem = _items[product.id];

    if (existingItem != null) {
      // تحديث الكمية للمنتج الموجود
      final newQuantity = existingItem.quantity + quantity;
      if (newQuantity > product.stockQuantity) {
        throw InsufficientStockException('تجاوز الكمية المتاحة في المخزون');
      }

      existingItem.quantity = newQuantity;
      existingItem.updatedAt = DateTime.now();
    } else {
      // إضافة منتج جديد
      _items[product.id] = CartServiceItem(
        id: _generateItemId(),
        product: product,
        quantity: quantity,
        unitPrice: product.price,
        addedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    // تحديث الواجهة فوراً
    notifyListeners();

    // مزامنة مع الخادم في الخلفية
    _scheduleSyncWithServer();

    debugPrint('✅ تم إضافة ${product.name} للسلة');
  }

  /// تحديث كمية منتج في السلة
  Future<void> updateQuantity(String productId, int newQuantity) async {
    if (newQuantity <= 0) {
      await removeItem(productId);
      return;
    }

    final item = _items[productId];
    if (item == null) {
      throw ArgumentError('المنتج غير موجود في السلة');
    }

    if (newQuantity > item.product.stockQuantity) {
      throw InsufficientStockException('الكمية المطلوبة غير متوفرة في المخزون');
    }

    item.quantity = newQuantity;
    item.updatedAt = DateTime.now();

    notifyListeners();
    _scheduleSyncWithServer();

    debugPrint('✅ تم تحديث كمية ${item.product.name} إلى $newQuantity');
  }

  /// إزالة منتج من السلة
  Future<void> removeItem(String productId) async {
    final item = _items.remove(productId);
    if (item != null) {
      notifyListeners();
      _scheduleSyncWithServer();
      debugPrint('✅ تم إزالة ${item.product.name} من السلة');
    }
  }

  /// مسح السلة بالكامل
  Future<void> clearCart() async {
    _items.clear();
    notifyListeners();
    _scheduleSyncWithServer();
    debugPrint('🗑️ تم مسح السلة بالكامل');
  }

  /// التحقق من وجود منتج في السلة
  bool containsProduct(String productId) {
    return _items.containsKey(productId);
  }

  /// الحصول على كمية منتج في السلة
  int getProductQuantity(String productId) {
    return _items[productId]?.quantity ?? 0;
  }

  /// تحميل السلة من الخادم
  Future<void> _loadCartFromServer() async {
    if (_currentUserId == null) return;

    try {
      _isLoading = true;
      notifyListeners();

      final response = await _client
          .from('cart_items')
          .select('''
            *,
            products(*)
          ''')
          .eq('user_id', _currentUserId!);

      _items.clear();
      for (final item in response) {
        final product = ProductModel.fromJson(item['products']);
        _items[product.id] = CartServiceItem(
          id: item['id'],
          product: product,
          quantity: item['quantity'],
          unitPrice: item['unit_price']?.toDouble() ?? product.price,
          addedAt: DateTime.parse(item['created_at']),
          updatedAt: DateTime.parse(item['updated_at']),
        );
      }

      debugPrint('✅ تم تحميل ${_items.length} عنصر من السلة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل السلة: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// مزامنة مع الخادم بشكل دوري
  void _scheduleSyncWithServer() {
    _syncTimer?.cancel();
    _syncTimer = Timer(const Duration(seconds: 2), () {
      _syncWithServer();
    });
  }

  /// مزامنة السلة مع الخادم
  Future<void> _syncWithServer() async {
    if (_currentUserId == null) return;

    try {
      // حذف العناصر القديمة
      await _client.from('cart_items').delete().eq('user_id', _currentUserId!);

      // إضافة العناصر الحالية
      if (_items.isNotEmpty) {
        final itemsToInsert =
            _items.values
                .map(
                  (item) => {
                    'id': item.id,
                    'user_id': _currentUserId,
                    'product_id': item.product.id,
                    'quantity': item.quantity,
                    'unit_price': item.unitPrice,
                    'created_at': item.addedAt.toIso8601String(),
                    'updated_at': item.updatedAt.toIso8601String(),
                  },
                )
                .toList();

        await _client.from('cart_items').insert(itemsToInsert);
      }

      debugPrint('✅ تم مزامنة السلة مع الخادم');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة السلة: $e');
    }
  }

  /// إنشاء معرف فريد للعنصر
  String _generateItemId() {
    return 'cart_item_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// تحديث معرف المستخدم الحالي
  void updateCurrentUser(String? userId) {
    if (_currentUserId != userId) {
      _currentUserId = userId;
      _items.clear();
      if (userId != null) {
        _loadCartFromServer();
      }
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _syncTimer?.cancel();
    super.dispose();
  }
}

/// عنصر في السلة
class CartServiceItem {
  final String id;
  final ProductModel product;
  int quantity;
  final double unitPrice;
  final DateTime addedAt;
  DateTime updatedAt;

  CartServiceItem({
    required this.id,
    required this.product,
    required this.quantity,
    required this.unitPrice,
    required this.addedAt,
    required this.updatedAt,
  });

  /// إجمالي سعر العنصر
  double get totalPrice => quantity * unitPrice;

  /// نسبة الخصم إن وجدت
  double get discountPercentage {
    if (product.originalPrice != null && product.originalPrice! > unitPrice) {
      return ((product.originalPrice! - unitPrice) / product.originalPrice!) *
          100;
    }
    return 0.0;
  }

  /// مبلغ الوفر
  double get savedAmount {
    if (product.originalPrice != null && product.originalPrice! > unitPrice) {
      return (product.originalPrice! - unitPrice) * quantity;
    }
    return 0.0;
  }
}

/// استثناء نقص المخزون
class InsufficientStockException implements Exception {
  final String message;
  InsufficientStockException(this.message);

  @override
  String toString() => 'InsufficientStockException: $message';
}
