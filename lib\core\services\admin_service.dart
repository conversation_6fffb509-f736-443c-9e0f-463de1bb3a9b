import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../models/user/user_model.dart';
import '../constants/admin_constants.dart';

/// خدمة إدارة المسؤولين في التطبيق
class AdminService extends ChangeNotifier {
  final SupabaseClient _client;
  String? _error;
  bool _isLoading = false;

  /// الحصول على حالة التحميل
  bool get isLoading => _isLoading;

  /// الحصول على رسالة الخطأ الأخيرة
  String? get error => _error;

  /// إنشاء نسخة من خدمة إدارة المسؤولين
  AdminService({required SupabaseClient client}) : _client = client;

  /// التحقق مما إذا كان المستخدم هو المسؤول الرئيسي
  Future<bool> isMainAdmin(String email) async {
    return await AdminConstants.isMainAdmin(email);
  }

  /// التحقق مما إذا كان المستخدم مسؤولاً
  Future<bool> isUserAdmin(String userId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .select('profile_type')
              .eq('id', userId)
              .single();

      return response['profile_type'] == 'admin';
    } catch (e) {
      _error = 'فشل التحقق من صلاحيات المستخدم: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// ترقية مستخدم إلى مسؤول (يتطلب أن يكون المستخدم الحالي مسؤولاً)
  Future<bool> upgradeUserToAdmin(String userId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // التحقق من أن المستخدم الحالي هو مسؤول
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final isAdmin = await isUserAdmin(currentUser.id);
      if (!isAdmin) {
        throw Exception('ليس لديك صلاحيات كافية لترقية المستخدمين');
      }

      // تحديث نوع الملف الشخصي للمستخدم المحدد إلى مسؤول
      await _client
          .from(AppConstants.profilesTable)
          .update({'profile_type': AppConstants.profileTypeAdmin})
          .eq('id', userId);

      // تسجيل عملية الترقية في سجل المسؤولين
      await _client.from(AppConstants.adminLogsTable).insert({
        'admin_id': currentUser.id,
        'action': 'user_promotion',
        'target_user_id': userId,
        'details': {
          'timestamp': DateTime.now().toIso8601String(),
          'promoted_by': currentUser.id,
        },
      });

      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في ترقية المستخدم: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// ترقية مستخدم إلى مسؤول باستخدام service_role (للاستخدام في الخادم فقط)
  /// يجب استخدام هذه الوظيفة فقط في بيئة آمنة مثل Edge Functions
  Future<bool> secureUpgradeUserToAdmin(
    String adminId,
    String targetUserId,
  ) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // الحصول على مفتاح service_role من متغيرات البيئة
      final serviceRoleKey = dotenv.env['SUPABASE_SERVICE_ROLE_KEY'];
      if (serviceRoleKey == null || serviceRoleKey.isEmpty) {
        throw Exception('مفتاح service_role غير متوفر');
      }

      // إنشاء عميل Supabase جديد باستخدام مفتاح service_role
      final adminClient = SupabaseClient(
        dotenv.env['SUPABASE_URL'] ?? '',
        serviceRoleKey,
      );

      // التحقق من أن المستخدم الذي يقوم بالترقية هو مسؤول
      final adminData =
          await adminClient
              .from(AppConstants.profilesTable)
              .select('profile_type, email')
              .eq('id', adminId)
              .single();

      final isAdmin = adminData['profile_type'] == 'admin';
      if (!isAdmin) {
        throw Exception('ليس لديك صلاحيات كافية لترقية المستخدمين');
      }

      // تحديث نوع الملف الشخصي للمستخدم المستهدف إلى مسؤول
      await adminClient
          .from(AppConstants.profilesTable)
          .update({'profile_type': AppConstants.profileTypeAdmin})
          .eq('id', targetUserId);

      // تسجيل عملية الترقية في سجل المسؤولين
      await adminClient.from(AppConstants.adminLogsTable).insert({
        'admin_id': adminId,
        'action': 'secure_user_promotion',
        'target_user_id': targetUserId,
        'details': {
          'timestamp': DateTime.now().toIso8601String(),
          'promoted_by': adminId,
          'method': 'service_role',
        },
      });

      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في ترقية المستخدم بشكل آمن: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// الحصول على قائمة المسؤولين
  Future<List<UserModel>> getAdminsList() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _client
          .from(AppConstants.profilesTable)
          .select('*')
          .eq('profile_type', AppConstants.profileTypeAdmin);

      return response.map((json) => UserModel.fromJson(json)).toList();
    } catch (e) {
      _error = 'فشل في جلب قائمة المسؤولين: $e';
      debugPrint(_error);
      return [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحديث البريد الإلكتروني للمسؤول الرئيسي (يتطلب أن يكون المستخدم الحالي هو المسؤول الرئيسي)
  Future<bool> updateMainAdminEmail(String newEmail) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // التحقق من أن المستخدم الحالي هو المسؤول الرئيسي
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      if (!(await isMainAdmin(currentUser.email ?? ''))) {
        throw Exception(
          'فقط المسؤول الرئيسي يمكنه تحديث البريد الإلكتروني للمسؤول الرئيسي',
        );
      }

      // تحديث البريد الإلكتروني للمسؤول الرئيسي في قاعدة البيانات
      await _client
          .from(AppConstants.adminSettingsTable)
          .update({'setting_value': newEmail})
          .eq('setting_key', 'main_admin_email');

      // تسجيل عملية التحديث في سجل المسؤولين
      await _client.from(AppConstants.adminLogsTable).insert({
        'admin_id': currentUser.id,
        'action': 'update_main_admin_email',
        'details': {
          'old_email': currentUser.email,
          'new_email': newEmail,
          'timestamp': DateTime.now().toIso8601String(),
        },
      });

      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في تحديث البريد الإلكتروني للمسؤول الرئيسي: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
