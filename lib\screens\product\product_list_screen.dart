import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/advanced_search_service.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';

class ProductListScreen extends StatefulWidget {
  final String? categoryId;
  final String? categoryName;
  final String? company;
  final String? searchQuery;
  final String? title;
  final List<ProductModel>? products;
  final Map<String, dynamic>? filter;
  final Map<String, dynamic>? searchCriteria;

  const ProductListScreen({
    super.key,
    this.categoryId,
    this.categoryName,
    this.company,
    this.searchQuery,
    this.title,
    this.products,
    this.filter,
    this.searchCriteria,
  });

  @override
  State<ProductListScreen> createState() => _ProductListScreenState();
}

class _ProductListScreenState extends State<ProductListScreen> {
  final AuthSupabaseService _supabaseService = AuthSupabaseService();
  late final ProductService _productService;
  final ScrollController _scrollController = ScrollController();

  List<ProductModel> _products = [];
  List<String> _companies = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreProducts = true;
  int _currentPage = 0;
  static const int _pageSize = 24;
  String? _error;

  String _selectedCompany = '';
  String _sortBy = 'name';
  bool _sortAscending = true;
  bool _isGridView = true;
  RangeValues _priceRange = const RangeValues(0, 1000000);

  bool _onlyAvailable = false;
  bool _onlyDiscounted = false;

  @override
  void initState() {
    super.initState();
    _selectedCompany = widget.company ?? '';
    _productService = ProductService(_supabaseService.client);
    _setupScrollListener();
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        if (!_isLoadingMore && _hasMoreProducts) {
          _loadMoreProducts();
        }
      }
    });
  }

  Future<void> _loadMoreProducts() async {
    if (_isLoadingMore || !_hasMoreProducts) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final moreProducts = await _supabaseService.getAllProducts(
        limit: _pageSize,
        offset: _currentPage * _pageSize,
        searchQuery: widget.searchQuery,
        categoryId: widget.categoryId,
        sortBy: _sortBy,
        ascending: _sortAscending,
      );

      setState(() {
        if (moreProducts.isNotEmpty) {
          _products.addAll(moreProducts);
          _currentPage++;
          _hasMoreProducts = moreProducts.length >= _pageSize;
        } else {
          _hasMoreProducts = false;
        }
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      debugPrint('خطأ في تحميل المزيد من المنتجات: $e');
    }
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child:
          _isLoadingMore
              ? const CircularProgressIndicator()
              : const SizedBox.shrink(),
    );
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      if (!_supabaseService.isInitialized) {
        await _supabaseService.initialize();
      }

      await _loadProductsFromServer();
      final companiesList = await _productService.getAvailableCompanies();
      _companies =
          companiesList.map((company) => company['name'] as String).toList();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadProductsFromServer() async {
    try {
      List<ProductModel> products = [];

      if (widget.products?.isNotEmpty ?? false) {
        setState(() {
          _products = widget.products!;
          _isLoading = false;
        });
        return;
      }

      if (widget.searchQuery?.isNotEmpty ?? false) {
        final advancedSearch = AdvancedSearchService();
        final searchResults = await advancedSearch.searchProducts(
          searchQuery: widget.searchQuery!,
        );
        products = searchResults;
        setState(() {
          _products = products;
          _isLoading = false;
        });
        return;
      }

      if (widget.categoryId != null && _selectedCompany.isNotEmpty) {
        products = await _productService.getSortedProducts(
          sortBy: _sortBy,
          ascending: _sortAscending,
          category: widget.categoryId,
        );
      } else if (widget.categoryId != null) {
        products = await _supabaseService.getProductsByCategory(
          widget.categoryId!,
        );
      } else if (_selectedCompany.isNotEmpty) {
        products = await _productService.getProductsByCompany(
          companyId: _selectedCompany,
        );
      } else {
        products = await _supabaseService.getAllProducts(
          limit: _pageSize,
          offset: 0,
          sortBy: _sortBy,
          ascending: _sortAscending,
        );
      }

      setState(() {
        _products = products;
        _currentPage = 1;
        _hasMoreProducts = products.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _applyFilters() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final products = await _productService.getSortedProducts(
        sortBy: _sortBy,
        ascending: _sortAscending,
        category: widget.categoryId,
      );

      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // العنوان
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.title ??
                              (widget.searchQuery != null
                                  ? 'نتائج البحث'
                                  : widget.categoryName ??
                                      widget.company ??
                                      'جميع المنتجات'),
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (widget.searchQuery != null)
                          Text(
                            '"${widget.searchQuery}"',
                            style: AppTheme.cardSubtitle.copyWith(
                              color: AppTheme.textLightColor.withOpacity(0.8),
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      _buildHeaderButton(
                        icon:
                            _isGridView
                                ? Icons.view_list_rounded
                                : Icons.grid_view_rounded,
                        onPressed: () {
                          setState(() {
                            _isGridView = !_isGridView;
                          });
                        },
                        tooltip: _isGridView ? 'عرض قائمة' : 'عرض شبكة',
                      ),
                      const SizedBox(width: 8),
                      _buildHeaderButton(
                        icon: Icons.tune_rounded,
                        onPressed: _showFilterDialog,
                        tooltip: 'خيارات التصفية',
                        showBadge: _hasActiveFilters(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'حدث خطأ أثناء تحميل البيانات',
                      style: TextStyle(color: AppTheme.errorColor),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadData,
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              )
              : _products.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.search_off, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    const Text(
                      'لا توجد منتجات متاحة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'حاول تغيير خيارات التصفية',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedCompany = widget.company ?? '';
                          _sortBy = 'name';
                          _sortAscending = true;
                          _priceRange = const RangeValues(0, 1000000);
                          // تم حذف _minRating حسب المتطلبات
                          _onlyAvailable = false;
                          _onlyDiscounted = false;
                        });
                        _loadData();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة تعيين التصفية'),
                    ),
                  ],
                ),
              )
              : Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: ResponsiveHelper.getPadding(context),
                      vertical: 8,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'عدد النتائج: ${_products.length}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        if (_selectedCompany.isNotEmpty ||
                            // تم حذف _minRating حسب المتطلبات
                            _onlyAvailable ||
                            _onlyDiscounted ||
                            _priceRange.start > 0 ||
                            _priceRange.end < 1000000)
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                _selectedCompany = widget.company ?? '';
                                _sortBy = 'name';
                                _sortAscending = true;
                                _priceRange = const RangeValues(0, 1000000);
                                // تم حذف _minRating حسب المتطلبات
                                _onlyAvailable = false;
                                _onlyDiscounted = false;
                              });
                              _loadData();
                            },
                            icon: const Icon(Icons.filter_list_off, size: 16),
                            label: const Text('إلغاء التصفية'),
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadData,
                      child: ResponsiveBuilder(
                        builder: (context, constraints) {
                          final crossAxisCount =
                              ResponsiveHelper.getGridColumns(context);
                          final padding = ResponsiveHelper.getPadding(context);

                          return _isGridView
                              ? GridView.builder(
                                controller: _scrollController,
                                padding: EdgeInsets.all(padding),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: crossAxisCount,
                                      childAspectRatio:
                                          ResponsiveHelper.isMobile(context)
                                              ? 0.7
                                              : 0.8,
                                      crossAxisSpacing:
                                          ResponsiveHelper.isMobile(context)
                                              ? 12
                                              : 16,
                                      mainAxisSpacing:
                                          ResponsiveHelper.isMobile(context)
                                              ? 12
                                              : 16,
                                    ),
                                itemCount:
                                    _products.length +
                                    (_hasMoreProducts ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == _products.length) {
                                    return _buildLoadingIndicator();
                                  }
                                  final product = _products[index];
                                  return _buildProductCard(product);
                                },
                              )
                              : ListView.builder(
                                controller: _scrollController,
                                padding: EdgeInsets.all(padding),
                                itemCount:
                                    _products.length +
                                    (_hasMoreProducts ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == _products.length) {
                                    return _buildLoadingIndicator();
                                  }
                                  final product = _products[index];
                                  return _buildProductListItem(product);
                                },
                              );
                        },
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildProductListItem(ProductModel product) {
    final hasDiscount = product.discountPrice != null;
    final displayPrice = hasDiscount ? product.discountPrice! : product.price;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(context, '/product_details', arguments: product);
        },
        borderRadius: BorderRadius.circular(12),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.horizontal(
                right: Radius.circular(12),
              ),
              child: SizedBox(
                width: 120,
                height: 120,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Image.asset(
                      product.imageUrls.isNotEmpty
                          ? product.imageUrls.first
                          : 'assets/images/product1.png',
                      fit: BoxFit.cover,
                    ),
                    if (hasDiscount)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.errorColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${(((product.price - product.discountPrice!) / product.price) * 100).toInt()}% خصم',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.companyId,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          '$displayPrice د.ع',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                            fontSize: 16,
                          ),
                        ),
                        if (hasDiscount)
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Text(
                              '${product.price} د.ع',
                              style: TextStyle(
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Spacer(),
                        IconButton(
                          icon: const Icon(
                            Icons.add_shopping_cart,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'تمت إضافة ${product.name} إلى سلة التسوق',
                                ),
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          },
                          tooltip: 'إضافة إلى السلة',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductCard(ProductModel product) {
    final hasDiscount = product.discountPrice != null;
    final displayPrice = hasDiscount ? product.discountPrice! : product.price;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(context, '/product_details', arguments: product);
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Image.asset(
                      product.imageUrls.isNotEmpty
                          ? product.imageUrls.first
                          : 'assets/images/product1.png',
                      fit: BoxFit.cover,
                    ),
                    if (hasDiscount)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.errorColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${(((product.price - product.discountPrice!) / product.price) * 100).toInt()}% خصم',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      product.companyId,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        Text(
                          '$displayPrice د.ع',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        if (hasDiscount)
                          Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Text(
                              '${product.price} د.ع',
                              style: TextStyle(
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('فلترة وترتيب'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الشركة المصنعة:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedCompany.isEmpty ? null : _selectedCompany,
                      hint: const Text('جميع الشركات'),
                      isExpanded: true,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      items: [
                        const DropdownMenuItem<String>(
                          value: '',
                          child: Text('جميع الشركات'),
                        ),
                        ..._companies.map(
                          (company) => DropdownMenuItem<String>(
                            value: company,
                            child: Text(company),
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedCompany = value ?? '';
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'ترتيب حسب:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _sortBy,
                      isExpanded: true,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      items: const [
                        DropdownMenuItem<String>(
                          value: 'name',
                          child: Text('الاسم'),
                        ),
                        DropdownMenuItem<String>(
                          value: 'price',
                          child: Text('السعر: من الأقل إلى الأعلى'),
                        ),
                        DropdownMenuItem<String>(
                          value: 'price',
                          child: Text('السعر: من الأعلى إلى الأقل'),
                        ),

                        DropdownMenuItem<String>(
                          value: 'newest',
                          child: Text('الأحدث'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _sortBy = value ?? 'name';
                          if (value == 'price' && _sortBy == 'price') {
                            _sortAscending = !_sortAscending;
                          } else {
                            _sortAscending = true;
                          }
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters();
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 🎨 دوال بناء العناصر المتطورة
  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    bool showBadge = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: Stack(
        children: [
          IconButton(
            icon: Icon(icon, color: AppTheme.textLightColor, size: 22),
            onPressed: onPressed,
            tooltip: tooltip,
            splashRadius: 20,
            padding: const EdgeInsets.all(8),
          ),
          if (showBadge)
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.textLightColor, width: 1),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // دالة للتحقق من وجود فلاتر نشطة
  bool _hasActiveFilters() {
    return _selectedCompany.isNotEmpty ||
        // تم حذف _minRating حسب المتطلبات
        _onlyAvailable ||
        _onlyDiscounted ||
        _priceRange.start > 0 ||
        _priceRange.end < 1000000;
  }
}
