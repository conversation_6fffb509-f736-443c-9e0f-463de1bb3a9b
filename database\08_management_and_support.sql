-- ===================================================================
-- جداول الإدارة والدعم الفني
-- ===================================================================

-- 1. جدول تفاعلات الدعم الفني
-- ===================================================================
CREATE TABLE IF NOT EXISTS support_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('chat', 'email', 'phone', 'ticket')),
    subject VARCHAR(200),
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    assigned_to UUID REFERENCES profiles(id) ON DELETE SET NULL,
    resolution TEXT,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_support_interactions_user_id ON support_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_support_interactions_status ON support_interactions(status);
CREATE INDEX IF NOT EXISTS idx_support_interactions_priority ON support_interactions(priority);
CREATE INDEX IF NOT EXISTS idx_support_interactions_assigned_to ON support_interactions(assigned_to);

-- ===================================================================
-- 2. جدول سجلات النسخ الاحتياطي
-- ===================================================================
CREATE TABLE IF NOT EXISTS backup_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    backup_type VARCHAR(20) NOT NULL CHECK (backup_type IN ('full', 'incremental', 'differential')),
    table_name VARCHAR(100),
    file_path TEXT,
    file_size_bytes BIGINT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    error_message TEXT,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_backup_logs_backup_type ON backup_logs(backup_type);
CREATE INDEX IF NOT EXISTS idx_backup_logs_status ON backup_logs(status);
CREATE INDEX IF NOT EXISTS idx_backup_logs_started_at ON backup_logs(started_at);

-- ===================================================================
-- الدوال الإدارية المساعدة
-- ===================================================================

-- دالة إنشاء تذكرة دعم فني
CREATE OR REPLACE FUNCTION create_support_ticket(
    p_user_id UUID,
    p_subject VARCHAR,
    p_message TEXT,
    p_priority VARCHAR DEFAULT 'medium'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_ticket_id UUID;
BEGIN
    -- إنشاء تذكرة الدعم
    INSERT INTO support_interactions (
        user_id, interaction_type, subject, message, priority
    ) VALUES (
        p_user_id, 'ticket', p_subject, p_message, p_priority
    ) RETURNING id INTO v_ticket_id;

    -- إرسال إشعار للمشرفين
    INSERT INTO notifications (user_id, title, body, type, data)
    SELECT
        id,
        'تذكرة دعم جديدة',
        'تم إنشاء تذكرة دعم جديدة من ' || (SELECT name FROM profiles WHERE id = p_user_id),
        'support',
        jsonb_build_object('ticket_id', v_ticket_id, 'priority', p_priority)
    FROM profiles
    WHERE profile_type = 'admin';

    RETURN v_ticket_id;
END;
$$;
-- ===================================================================

-- دالة تحديث حالة تذكرة الدعم
CREATE OR REPLACE FUNCTION update_support_ticket_status(
    p_ticket_id UUID,
    p_status VARCHAR,
    p_resolution TEXT DEFAULT NULL,
    p_assigned_to UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user_id UUID;
    v_row_count INTEGER;
BEGIN
    -- الحصول على معرف المستخدم
    SELECT user_id INTO v_user_id
    FROM support_interactions
    WHERE id = p_ticket_id;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- تحديث حالة التذكرة
    UPDATE support_interactions
    SET
        status = p_status,
        resolution = COALESCE(p_resolution, resolution),
        assigned_to = COALESCE(p_assigned_to, assigned_to),
        resolved_at = CASE WHEN p_status IN ('resolved', 'closed') THEN NOW() ELSE resolved_at END,
        updated_at = NOW()
    WHERE id = p_ticket_id;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;

    IF v_row_count > 0 THEN
        -- إرسال إشعار للمستخدم
        PERFORM send_notification(
            v_user_id,
            'support_update',
            jsonb_build_object(
                'ticket_id', p_ticket_id,
                'status', p_status,
                'resolution', COALESCE(p_resolution, '')
            )
        );
        RETURN true;
    END IF;

    RETURN false;
END;
$$;
-- ===================================================================

-- دالة إحصائيات الدعم الفني
CREATE OR REPLACE FUNCTION get_support_statistics(
    start_date TIMESTAMPTZ DEFAULT NULL,
    end_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    total_tickets BIGINT,
    open_tickets BIGINT,
    resolved_tickets BIGINT,
    average_resolution_time INTERVAL
    -- تم حذف satisfaction_average حسب المتطلبات
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    start_date_filter TIMESTAMPTZ;
    end_date_filter TIMESTAMPTZ;
BEGIN
    start_date_filter := COALESCE(start_date, DATE_TRUNC('month', NOW()));
    end_date_filter := COALESCE(end_date, NOW());

    RETURN QUERY
    SELECT
        COUNT(*) as total_tickets,
        COUNT(*) FILTER (WHERE status IN ('open', 'in_progress')) as open_tickets,
        COUNT(*) FILTER (WHERE status IN ('resolved', 'closed')) as resolved_tickets,
        AVG(resolved_at - created_at) FILTER (WHERE resolved_at IS NOT NULL) as average_resolution_time
        -- تم حذف satisfaction_average حسب المتطلبات
    FROM support_interactions
    WHERE created_at BETWEEN start_date_filter AND end_date_filter;
END;
$$;
-- ===================================================================

-- دالة تنظيف البيانات الإدارية
CREATE OR REPLACE FUNCTION cleanup_admin_data()
RETURNS TABLE (
    table_name TEXT,
    deleted_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- تنظيف سجلات النسخ الاحتياطي القديمة
    DELETE FROM backup_logs WHERE started_at < NOW() - INTERVAL '3 months';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'backup_logs';
    deleted_count := v_deleted_count;
    RETURN NEXT;

    -- تنظيف تذاكر الدعم المغلقة القديمة
    DELETE FROM support_interactions
    WHERE status = 'closed' AND updated_at < NOW() - INTERVAL '1 year';
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    table_name := 'support_interactions';
    deleted_count := v_deleted_count;
    RETURN NEXT;
END;
$$;

-- ===================================================================

-- دالة إحصائيات النظام العامة
CREATE OR REPLACE FUNCTION get_system_statistics()
RETURNS TABLE (
    metric_name TEXT,
    metric_value BIGINT,
    metric_description TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        'total_users'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي عدد المستخدمين'::TEXT
    FROM profiles

    UNION ALL

    SELECT
        'total_products'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي عدد المنتجات'::TEXT
    FROM products

    UNION ALL

    SELECT
        'total_orders'::TEXT,
        COUNT(*)::BIGINT,
        'إجمالي عدد الطلبات'::TEXT
    FROM orders

    UNION ALL

    SELECT
        'active_users_today'::TEXT,
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_behavior_analytics')
            THEN (SELECT COUNT(DISTINCT user_id) FROM user_behavior_analytics WHERE created_at >= CURRENT_DATE)
            ELSE 0
        END::BIGINT,
        'المستخدمون النشطون اليوم'::TEXT

    UNION ALL

    SELECT
        'orders_today'::TEXT,
        COUNT(*)::BIGINT,
        'طلبات اليوم'::TEXT
    FROM orders
    WHERE created_at >= CURRENT_DATE

    UNION ALL

    SELECT
        'revenue_today'::TEXT,
        COALESCE(SUM(total_amount), 0)::BIGINT,
        'إيرادات اليوم'::TEXT
    FROM orders
    WHERE created_at >= CURRENT_DATE AND status = 'delivered';
END;
$$;
-- ===================================================================

-- دالة فحص إعداد قاعدة البيانات
CREATE OR REPLACE FUNCTION check_database_setup()
RETURNS TABLE (
    total_tables BIGINT,
    total_functions BIGINT,
    total_triggers BIGINT,
    total_policies BIGINT,
    total_indexes BIGINT,
    database_size TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public')::BIGINT,
        (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public')::BIGINT,
        (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_schema = 'public')::BIGINT,
        (SELECT COUNT(*) FROM pg_policies WHERE schemaname = 'public')::BIGINT,
        (SELECT COUNT(*) FROM pg_indexes WHERE schemaname = 'public')::BIGINT,
        pg_size_pretty(pg_database_size(current_database()));
END;
$$;
-- ===================================================================

-- دالة فحص أداء قاعدة البيانات
CREATE OR REPLACE FUNCTION check_database_performance()
RETURNS TABLE (
    slow_queries_count BIGINT,
    cache_hit_ratio DECIMAL,
    active_connections INTEGER,
    database_uptime INTERVAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        0::BIGINT as slow_queries_count, -- يمكن تحسينها لاحقاً
        ROUND(
            (SELECT sum(blks_hit) * 100.0 / sum(blks_hit + blks_read)
             FROM pg_stat_database
             WHERE datname = current_database()), 2
        ) as cache_hit_ratio,
        (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')::INTEGER as active_connections,
        NOW() - pg_postmaster_start_time() as database_uptime;
END;
$$;
-- ===================================================================

-- دالة فحص سلامة قاعدة البيانات المحسنة
CREATE OR REPLACE FUNCTION check_database_integrity()
RETURNS TABLE (
    check_type TEXT,
    table_name TEXT,
    status TEXT,
    description TEXT,
    severity TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- فحص الجداول التي لديها RLS مفعل بدون سياسات
    RETURN QUERY
    SELECT
        'Missing RLS Policy'::TEXT as check_type,
        c.relname::TEXT as table_name,
        'WARNING'::TEXT as status,
        'Table has RLS enabled but no policies defined'::TEXT as description,
        'medium'::TEXT as severity
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE c.relkind = 'r'
    AND n.nspname = 'public'
    AND c.relrowsecurity = true
    AND NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polrelid = c.oid
    );

    -- فحص الجداول بدون مفاتيح أساسية
    RETURN QUERY
    SELECT
        'Missing Primary Key'::TEXT,
        c.relname::TEXT,
        'ERROR'::TEXT,
        'Table does not have a primary key'::TEXT,
        'high'::TEXT
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE c.relkind = 'r'
    AND n.nspname = 'public'
    AND NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conrelid = c.oid AND contype = 'p'
    );

    -- فحص المراجع الخارجية المكسورة
    RETURN QUERY
    SELECT
        'Broken Foreign Key'::TEXT,
        tc.table_name::TEXT,
        'ERROR'::TEXT,
        format('Foreign key %s references non-existent data', tc.constraint_name)::TEXT,
        'high'::TEXT
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND EXISTS (
        SELECT 1 FROM information_schema.tables t
        WHERE t.table_name = tc.table_name
        AND t.table_schema = 'public'
    );

    -- فحص الفهارس المفقودة على المفاتيح الخارجية
    RETURN QUERY
    SELECT
        'Missing Index on FK'::TEXT,
        kcu.table_name::TEXT,
        'WARNING'::TEXT,
        format('Foreign key column %s lacks index', kcu.column_name)::TEXT,
        'medium'::TEXT
    FROM information_schema.key_column_usage kcu
    JOIN information_schema.table_constraints tc
        ON kcu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE tablename = kcu.table_name
        AND indexdef LIKE '%' || kcu.column_name || '%'
    );

    -- إذا لم توجد مشاكل، أرجع رسالة نجاح
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT
            'Database Health'::TEXT,
            'All Tables'::TEXT,
            'SUCCESS'::TEXT,
            'Database integrity check passed successfully'::TEXT,
            'info'::TEXT;
    END IF;

END;
$$;

-- ===================================================================
-- دالة فحص التوافق والتكرارات
-- ===================================================================
CREATE OR REPLACE FUNCTION check_database_compatibility()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details TEXT,
    recommendation TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- فحص الجداول المطلوبة
    RETURN QUERY
    SELECT
        'Required Tables Check'::TEXT,
        CASE 
            WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') AND
                 EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') AND
                 EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders')
            THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'Checking core tables existence'::TEXT,
        'Ensure all core tables are created'::TEXT;

    -- فحص الدوال المطلوبة
    RETURN QUERY
    SELECT
        'Required Functions Check'::TEXT,
        CASE 
            WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_admin') AND
                 EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'send_notification')
            THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'Checking core functions existence'::TEXT,
        'Ensure all core functions are created'::TEXT;

    -- فحص السياسات الأمنية
    RETURN QUERY
    SELECT
        'Security Policies Check'::TEXT,
        CASE 
            WHEN EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'profiles') AND
                 EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'products')
            THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'Checking RLS policies existence'::TEXT,
        'Ensure all tables have proper security policies'::TEXT;

    -- فحص الفهارس المحسنة
    RETURN QUERY
    SELECT
        'Optimized Indexes Check'::TEXT,
        CASE 
            WHEN EXISTS (SELECT 1 FROM pg_indexes WHERE indexname LIKE 'idx_%')
            THEN 'PASS'::TEXT
            ELSE 'FAIL'::TEXT
        END,
        'Checking performance indexes'::TEXT,
        'Ensure all tables have proper indexes for performance'::TEXT;
END;
$$;

