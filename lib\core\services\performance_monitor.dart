import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

/// خدمة مراقبة الأداء والذاكرة
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  Timer? _monitoringTimer;
  final List<PerformanceMetric> _metrics = [];
  bool _isMonitoring = false;

  /// بدء مراقبة الأداء
  void startMonitoring({Duration interval = const Duration(seconds: 5)}) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(interval, (_) => _collectMetrics());

    debugPrint('🔍 بدء مراقبة الأداء');
  }

  /// إيقاف مراقبة الأداء
  void stopMonitoring() {
    _monitoringTimer?.cancel();
    _isMonitoring = false;
    debugPrint('⏹️ تم إيقاف مراقبة الأداء');
  }

  /// جمع مقاييس الأداء
  Future<void> _collectMetrics() async {
    try {
      final metric = PerformanceMetric(
        timestamp: DateTime.now(),
        memoryUsage: await _getMemoryUsage(),
        cpuUsage: await _getCpuUsage(),
        frameRate: await _getFrameRate(),
        networkLatency: await _getNetworkLatency(),
      );

      _metrics.add(metric);

      // الاحتفاظ بآخر 100 قياس فقط
      if (_metrics.length > 100) {
        _metrics.removeAt(0);
      }

      // تحليل الأداء
      _analyzePerformance(metric);
    } catch (e) {
      debugPrint('❌ خطأ في جمع مقاييس الأداء: $e');
    }
  }

  /// الحصول على استخدام الذاكرة
  Future<double> _getMemoryUsage() async {
    try {
      if (Platform.isAndroid) {
        final result = await _invokeNativeMethod('getMemoryUsage');
        return result?.toDouble() ?? 0.0;
      }
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على استخدام المعالج
  Future<double> _getCpuUsage() async {
    try {
      if (Platform.isAndroid) {
        final result = await _invokeNativeMethod('getCpuUsage');
        return result?.toDouble() ?? 0.0;
      }
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  /// الحصول على معدل الإطارات
  Future<double> _getFrameRate() async {
    // يمكن تطبيق قياس معدل الإطارات هنا
    return 60.0; // افتراضي
  }

  /// الحصول على زمن استجابة الشبكة
  Future<double> _getNetworkLatency() async {
    try {
      final stopwatch = Stopwatch()..start();

      // ping بسيط لقياس زمن الاستجابة باستخدام http package
      final response = await http
          .get(Uri.parse('https://www.google.com'))
          .timeout(const Duration(seconds: 5));

      stopwatch.stop();

      if (response.statusCode == 200) {
        return stopwatch.elapsedMilliseconds.toDouble();
      } else {
        return -1.0; // خطأ في الاستجابة
      }
    } catch (e) {
      return -1.0; // خطأ في الشبكة
    }
  }

  /// استدعاء دالة native
  Future<dynamic> _invokeNativeMethod(String method) async {
    const platform = MethodChannel('performance_monitor');
    try {
      return await platform.invokeMethod(method);
    } catch (e) {
      debugPrint('خطأ في استدعاء $method: $e');
      return null;
    }
  }

  /// تحليل الأداء وإرسال تحذيرات
  void _analyzePerformance(PerformanceMetric metric) {
    // تحذير استخدام الذاكرة العالي
    if (metric.memoryUsage > 200) {
      _sendPerformanceAlert(
        'استخدام ذاكرة عالي',
        'استخدام الذاكرة: ${metric.memoryUsage.toStringAsFixed(1)} MB',
        PerformanceAlertLevel.warning,
      );
    }

    // تحذير استخدام المعالج العالي
    if (metric.cpuUsage > 80) {
      _sendPerformanceAlert(
        'استخدام معالج عالي',
        'استخدام المعالج: ${metric.cpuUsage.toStringAsFixed(1)}%',
        PerformanceAlertLevel.warning,
      );
    }

    // تحذير زمن استجابة الشبكة العالي
    if (metric.networkLatency > 2000) {
      _sendPerformanceAlert(
        'زمن استجابة شبكة عالي',
        'زمن الاستجابة: ${metric.networkLatency.toStringAsFixed(0)} ms',
        PerformanceAlertLevel.info,
      );
    }
  }

  /// إرسال تحذير أداء
  void _sendPerformanceAlert(
    String title,
    String message,
    PerformanceAlertLevel level,
  ) {
    if (kDebugMode) {
      final icon =
          level == PerformanceAlertLevel.critical
              ? '🚨'
              : level == PerformanceAlertLevel.warning
              ? '⚠️'
              : 'ℹ️';

      debugPrint('$icon تحذير أداء: $title - $message');
    }
  }

  /// الحصول على تقرير الأداء
  PerformanceReport getPerformanceReport() {
    if (_metrics.isEmpty) {
      return PerformanceReport.empty();
    }

    final memoryUsages = _metrics.map((m) => m.memoryUsage).toList();
    final cpuUsages = _metrics.map((m) => m.cpuUsage).toList();
    final frameRates = _metrics.map((m) => m.frameRate).toList();
    final networkLatencies =
        _metrics.map((m) => m.networkLatency).where((l) => l > 0).toList();

    return PerformanceReport(
      averageMemoryUsage: _calculateAverage(memoryUsages),
      maxMemoryUsage: memoryUsages.reduce((a, b) => a > b ? a : b),
      averageCpuUsage: _calculateAverage(cpuUsages),
      maxCpuUsage: cpuUsages.reduce((a, b) => a > b ? a : b),
      averageFrameRate: _calculateAverage(frameRates),
      minFrameRate: frameRates.reduce((a, b) => a < b ? a : b),
      averageNetworkLatency:
          networkLatencies.isNotEmpty ? _calculateAverage(networkLatencies) : 0,
      maxNetworkLatency:
          networkLatencies.isNotEmpty
              ? networkLatencies.reduce((a, b) => a > b ? a : b)
              : 0,
      totalSamples: _metrics.length,
      monitoringDuration:
          _metrics.isNotEmpty
              ? _metrics.last.timestamp.difference(_metrics.first.timestamp)
              : Duration.zero,
    );
  }

  /// حساب المتوسط
  double _calculateAverage(List<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  /// مسح البيانات
  void clearMetrics() {
    _metrics.clear();
    debugPrint('🗑️ تم مسح بيانات مراقبة الأداء');
  }

  /// طباعة تقرير الأداء
  void printPerformanceReport() {
    final report = getPerformanceReport();

    if (kDebugMode) {
      print('📊 تقرير الأداء:');
      print(
        'متوسط استخدام الذاكرة: ${report.averageMemoryUsage.toStringAsFixed(1)} MB',
      );
      print(
        'أقصى استخدام للذاكرة: ${report.maxMemoryUsage.toStringAsFixed(1)} MB',
      );
      print(
        'متوسط استخدام المعالج: ${report.averageCpuUsage.toStringAsFixed(1)}%',
      );
      print('أقصى استخدام للمعالج: ${report.maxCpuUsage.toStringAsFixed(1)}%');
      print(
        'متوسط معدل الإطارات: ${report.averageFrameRate.toStringAsFixed(1)} FPS',
      );
      print('أقل معدل إطارات: ${report.minFrameRate.toStringAsFixed(1)} FPS');
      print(
        'متوسط زمن استجابة الشبكة: ${report.averageNetworkLatency.toStringAsFixed(0)} ms',
      );
      print('مدة المراقبة: ${report.monitoringDuration.inMinutes} دقيقة');
      print('عدد العينات: ${report.totalSamples}');
    }
  }

  /// تصدير البيانات
  Map<String, dynamic> exportData() {
    return {
      'metrics': _metrics.map((m) => m.toJson()).toList(),
      'report': getPerformanceReport().toJson(),
      'exported_at': DateTime.now().toIso8601String(),
    };
  }

  void dispose() {
    stopMonitoring();
    _metrics.clear();
  }
}

/// مقياس أداء واحد
class PerformanceMetric {
  final DateTime timestamp;
  final double memoryUsage; // MB
  final double cpuUsage; // %
  final double frameRate; // FPS
  final double networkLatency; // ms

  PerformanceMetric({
    required this.timestamp,
    required this.memoryUsage,
    required this.cpuUsage,
    required this.frameRate,
    required this.networkLatency,
  });

  Map<String, dynamic> toJson() => {
    'timestamp': timestamp.toIso8601String(),
    'memory_usage': memoryUsage,
    'cpu_usage': cpuUsage,
    'frame_rate': frameRate,
    'network_latency': networkLatency,
  };
}

/// تقرير الأداء
class PerformanceReport {
  final double averageMemoryUsage;
  final double maxMemoryUsage;
  final double averageCpuUsage;
  final double maxCpuUsage;
  final double averageFrameRate;
  final double minFrameRate;
  final double averageNetworkLatency;
  final double maxNetworkLatency;
  final int totalSamples;
  final Duration monitoringDuration;

  PerformanceReport({
    required this.averageMemoryUsage,
    required this.maxMemoryUsage,
    required this.averageCpuUsage,
    required this.maxCpuUsage,
    required this.averageFrameRate,
    required this.minFrameRate,
    required this.averageNetworkLatency,
    required this.maxNetworkLatency,
    required this.totalSamples,
    required this.monitoringDuration,
  });

  factory PerformanceReport.empty() => PerformanceReport(
    averageMemoryUsage: 0,
    maxMemoryUsage: 0,
    averageCpuUsage: 0,
    maxCpuUsage: 0,
    averageFrameRate: 0,
    minFrameRate: 0,
    averageNetworkLatency: 0,
    maxNetworkLatency: 0,
    totalSamples: 0,
    monitoringDuration: Duration.zero,
  );

  Map<String, dynamic> toJson() => {
    'average_memory_usage': averageMemoryUsage,
    'max_memory_usage': maxMemoryUsage,
    'average_cpu_usage': averageCpuUsage,
    'max_cpu_usage': maxCpuUsage,
    'average_frame_rate': averageFrameRate,
    'min_frame_rate': minFrameRate,
    'average_network_latency': averageNetworkLatency,
    'max_network_latency': maxNetworkLatency,
    'total_samples': totalSamples,
    'monitoring_duration_minutes': monitoringDuration.inMinutes,
  };
}

/// مستوى تحذير الأداء
enum PerformanceAlertLevel { info, warning, critical }
