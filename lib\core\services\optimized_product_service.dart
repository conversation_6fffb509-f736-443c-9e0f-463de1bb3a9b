import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/products/product_model.dart';

/// خدمة المنتجات المحسنة مع Lazy Loading والتخزين المؤقت
class OptimizedProductService extends ChangeNotifier {
  final SupabaseClient _client;
  final Map<String, CachedData<List<ProductModel>>> _cache = {};
  static const Duration _cacheDuration = Duration(minutes: 5);
  
  OptimizedProductService(this._client);

  /// تحميل المنتجات مع Pagination والتخزين المؤقت
  Future<List<ProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? category,
    String? searchQuery,
    String? sortBy = 'created_at',
    bool ascending = false,
  }) async {
    final cacheKey = 'products_${page}_${limit}_${category}_${searchQuery}_${sortBy}_$ascending';
    
    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final cachedData = _cache[cacheKey]!;
      if (!cachedData.isExpired) {
        debugPrint('📦 تم تحميل المنتجات من الذاكرة المؤقتة');
        return cachedData.data;
      }
    }

    try {
      debugPrint('🔄 جاري تحميل المنتجات من الخادم...');
      
      var query = _client
          .from('products')
          .select('''
            *,
            categories(id, name, image_url),
            companies(id, name, logo_url)
          ''')
          .eq('is_available', true)
          .order(sortBy, ascending: ascending)
          .range((page - 1) * limit, page * limit - 1);

      // تطبيق الفلاتر
      if (category != null && category.isNotEmpty) {
        query = query.eq('category_id', category);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('name.ilike.%$searchQuery%,description.ilike.%$searchQuery%');
      }

      final response = await query;
      final products = (response as List)
          .map((json) => ProductModel.fromJson(json))
          .toList();

      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = CachedData(
        data: products,
        timestamp: DateTime.now(),
        duration: _cacheDuration,
      );

      debugPrint('✅ تم تحميل ${products.length} منتج بنجاح');
      
      // تنظيف الذاكرة المؤقتة المنتهية الصلاحية
      _cleanExpiredCache();
      
      return products;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المنتجات: $e');
      throw ProductServiceException('فشل في تحميل المنتجات: $e');
    }
  }

  /// تحميل المنتجات المميزة
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10}) async {
    return getProducts(
      limit: limit,
      sortBy: 'rating_average',
      ascending: false,
    );
  }

  /// تحميل الأكثر مبيعاً
  Future<List<ProductModel>> getBestSelling({int limit = 10}) async {
    return getProducts(
      limit: limit,
      sortBy: 'purchase_count',
      ascending: false,
    );
  }

  /// تحميل العروض
  Future<List<ProductModel>> getOffers({int limit = 10}) async {
    final cacheKey = 'offers_$limit';
    
    if (_cache.containsKey(cacheKey)) {
      final cachedData = _cache[cacheKey]!;
      if (!cachedData.isExpired) {
        return cachedData.data;
      }
    }

    try {
      final response = await _client
          .from('products')
          .select('''
            *,
            categories(id, name, image_url),
            companies(id, name, logo_url)
          ''')
          .eq('is_available', true)
          .not('compare_price', 'is', null)
          .gt('compare_price', 0)
          .order('created_at', ascending: false)
          .limit(limit);

      final products = (response as List)
          .map((json) => ProductModel.fromJson(json))
          .toList();

      _cache[cacheKey] = CachedData(
        data: products,
        timestamp: DateTime.now(),
        duration: _cacheDuration,
      );

      return products;
    } catch (e) {
      throw ProductServiceException('فشل في تحميل العروض: $e');
    }
  }

  /// البحث المتقدم
  Future<List<ProductModel>> searchProducts({
    required String query,
    String? category,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      var supabaseQuery = _client
          .from('products')
          .select('''
            *,
            categories(id, name, image_url),
            companies(id, name, logo_url)
          ''')
          .eq('is_available', true);

      // البحث النصي
      if (query.isNotEmpty) {
        supabaseQuery = supabaseQuery.or(
          'name.ilike.%$query%,description.ilike.%$query%,tags.cs.{$query}'
        );
      }

      // فلتر الفئة
      if (category != null && category.isNotEmpty) {
        supabaseQuery = supabaseQuery.eq('category_id', category);
      }

      // فلتر السعر
      if (minPrice != null) {
        supabaseQuery = supabaseQuery.gte('price', minPrice);
      }
      if (maxPrice != null) {
        supabaseQuery = supabaseQuery.lte('price', maxPrice);
      }

      // فلتر التقييم
      if (minRating != null) {
        supabaseQuery = supabaseQuery.gte('rating_average', minRating);
      }

      // ترتيب وتحديد النطاق
      final response = await supabaseQuery
          .order('rating_average', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      return (response as List)
          .map((json) => ProductModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ProductServiceException('فشل في البحث: $e');
    }
  }

  /// تحميل منتج واحد بالتفصيل
  Future<ProductModel?> getProductById(String id) async {
    final cacheKey = 'product_$id';
    
    if (_cache.containsKey(cacheKey)) {
      final cachedData = _cache[cacheKey]!;
      if (!cachedData.isExpired && cachedData.data.isNotEmpty) {
        return cachedData.data.first;
      }
    }

    try {
      final response = await _client
          .from('products')
          .select('''
            *,
            categories(id, name, image_url),
            companies(id, name, logo_url)
          ''')
          .eq('id', id)
          .single();

      final product = ProductModel.fromJson(response);
      
      _cache[cacheKey] = CachedData(
        data: [product],
        timestamp: DateTime.now(),
        duration: _cacheDuration,
      );

      return product;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المنتج: $e');
      return null;
    }
  }

  /// تنظيف الذاكرة المؤقتة المنتهية الصلاحية
  void _cleanExpiredCache() {
    _cache.removeWhere((key, value) => value.isExpired);
    debugPrint('🧹 تم تنظيف ${_cache.length} عنصر من الذاكرة المؤقتة');
  }

  /// مسح الذاكرة المؤقتة يدوياً
  void clearCache() {
    _cache.clear();
    debugPrint('🗑️ تم مسح الذاكرة المؤقتة بالكامل');
    notifyListeners();
  }

  /// تحديث عداد المشاهدات
  Future<void> incrementViewCount(String productId) async {
    try {
      await _client.rpc('increment_product_views', params: {
        'product_id': productId,
      });
      
      // إزالة المنتج من الذاكرة المؤقتة لإعادة تحميله
      _cache.removeWhere((key, value) => key.contains(productId));
    } catch (e) {
      debugPrint('❌ خطأ في تحديث عداد المشاهدات: $e');
    }
  }

  @override
  void dispose() {
    _cache.clear();
    super.dispose();
  }
}

/// فئة البيانات المخزنة مؤقتاً
class CachedData<T> {
  final T data;
  final DateTime timestamp;
  final Duration duration;

  CachedData({
    required this.data,
    required this.timestamp,
    required this.duration,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > duration;
}

/// استثناء خدمة المنتجات
class ProductServiceException implements Exception {
  final String message;
  ProductServiceException(this.message);

  @override
  String toString() => 'ProductServiceException: $message';
}
