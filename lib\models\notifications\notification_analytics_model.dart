import 'package:flutter/material.dart';

class NotificationAnalyticsModel {
  final String id;
  final String notificationId;
  final String eventType;
  final String? userId;
  final String? deviceType;
  final String? ipAddress;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationAnalyticsModel({
    required this.id,
    required this.notificationId,
    required this.eventType,
    this.userId,
    this.deviceType,
    this.ipAddress,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return NotificationAnalyticsModel(
      id: json['id'] as String,
      notificationId: json['notification_id'] as String,
      eventType: json['event_type'] as String,
      userId: json['user_id'] as String?,
      deviceType: json['device_type'] as String?,
      ipAddress: json['ip_address'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'notification_id': notificationId,
      'event_type': eventType,
      'user_id': userId,
      'device_type': deviceType,
      'ip_address': ipAddress,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  NotificationAnalyticsModel copyWith({
    String? id,
    String? notificationId,
    String? eventType,
    String? userId,
    String? deviceType,
    String? ipAddress,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationAnalyticsModel(
      id: id ?? this.id,
      notificationId: notificationId ?? this.notificationId,
      eventType: eventType ?? this.eventType,
      userId: userId ?? this.userId,
      deviceType: deviceType ?? this.deviceType,
      ipAddress: ipAddress ?? this.ipAddress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}