

-- 1. إعدادات المنتجات الجديدة
-- ===================================================================

-- جدول إعدادات المنتجات الجديدة
CREATE TABLE IF NOT EXISTS new_product_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_name VARCHAR(100) UNIQUE NOT NULL,
    setting_value INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إدراج الإعدادات الافتراضية
INSERT INTO new_product_settings (setting_name, setting_value, description) VALUES
    ('new_product_duration_days', 10, 'عدد الأيام التي يظل فيها المنتج معلماً كـ "جديد"'),
    ('auto_cleanup_enabled', 1, 'تفعيل التنظيف التلقائي للمنتجات الجديدة (1 = مفعل، 0 = معطل)'),
    ('cleanup_interval_hours', 24, 'فترة تشغيل التنظيف التلقائي بالساعات')
ON CONFLICT (setting_name) DO NOTHING;

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_new_product_settings_name ON new_product_settings(setting_name);
CREATE INDEX IF NOT EXISTS idx_new_product_settings_active ON new_product_settings(is_active);

-- ===================================================================

-- جدول سجلات النظام
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    action VARCHAR(50),
    entity_type VARCHAR(50),
    description TEXT,
    message TEXT,
    log_type VARCHAR(50),
    details JSONB DEFAULT '{}'::jsonb,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_system_logs_action ON system_logs(action);
CREATE INDEX IF NOT EXISTS idx_system_logs_entity_type ON system_logs(entity_type);
CREATE INDEX IF NOT EXISTS idx_system_logs_log_type ON system_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);
-- فهرس مركب للبحث السريع
CREATE INDEX IF NOT EXISTS idx_system_logs_type_date ON system_logs(log_type, created_at);

-- ===================================================================
-- 3. إدارة المخزون المتقدمة
-- ===================================================================

-- جدول طلبات إعادة التخزين
CREATE TABLE IF NOT EXISTS restock_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    requested_quantity INTEGER NOT NULL CHECK (requested_quantity > 0),
    current_stock INTEGER,
    notes TEXT,
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'ordered', 'received', 'cancelled')),
    estimated_cost DECIMAL(10,2),
    supplier_info JSONB DEFAULT '{}'::jsonb,
    requested_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    approved_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_restock_requests_product_id ON restock_requests(product_id);
CREATE INDEX IF NOT EXISTS idx_restock_requests_status ON restock_requests(status);
CREATE INDEX IF NOT EXISTS idx_restock_requests_priority ON restock_requests(priority);

-- ===================================================================
-- 5. ميزات التطبيق المتقدمة
-- ===================================================================

-- جدول قوائم المفضلات المتعددة
CREATE TABLE IF NOT EXISTS wishlist_collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    is_default BOOLEAN DEFAULT false,
    items_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wishlist_collections_user_id ON wishlist_collections(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_collections_public ON wishlist_collections(is_public);

-- ===================================================================

-- جدول عناصر قوائم المفضلات
CREATE TABLE IF NOT EXISTS wishlist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    collection_id UUID NOT NULL REFERENCES wishlist_collections(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    notes TEXT,
    priority INTEGER DEFAULT 1,
    added_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(collection_id, product_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wishlist_items_collection_id ON wishlist_items(collection_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_product_id ON wishlist_items(product_id);

-- ===================================================================

-- جدول مقارنة المنتجات
CREATE TABLE IF NOT EXISTS product_comparisons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    session_id VARCHAR(100),
    name VARCHAR(100),
    product_ids JSONB NOT NULL DEFAULT '[]'::jsonb,
    comparison_data JSONB DEFAULT '{}'::jsonb,
    is_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_product_comparisons_user_id ON product_comparisons(user_id);
CREATE INDEX IF NOT EXISTS idx_product_comparisons_session_id ON product_comparisons(session_id);

-- ===================================================================
-- الدوال المساعدة للميزات المتقدمة
-- ===================================================================


-- دالة الحصول على إعدادات المنتجات الجديدة
CREATE OR REPLACE FUNCTION get_new_product_setting(
    p_setting_name VARCHAR
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_setting_value INTEGER;
BEGIN
    SELECT setting_value INTO v_setting_value
    FROM new_product_settings
    WHERE setting_name = p_setting_name AND is_active = true;

    -- إرجاع القيمة الافتراضية إذا لم توجد
    IF v_setting_value IS NULL THEN
        CASE p_setting_name
            WHEN 'new_product_duration_days' THEN v_setting_value := 10;
            WHEN 'auto_cleanup_enabled' THEN v_setting_value := 1;
            WHEN 'cleanup_interval_hours' THEN v_setting_value := 24;
            ELSE v_setting_value := 0;
        END CASE;
    END IF;

    RETURN v_setting_value;
END;
$$;
-- ===================================================================

-- دالة تحديث إعدادات المنتجات الجديدة
CREATE OR REPLACE FUNCTION update_new_product_setting(
    p_setting_name VARCHAR,
    p_setting_value INTEGER
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    UPDATE new_product_settings
    SET setting_value = p_setting_value, updated_at = NOW()
    WHERE setting_name = p_setting_name;

    IF NOT FOUND THEN
        INSERT INTO new_product_settings (setting_name, setting_value)
        VALUES (p_setting_name, p_setting_value);
    END IF;

    RETURN true;
END;
$$;
-- ===================================================================

-- دالة إنشاء منتج جديد مع إعدادات مخصصة
CREATE OR REPLACE FUNCTION create_new_product(
    p_product_data JSONB,
    p_custom_duration_days INTEGER DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_product_id UUID;
    v_duration_days INTEGER;
    v_new_until TIMESTAMPTZ;
BEGIN
    -- الحصول على مدة المنتج الجديد
    IF p_custom_duration_days IS NOT NULL THEN
        v_duration_days := p_custom_duration_days;
    ELSE
        v_duration_days := get_new_product_setting('new_product_duration_days');
    END IF;

    -- حساب تاريخ انتهاء كون المنتج جديد
    v_new_until := NOW() + (v_duration_days || ' days')::INTERVAL;

    -- إضافة الحقول المطلوبة للبيانات
    p_product_data := p_product_data || jsonb_build_object(
        'is_new', true,
        'new_until', v_new_until,
        'created_at', NOW(),
        'updated_at', NOW()
    );

    -- إدراج المنتج (هذا مثال - يجب تخصيصه حسب هيكل جدول المنتجات)
    INSERT INTO products (
        name, description, price, category_id, company_id,
        is_new, new_until, created_at, updated_at
    )
    SELECT
        (p_product_data->>'name')::VARCHAR,
        (p_product_data->>'description')::TEXT,
        (p_product_data->>'price')::DECIMAL,
        (p_product_data->>'category_id')::UUID,
        (p_product_data->>'company_id')::UUID,
        true,
        v_new_until,
        NOW(),
        NOW()
    RETURNING id INTO v_product_id;

    RETURN v_product_id;
END;
$$;
-- ===================================================================
-- 5. دوال إدارة المخزون تلقائيًا
-- ===================================================================

-- دالة لإدارة المخزون تلقائيًا
CREATE OR REPLACE FUNCTION auto_manage_inventory()
RETURNS TABLE (
    product_id UUID,
    action_taken TEXT,
    details JSONB,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    low_stock_product RECORD;
    out_of_stock_product RECORD;
    overstock_product RECORD;
    alert_count INT := 0;
    restock_count INT := 0;
BEGIN
    -- إنشاء جدول مؤقت للنتائج
    CREATE TEMP TABLE IF NOT EXISTS inventory_management_results (
        product_id UUID,
        action_taken TEXT,
        details JSONB,
        status TEXT
    ) ON COMMIT DROP;
    
    -- 1. التحقق من المنتجات ذات المخزون المنخفض
    FOR low_stock_product IN
        SELECT 
            p.id,
            p.name,
            p.sku,
            p.stock_quantity,
            p.min_stock_level,
            p.price,
            c.name AS category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE p.stock_quantity <= COALESCE(p.min_stock_level, 5)
        AND p.stock_quantity > 0
        AND p.is_available = true
        AND NOT EXISTS (
            SELECT 1 FROM inventory_alerts
            WHERE product_id = p.id
            AND alert_type = 'low_stock'
            AND is_resolved = false
            AND created_at > NOW() - INTERVAL '7 days'
        )
        ORDER BY (p.stock_quantity::float / NULLIF(COALESCE(p.min_stock_level, 5), 0)) ASC
        LIMIT 50
    LOOP
        BEGIN
            -- إنشاء تنبيه مخزون منخفض
            INSERT INTO inventory_alerts (
                product_id,
                alert_type,
                threshold_value,
                current_value,
                created_at
            ) VALUES (
                low_stock_product.id,
                'low_stock',
                low_stock_product.min_stock_level,
                low_stock_product.stock_quantity,
                NOW()
            );
            
            -- إنشاء طلب إعادة تخزين تلقائي
            INSERT INTO restock_requests (
                product_id,
                requested_quantity,
                priority,
                notes,
                created_at
            ) VALUES (
                low_stock_product.id,
                GREATEST(low_stock_product.min_stock_level * 2 - low_stock_product.stock_quantity, 10),
                'medium',
                'طلب إعادة تخزين تلقائي - مخزون منخفض',
                NOW()
            );
            
            alert_count := alert_count + 1;
            restock_count := restock_count + 1;
            
            INSERT INTO inventory_management_results VALUES (
                low_stock_product.id,
                'low_stock_alert_created',
                jsonb_build_object(
                    'product_name', low_stock_product.name,
                    'sku', low_stock_product.sku,
                    'current_stock', low_stock_product.stock_quantity,
                    'threshold', low_stock_product.min_stock_level,
                    'restock_requested', true
                ),
                'success'
            );
        EXCEPTION WHEN OTHERS THEN
            INSERT INTO inventory_management_results VALUES (
                low_stock_product.id,
                'low_stock_alert_failed',
                jsonb_build_object(
                    'product_name', low_stock_product.name,
                    'error', SQLERRM
                ),
                'error'
            );
        END;
    END LOOP;
    
    -- 2. التحقق من المنتجات التي نفدت من المخزون
    FOR out_of_stock_product IN
        SELECT 
            p.id,
            p.name,
            p.sku,
            p.price,
            p.min_stock_level * 3 AS restock_level,
            c.name AS category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE p.stock_quantity = 0
        AND p.is_active = true
        AND NOT EXISTS (
            SELECT 1 FROM inventory_alerts
            WHERE product_id = p.id
            AND alert_type = 'out_of_stock'
            AND is_resolved = false
            AND created_at > NOW() - INTERVAL '3 days'
        )
        ORDER BY p.last_restock_date ASC NULLS FIRST
        LIMIT 30
    LOOP
        BEGIN
            -- إنشاء تنبيه نفاد المخزون
            INSERT INTO inventory_alerts (
                product_id,
                alert_type,
                threshold_value,
                current_value,
                created_at
            ) VALUES (
                out_of_stock_product.id,
                'out_of_stock',
                1,
                0,
                NOW()
            );
            
            -- إنشاء طلب إعادة تخزين تلقائي بأولوية عالية
            INSERT INTO restock_requests (
                product_id,
                requested_quantity,
                priority,
                notes,
                created_at
            ) VALUES (
                out_of_stock_product.id,
                COALESCE(out_of_stock_product.restock_level, 20),
                'high',
                'طلب إعادة تخزين عاجل - المنتج غير متوفر في المخزون',
                NOW()
            );
            
            alert_count := alert_count + 1;
            restock_count := restock_count + 1;
            
            INSERT INTO inventory_management_results VALUES (
                out_of_stock_product.id,
                'out_of_stock_alert_created',
                jsonb_build_object(
                    'product_name', out_of_stock_product.name,
                    'sku', out_of_stock_product.sku,
                    'urgent_restock_requested', true,
                    'requested_quantity', COALESCE(out_of_stock_product.restock_level, 20)
                ),
                'success'
            );
        EXCEPTION WHEN OTHERS THEN
            INSERT INTO inventory_management_results VALUES (
                out_of_stock_product.id,
                'out_of_stock_alert_failed',
                jsonb_build_object(
                    'product_name', out_of_stock_product.name,
                    'error', SQLERRM
                ),
                'error'
            );
        END;
    END LOOP;
    
    -- 3. التحقق من المنتجات ذات المخزون الزائد
    FOR overstock_product IN
        SELECT 
            p.id,
            p.name,
            p.sku,
            p.stock_quantity,
            p.min_stock_level * 4 AS max_stock_threshold,
            p.price,
            c.name AS category_name
        FROM products p
        JOIN categories c ON p.category_id = c.id
        WHERE p.min_stock_level IS NOT NULL
        AND p.stock_quantity > (p.min_stock_level * 4)
        AND p.is_active = true
        AND NOT EXISTS (
            SELECT 1 FROM inventory_alerts
            WHERE product_id = p.id
            AND alert_type = 'overstock'
            AND is_resolved = false
            AND created_at > NOW() - INTERVAL '14 days'
        )
        ORDER BY (p.stock_quantity::float / NULLIF(p.min_stock_level * 4, 1)) DESC
        LIMIT 20
    LOOP
        BEGIN
            -- إنشاء تنبيه مخزون زائد
            INSERT INTO inventory_alerts (
                product_id,
                alert_type,
                threshold_value,
                current_value,
                created_at
            ) VALUES (
                overstock_product.id,
                'overstock',
                overstock_product.max_stock_threshold,
                overstock_product.stock_quantity,
                NOW()
            );
            
            alert_count := alert_count + 1;
            
            INSERT INTO inventory_management_results VALUES (
                overstock_product.id,
                'overstock_alert_created',
                jsonb_build_object(
                    'product_name', overstock_product.name,
                    'sku', overstock_product.sku,
                    'current_stock', overstock_product.stock_quantity,
                    'max_threshold', overstock_product.max_stock_threshold,
                    'excess_quantity', overstock_product.stock_quantity - overstock_product.max_stock_threshold
                ),
                'success'
            );
        EXCEPTION WHEN OTHERS THEN
            INSERT INTO inventory_management_results VALUES (
                overstock_product.id,
                'overstock_alert_failed',
                jsonb_build_object(
                    'product_name', overstock_product.name,
                    'error', SQLERRM
                ),
                'error'
            );
        END;
    END LOOP;
    
    -- 4. تحديث حالة طلبات إعادة التخزين القديمة
    BEGIN
        WITH updated AS (
            UPDATE restock_requests
            SET status = 'cancelled',
            notes = notes || E'\nتم إلغاء الطلب تلقائيًا بسبب عدم المعالجة لفترة طويلة',
            updated_at = NOW()
            WHERE status = 'pending'
            AND created_at < NOW() - INTERVAL '30 days'
            RETURNING id
        )
        SELECT count(*) INTO restock_count FROM updated;
        
        INSERT INTO inventory_management_results VALUES (
            NULL,
            'old_restock_requests_cancelled',
            jsonb_build_object(
                'count', restock_count,
                'older_than', '30 days'
            ),
            'success'
        );
    EXCEPTION WHEN OTHERS THEN
        INSERT INTO inventory_management_results VALUES (
            NULL,
            'old_restock_requests_update_failed',
            jsonb_build_object(
                'error', SQLERRM
            ),
            'error'
        );
    END;
    
    -- 5. تسجيل نتائج إدارة المخزون في سجل النظام
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_logs') THEN
        INSERT INTO system_logs (
            log_type,
            details,
            message
        ) VALUES (
            'inventory_management',
            jsonb_build_object(
                'alerts_created', alert_count,
                'restock_requests_created', restock_count,
                'timestamp', NOW()
            ),
            format('تم إدارة المخزون تلقائيًا (إنشاء %s تنبيه، %s طلب إعادة تخزين)', 
                alert_count, restock_count)
        );
    END IF;
    
    -- إرجاع النتائج
    RETURN QUERY SELECT * FROM inventory_management_results;
END;
$$;
-- ===================================================================
-- دالة لتحديث حالة المخزون بناءً على الطلبات المكتملة
CREATE OR REPLACE FUNCTION update_inventory_from_orders()
RETURNS INT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_updated_count INT := 0;
    v_order RECORD;
    v_order_item RECORD;
BEGIN
    -- البحث عن الطلبات المكتملة التي لم يتم تحديث المخزون لها بعد
    FOR v_order IN
        SELECT o.id, o.order_number
        FROM orders o
        WHERE o.status = 'delivered'
        -- تم تعديل الشرط لتجنب استخدام حقل غير موجود
        -- AND o.inventory_updated = false
        ORDER BY o.updated_at DESC
        LIMIT 100
    LOOP
        -- تحديث المخزون لكل عنصر في الطلب
        FOR v_order_item IN
            SELECT oi.product_id, oi.quantity
            FROM order_items oi
            WHERE oi.order_id = v_order.id
        LOOP
            -- تحديث كمية المخزون
            UPDATE products
            SET 
                stock_quantity = GREATEST(0, stock_quantity - v_order_item.quantity),
                sales_count = COALESCE(sales_count, 0) + v_order_item.quantity,
                updated_at = NOW()
            WHERE id = v_order_item.product_id;
            
            v_updated_count := v_updated_count + 1;
        END LOOP;
        
        -- تحديث حالة الطلب
        UPDATE orders
        SET 
            updated_at = NOW()
        WHERE id = v_order.id;
        
        -- تسجيل العملية في سجل النظام
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_logs') THEN
            INSERT INTO system_logs (
                log_type,
                details,
                message
            ) VALUES (
                'inventory_update',
                jsonb_build_object(
                    'order_id', v_order.id,
                    'order_number', v_order.order_number,
                    'items_updated', v_updated_count
                ),
                format('تم تحديث المخزون للطلب رقم %s', v_order.order_number)
            );
        END IF;
    END LOOP;
    
    RETURN v_updated_count;
END;
$$;

