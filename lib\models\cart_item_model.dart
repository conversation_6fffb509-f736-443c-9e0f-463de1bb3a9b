import 'package:motorcycle_parts_shop/models/products/product_model.dart';

/// نموذج يمثل عنصرًا في سلة التسوق
class CartItemModel {
  final String id;
  final String userId;
  final String productId;
  final String productName;
  final String productImage;
  final double price;
  final double? discountPrice;
  final int quantity;
  final DateTime createdAt;
  final DateTime updatedAt;
  bool selected;

  CartItemModel({
    required this.id,
    required this.userId,
    required this.productId,
    required this.productName,
    required this.productImage,
    required this.price,
    this.discountPrice,
    required this.quantity,
    required this.createdAt,
    required this.updatedAt,
    this.selected = false,
  });

  /// تحويل الكائن إلى خريطة (Map) لتسهيل التخزين أو الإرسال
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'product_id': productId,
      'product_name': productName,
      'product_image': productImage,
      'price': price,
      'discount_price': discountPrice,
      'quantity': quantity,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'selected': selected,
    };
  }

  /// تحويل البيانات من خريطة (Map) إلى كائن
  factory CartItemModel.fromJson(Map<String, dynamic> json) {
    return CartItemModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      productImage: json['product_image'] as String,
      price: (json['price'] as num).toDouble(),
      discountPrice:
          json['discount_price'] != null
              ? (json['discount_price'] as num).toDouble()
              : null,
      quantity: json['quantity'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      selected: json['selected'] as bool? ?? false,
    );
  }

  /// إنشاء عنصر سلة تسوق من منتج معين
  factory CartItemModel.fromProduct(
    ProductModel product, {
    required String userId,
    int quantity = 1,
  }) {
    return CartItemModel(
      id:
          DateTime.now().millisecondsSinceEpoch
              .toString(), // إنشاء معرف فريد بناءً على الوقت الحالي
      userId: userId,
      productId: product.id,
      productName: product.name,
      productImage: product.mainImage ?? 'assets/images/default_product.png',
      price: product.price,
      discountPrice: product.discountPrice,
      quantity: quantity, // الكمية الافتراضية هي 1
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      selected: false,
    );
  }

  /// حساب السعر الإجمالي للعنصر بناءً على الكمية والسعر
  double get totalPrice => (discountPrice ?? price) * quantity;

  /// حساب قيمة الخصم للعنصر
  double get discountAmount {
    if (discountPrice == null) return 0;
    return price -
        discountPrice!; // قيمة الخصم = الفرق بين السعر الأصلي وسعر الخصم
  }

  /// نسخ الكائن مع إمكانية تعديل بعض الخصائص
  CartItemModel copyWith({
    String? id,
    String? userId,
    String? productId,
    String? productName,
    String? productImage,
    double? price,
    double? discountPrice,
    int? quantity,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? selected,
  }) {
    return CartItemModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productImage: productImage ?? this.productImage,
      price: price ?? this.price,
      discountPrice: discountPrice ?? this.discountPrice,
      quantity: quantity ?? this.quantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      selected: selected ?? this.selected,
    );
  }
}
