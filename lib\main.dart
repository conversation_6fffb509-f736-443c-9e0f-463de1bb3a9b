import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:motorcycle_parts_shop/core/analytics/unified_analytics.dart';
import 'package:motorcycle_parts_shop/core/backup/backup_manager.dart';
import 'package:motorcycle_parts_shop/core/monitoring/app_logger.dart';
import 'package:motorcycle_parts_shop/core/services/accessibility_service.dart';
import 'package:motorcycle_parts_shop/core/services/ad_campaign_service.dart';
import 'package:motorcycle_parts_shop/core/services/address_service.dart';
import 'package:motorcycle_parts_shop/core/services/admin_service.dart';
import 'package:motorcycle_parts_shop/core/services/advanced_media_manager.dart';
import 'package:motorcycle_parts_shop/core/services/advanced_search_service.dart';
import 'package:motorcycle_parts_shop/core/services/advertisement_service.dart';
import 'package:motorcycle_parts_shop/core/services/app_shortcuts_service.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart';
import 'package:motorcycle_parts_shop/core/services/chatbot_service.dart';
import 'package:motorcycle_parts_shop/core/services/connectivity_service.dart';
import 'package:motorcycle_parts_shop/core/services/coupon_service.dart';
import 'package:motorcycle_parts_shop/core/services/currency_service.dart';
import 'package:motorcycle_parts_shop/core/services/database_optimizer.dart';
import 'package:motorcycle_parts_shop/core/services/device_capability_service.dart';
import 'package:motorcycle_parts_shop/core/services/image_recognition_service.dart';
import 'package:motorcycle_parts_shop/core/services/inventory_service.dart';
import 'package:motorcycle_parts_shop/core/services/navigation_service.dart';
import 'package:motorcycle_parts_shop/core/services/notification_service.dart';
import 'package:motorcycle_parts_shop/core/services/offline_mode_service.dart';
import 'package:motorcycle_parts_shop/core/services/order_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/services/sync_manager.dart';
import 'package:motorcycle_parts_shop/core/services/theme_service.dart';
import 'package:motorcycle_parts_shop/core/services/unified_database_service.dart';
import 'package:motorcycle_parts_shop/core/services/unified_performance_service.dart';
import 'package:motorcycle_parts_shop/core/services/user_interaction_service.dart';
import 'package:motorcycle_parts_shop/core/services/user_settings_service.dart';
import 'package:motorcycle_parts_shop/core/services/vibration_service.dart';
import 'package:motorcycle_parts_shop/core/services/wishlist_service.dart';
import 'package:motorcycle_parts_shop/core/utils/api_keys_migration.dart';
import 'package:motorcycle_parts_shop/core/utils/cloudinary_setup.dart';
import 'package:motorcycle_parts_shop/core/utils/service_locator.dart';
import 'package:motorcycle_parts_shop/core/widgets/shortcuts_manager.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:motorcycle_parts_shop/screens/admin/add_edit_product_screen.dart';
import 'package:motorcycle_parts_shop/screens/admin/inventory_management_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/auth_callback_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/change_password_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/login_screen.dart';
import 'package:motorcycle_parts_shop/screens/auth/register_screen.dart';
import 'package:motorcycle_parts_shop/screens/cart/cart_screen.dart';
import 'package:motorcycle_parts_shop/screens/home/<USER>';
import 'package:motorcycle_parts_shop/screens/onboarding/onboarding_screen.dart';
import 'package:motorcycle_parts_shop/screens/product/product_details_screen.dart';
import 'package:motorcycle_parts_shop/screens/profile/notification_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/app_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/privacy_settings_screen.dart';
import 'package:motorcycle_parts_shop/screens/settings/security_activity_screen.dart';
import 'package:motorcycle_parts_shop/screens/welcome_screen.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'core/services/optimization_applier.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  Provider.debugCheckInvalidValueType = null;

  try {
    // تحميل ملف البيئة أولاً
    await dotenv.load(fileName: ".env");
    debugPrint('✅ تم تحميل ملف البيئة بنجاح');

    // ترحيل مفاتيح API إلى التخزين الآمن
    await ApiKeysMigration.migrateAllApiKeys();
    debugPrint('✅ تم ترحيل مفاتيح API بنجاح');

    // تهيئة Cloudinary بعد تحميل ملف البيئة
    final cloudinaryInitialized = await CloudinarySetup.initialize();
    debugPrint('✅ حالة تهيئة Cloudinary: $cloudinaryInitialized');

    // تهيئة AdvancedMediaManager بعد تهيئة Cloudinary
    if (cloudinaryInitialized) {
      try {
        final mediaManager = AdvancedMediaManager();
        final mediaManagerInitialized = await mediaManager.initialize();
        debugPrint(
          '✅ تم تهيئة AdvancedMediaManager بنجاح: $mediaManagerInitialized',
        );
      } catch (e) {
        debugPrint('⚠️ تحذير: فشل في تهيئة AdvancedMediaManager: $e');
        // نتجاهل الخطأ ونستمر
      }
    }

    // تهيئة نظام الأداء الموحد
    await UnifiedPerformanceSystem.initialize();
    debugPrint('✅ تم تهيئة UnifiedPerformanceSystem بنجاح');

    // تطبيق التحسينات الجديدة
    try {
      await OptimizationApplier.applyAllOptimizations();
      debugPrint('✅ تم تطبيق جميع التحسينات بنجاح');
    } catch (e) {
      debugPrint('⚠️ تحذير: فشل في تطبيق بعض التحسينات: $e');
      // نتجاهل الخطأ ونستمر
    }

    final supabaseUrl = dotenv.env['SUPABASE_URL'];
    debugPrint(
      '🔍 Supabase URL: ${supabaseUrl != null ? "موجود" : "غير موجود"}',
    );

    // استخدام anon key للواجهة الأمامية (الطريقة الآمنة)
    final supabaseAnonKey = await ApiKeysMigration.getApiKey(
      'SUPABASE_ANON_KEY',
    );
    debugPrint(
      '🔍 Supabase Anon Key: ${supabaseAnonKey != null ? "موجود" : "غير موجود"}',
    );

    if (supabaseUrl == null || supabaseAnonKey == null) {
      throw Exception('المتغيرات البيئية المطلوبة غير موجودة');
    }

    // تهيئة SharedPreferences والخدمات الأساسية بالتوازي
    final initFutures = await Future.wait([
      SharedPreferences.getInstance(),
      _initializeSupabase(supabaseUrl, supabaseAnonKey),
    ]);

    final prefs = initFutures[0] as SharedPreferences;

    final coreServices = await _initializeCoreServices(prefs);

    // تعيين اتجاه الشاشة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // تشغيل التطبيق فوراً مع الخدمات الأساسية
    runApp(MyApp(prefs: prefs, coreServices: coreServices));

    // تهيئة الخدمات الثانوية في الخلفية
    _initializeSecondaryServicesInBackground(coreServices, prefs);
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    runApp(ErrorApp(errorMessage: e.toString()));
  }
}

/// تهيئة Supabase بالطريقة الآمنة باستخدام anon key
Future<void> _initializeSupabase(String url, String anonKey) async {
  try {
    debugPrint('🔄 جاري تهيئة Supabase بالطريقة الآمنة...');
    debugPrint('🔍 URL: $url');
    debugPrint('🔍 Anon Key: ${anonKey.substring(0, 10)}...');

    await Supabase.initialize(
      url: url,
      anonKey: anonKey, // استخدام anon key (الطريقة الآمنة)
      authOptions: const FlutterAuthClientOptions(autoRefreshToken: true),
    );

    debugPrint('✅ تم تهيئة Supabase بنجاح بالطريقة الآمنة');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة Supabase: $e');
    // لا نرمي استثناء للسماح للتطبيق بالعمل
  }
}

/// تهيئة الخدمات الأساسية فقط للسرعة
Future<CoreServices> _initializeCoreServices(SharedPreferences prefs) async {
  debugPrint('🔄 جاري تهيئة الخدمات الأساسية فقط...');
  final supabaseClient = Supabase.instance.client;
  debugPrint('✅ تم الحصول على عميل Supabase');

  // تهيئة ServiceLocator
  await ServiceLocator.initialize();

  // الخدمات الأساسية فقط (3-5 خدمات)
  final authService = AuthSupabaseService();
  final themeService = ThemeService();
  final navigationService = NavigationService();
  final connectivityService = ConnectivityService();

  // تهيئة الخدمات الأساسية بالتوازي
  debugPrint('🔄 جاري تهيئة الخدمات الأساسية بالتوازي...');
  try {
    await Future.wait([
      themeService.initialize(),
      connectivityService.initialize(),
    ]);
    debugPrint('✅ تم تهيئة الخدمات الأساسية بنجاح');
  } catch (e) {
    debugPrint('⚠️ خطأ في تهيئة بعض الخدمات الأساسية: $e');
  }

  return CoreServices(
    authService: authService,
    themeService: themeService,
    navigationService: navigationService,
    connectivityService: connectivityService,
  );
}

/// تهيئة الخدمات الثانوية في الخلفية
Future<AppServices> _initializeSecondaryServices(
  CoreServices coreServices,
  SharedPreferences prefs,
) async {
  debugPrint('🔄 جاري تهيئة الخدمات الثانوية...');
  final supabaseClient = Supabase.instance.client;

  // إنشاء الخدمات الثانوية
  final cartService = CartService();
  final wishlistService = WishlistService();
  final productService = ProductService(supabaseClient);
  final analyticsService = UnifiedAnalyticsService();
  final userSettingsService = UserSettingsService(client: supabaseClient);
  final userInteractionService = UserInteractionService(supabaseClient);
  final shortcutsService = AppShortcutsService();

  // خدمات تحسين قاعدة البيانات
  final databaseOptimizer = DatabaseOptimizer(supabaseClient);
  final databaseService = UnifiedDatabaseService();
  final logger = AppLogger();

  await databaseService.initialize();
  logger.initialize(supabaseClient);

  // تهيئة الخدمات الثانوية بالتوازي
  try {
    await Future.wait([
      cartService.initialize(),
      wishlistService.initialize(),
      analyticsService.initialize(supabase: supabaseClient, prefs: prefs),
      userSettingsService.initialize(),
    ]);
    debugPrint('✅ تم تهيئة الخدمات الثانوية بنجاح');
  } catch (e) {
    debugPrint('⚠️ خطأ في تهيئة بعض الخدمات الثانوية: $e');
  }

  // تهيئة خدمة المصادقة بدون انتظار
  coreServices.authService
      .initialize()
      .then((_) {
        debugPrint('✅ تم تهيئة خدمة المصادقة');
      })
      .catchError((e) {
        debugPrint('⚠️ خطأ في تهيئة خدمة المصادقة: $e');
      });

  // تهيئة خدمة الاختصارات السريعة
  shortcutsService
      .initialize()
      .then((_) {
        debugPrint('✅ تم تهيئة خدمة الاختصارات السريعة');
      })
      .catchError((e) {
        debugPrint('⚠️ خطأ في تهيئة خدمة الاختصارات: $e');
      });

  return AppServices(
    supabaseService: coreServices.authService,
    themeService: coreServices.themeService,
    cartService: cartService,
    wishlistService: wishlistService,
    navigationService: coreServices.navigationService,
    shortcutsService: shortcutsService,

    connectivityService: coreServices.connectivityService,
    productService: productService,
    analyticsService: analyticsService,

    // خدمات تحسين قاعدة البيانات ومعالجة الأخطاء
    databaseOptimizer: databaseOptimizer,
    databaseService: databaseService,
    logger: logger,

    chatbotService: ChatbotService(
      coreServices.authService,
      userInteractionService,
      analyticsService,
    ),
    syncManager: SyncManager(
      supabase: supabaseClient,
      cartService: cartService,
      wishlistService: wishlistService,
    ),

    accessibilityService: AccessibilityService(),
    // خدمات افتراضية للباقي
    addressService: AddressService(),
    adminService: AdminService(client: supabaseClient),
    advancedSearchService: AdvancedSearchService(),
    advertisementService: AdvertisementService(),
    adCampaignService: AdCampaignService(supabaseClient),

    // الخدمات الجديدة
    userSettingsService: userSettingsService,

    backupService: BackupManager(),
    couponService: CouponService(supabaseClient),
    currencyService: CurrencyService(),
    deviceCapabilityService: DeviceCapabilityService(),

    imageRecognitionService: ImageRecognitionService(productService),
    inventoryService: InventoryService(),
    notificationService: NotificationService(),
    offlineModeService: OfflineModeService(),
    orderService: OrderService(),
    vibrationService: VibrationService(userSettingsService),

    userInteractionService: userInteractionService,
  );
}

/// تهيئة الخدمات الثانوية في الخلفية
void _initializeSecondaryServicesInBackground(
  CoreServices coreServices,
  SharedPreferences prefs,
) {
  debugPrint('🔄 جدولة تهيئة الخدمات الثانوية في الخلفية...');
  Future.delayed(const Duration(milliseconds: 500), () async {
    try {
      final services = await _initializeSecondaryServices(coreServices, prefs);
      debugPrint('✅ تم تهيئة جميع الخدمات الثانوية بنجاح');

      // تحديث ServiceLocator بالخدمات الجديدة
      // ServiceLocator.registerLazySingleton(() => services.cartService);
      // ServiceLocator.registerLazySingleton(() => services.wishlistService);
      // ServiceLocator.registerLazySingleton(() => services.productService);
      debugPrint('✅ تم تسجيل الخدمات الثانوية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات الثانوية: $e');
    }
  });
}

/// تهيئة خدمة مع تسجيل الأخطاء
Future<void> initializeServiceWithLogging(
  String serviceName,
  Future<void> Function() initFunction,
) async {
  try {
    debugPrint('🔄 جاري تهيئة $serviceName...');
    await initFunction();
    debugPrint('✅ تم تهيئة $serviceName بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة $serviceName: $e');
  }
}

/// تهيئة الخدمات الإضافية في الخلفية
/// فئة الخدمات الأساسية
class CoreServices {
  final AuthSupabaseService authService;
  final ThemeService themeService;
  final NavigationService navigationService;
  final ConnectivityService connectivityService;

  CoreServices({
    required this.authService,
    required this.themeService,
    required this.navigationService,
    required this.connectivityService,
  });
}

void initializeAdditionalServicesInBackground(AppServices services) {
  debugPrint('🔄 جدولة تهيئة الخدمات الإضافية في الخلفية...');
  Future.delayed(const Duration(milliseconds: 500), () async {
    try {
      debugPrint('🔄 بدء تهيئة الخدمات الإضافية في الخلفية');

      // تكوين خدمة الإشعارات مع خدمة الاهتزاز
      try {
        services.notificationService.setVibrationService(
          services.vibrationService,
        );
        debugPrint('✅ تم تكوين خدمة الإشعارات مع خدمة الاهتزاز');
      } catch (e) {
        debugPrint('⚠️ خطأ في تكوين خدمة الإشعارات: $e');
      }

      // تفعيل الاهتزاز عند بدء التطبيق
      try {
        await services.vibrationService.vibrateOnAppStart();
        debugPrint('✅ تم تفعيل الاهتزاز عند بدء التطبيق');
      } catch (e) {
        debugPrint('⚠️ خطأ في تفعيل الاهتزاز: $e');
      }

      // تهيئة الخدمات الإضافية بدون انتظار
      debugPrint('🔄 جاري تهيئة الخدمات الإضافية بالتوازي...');
      final futures = [
        initializeServiceWithLogging('خدمة الدردشة الآلية (Speech)', () async {
          await services.chatbotService.initSpeech();
          return;
        }),
        initializeServiceWithLogging(
          'مدير المزامنة',
          () => services.syncManager.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة إمكانية الوصول',
          () => services.accessibilityService.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة المخزون',
          () => services.inventoryService.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة الإشعارات',
          () => services.notificationService.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة الطلبات',
          () => services.orderService.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة قدرات الجهاز',
          () => services.deviceCapabilityService.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة الوضع غير المتصل',
          () => services.offlineModeService.initialize(),
        ),
        initializeServiceWithLogging(
          'خدمة التعرف على الصور',
          () => services.imageRecognitionService.initialize(),
        ),
      ];

      // تهيئة الخدمات بالتوازي
      await Future.wait(futures);

      debugPrint('✅ تم تهيئة جميع الخدمات الإضافية في الخلفية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ عام في تهيئة الخدمات الإضافية: $e');
    }
  });
}

class AppServices {
  // استخدام ServiceLocator.storage بدلاً من UnifiedStorage مباشرة
  final AuthSupabaseService supabaseService;
  final ThemeService themeService;
  final CartService cartService;
  final WishlistService wishlistService;
  final NavigationService navigationService;
  final ConnectivityService connectivityService;
  final ProductService productService;
  final UnifiedAnalyticsService analyticsService;

  final ChatbotService chatbotService;
  final SyncManager syncManager;
  final AccessibilityService accessibilityService;
  final AppShortcutsService shortcutsService;

  // خدمات تحسين قاعدة البيانات ومعالجة الأخطاء
  final DatabaseOptimizer databaseOptimizer;
  final UnifiedDatabaseService databaseService;
  final AppLogger logger;

  // الخدمات الإضافية
  final AddressService addressService;
  final AdminService adminService;
  final AdvancedSearchService advancedSearchService;
  final AdvertisementService advertisementService;
  final AdCampaignService adCampaignService;
  final BackupManager backupService;
  final CouponService couponService;
  final CurrencyService currencyService;

  final DeviceCapabilityService deviceCapabilityService;
  final ImageRecognitionService imageRecognitionService;
  final InventoryService inventoryService;
  final NotificationService notificationService;
  final OfflineModeService offlineModeService;
  final OrderService orderService;
  final VibrationService vibrationService;
  final UserInteractionService userInteractionService;

  // الخدمات الجديدة
  final UserSettingsService userSettingsService;
  // تم حذف خدمة المحفظة الإلكترونية

  AppServices({
    required this.supabaseService,
    required this.themeService,
    required this.cartService,
    required this.wishlistService,
    required this.navigationService,
    required this.connectivityService,
    required this.productService,
    required this.shortcutsService,
    required this.analyticsService,

    required this.chatbotService,
    required this.syncManager,
    required this.accessibilityService,
    // خدمات تحسين قاعدة البيانات ومعالجة الأخطاء
    required this.databaseOptimizer,
    required this.databaseService,
    required this.logger,
    // الخدمات الإضافية
    required this.addressService,
    required this.adminService,
    required this.advancedSearchService,
    required this.advertisementService,
    required this.adCampaignService,
    required this.backupService,
    required this.couponService,
    required this.currencyService,
    required this.deviceCapabilityService,
    required this.imageRecognitionService,
    required this.inventoryService,
    required this.notificationService,
    required this.offlineModeService,
    required this.orderService,
    required this.vibrationService,
    required this.userInteractionService,
    // الخدمات الجديدة
    required this.userSettingsService,
  });
}

class MyApp extends StatefulWidget {
  final SharedPreferences prefs;
  final CoreServices coreServices;

  const MyApp({super.key, required this.prefs, required this.coreServices});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late bool _onboardingComplete;
  bool _isInitialized = false;
  AppServices? _secondaryServices;

  @override
  void initState() {
    super.initState();
    _onboardingComplete = widget.prefs.getBool('onboarding_complete') ?? false;
    _isInitialized = true;
    _initializeSecondaryServices();
  }

  void _initializeSecondaryServices() {
    Future.delayed(const Duration(milliseconds: 1000), () async {
      try {
        // تهيئة الخدمات الثانوية محلياً
        final supabaseClient = Supabase.instance.client;
        final cartService = CartService();
        final wishlistService = WishlistService();
        final productService = ProductService(supabaseClient);
        final analyticsService = UnifiedAnalyticsService();

        await Future.wait([
          cartService.initialize(),
          wishlistService.initialize(),
          analyticsService.initialize(
            supabase: supabaseClient,
            prefs: widget.prefs,
          ),
        ]);

        if (mounted) {
          setState(() {
            _secondaryServices = AppServices(
              supabaseService: widget.coreServices.authService,
              themeService: widget.coreServices.themeService,
              cartService: cartService,
              wishlistService: wishlistService,
              navigationService: widget.coreServices.navigationService,
              connectivityService: widget.coreServices.connectivityService,
              productService: productService,
              analyticsService: analyticsService,
              // باقي الخدمات الافتراضية
              shortcutsService: AppShortcutsService(),
              databaseOptimizer: DatabaseOptimizer(supabaseClient),
              databaseService: UnifiedDatabaseService(),
              logger: AppLogger(),
              chatbotService: ChatbotService(
                widget.coreServices.authService,
                UserInteractionService(supabaseClient),
                analyticsService,
              ),
              syncManager: SyncManager(
                supabase: supabaseClient,
                cartService: cartService,
                wishlistService: wishlistService,
              ),
              accessibilityService: AccessibilityService(),
              addressService: AddressService(),
              adminService: AdminService(client: supabaseClient),
              advancedSearchService: AdvancedSearchService(),
              advertisementService: AdvertisementService(),
              adCampaignService: AdCampaignService(supabaseClient),
              userSettingsService: UserSettingsService(client: supabaseClient),
              backupService: BackupManager(),
              couponService: CouponService(supabaseClient),
              currencyService: CurrencyService(),
              deviceCapabilityService: DeviceCapabilityService(),
              imageRecognitionService: ImageRecognitionService(productService),
              inventoryService: InventoryService(),
              notificationService: NotificationService(),
              offlineModeService: OfflineModeService(),
              orderService: OrderService(),
              vibrationService: VibrationService(
                UserSettingsService(client: supabaseClient),
              ),
              userInteractionService: UserInteractionService(supabaseClient),
            );
          });
        }
      } catch (e) {
        debugPrint('❌ خطأ في تهيئة الخدمات الثانوية: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const MaterialApp(
        home: Scaffold(body: Center(child: CircularProgressIndicator())),
      );
    }

    return MultiProvider(
      providers: [
        Provider.value(value: ServiceLocator.storage),
        ChangeNotifierProvider.value(value: ServiceLocator.storage),
        // الخدمات الأساسية متاحة دائماً
        ChangeNotifierProvider.value(value: widget.coreServices.authService),
        ChangeNotifierProvider.value(value: widget.coreServices.themeService),
        Provider.value(value: widget.coreServices.connectivityService),
        Provider.value(value: widget.coreServices.navigationService),

        // الخدمات الثانوية (قد تكون null في البداية)
        if (_secondaryServices != null) ...[
          ChangeNotifierProvider.value(value: _secondaryServices!.cartService),
          ChangeNotifierProvider.value(
            value: _secondaryServices!.wishlistService,
          ),
          ChangeNotifierProvider.value(
            value: _secondaryServices!.analyticsService,
          ),
          Provider.value(value: _secondaryServices!.accessibilityService),
          Provider.value(value: _secondaryServices!.productService),
          Provider.value(value: _secondaryServices!.syncManager),
          Provider.value(value: _secondaryServices!.chatbotService),
          Provider.value(value: _secondaryServices!.adCampaignService),
          ChangeNotifierProvider.value(
            value: _secondaryServices!.userSettingsService,
          ),
        ],
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, _) {
          final themeData =
              themeService.isDarkMode(context)
                  ? themeService.getDarkTheme()
                  : themeService.getLightTheme();

          return ShortcutsManager(
            child: MaterialApp(
              title: 'متجر قطع غيار الدراجات النارية',
              theme: themeData,
              home: _getInitialScreen(),
              navigatorKey: widget.coreServices.navigationService.navigatorKey,
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [Locale('ar', '')],
              locale: const Locale('ar', ''), // فقط اللغة العربية
              routes: {
                '/home': (context) => const HomeScreen(),
                '/welcome': (context) => const WelcomeScreen(),
                '/login': (context) => const LoginScreen(),
                '/register': (context) => const RegisterScreen(),
                '/auth/callback': (context) => const AuthCallbackScreen(),
                '/notification_settings':
                    (context) => const NotificationSettingsScreen(),

                // الشاشات الجديدة
                '/app_settings': (context) => const AppSettingsScreen(),
                '/privacy_settings': (context) => const PrivacySettingsScreen(),

                '/product_details':
                    (context) => ProductDetailsScreen(
                      product:
                          ModalRoute.of(context)!.settings.arguments
                              as ProductModel,
                    ),
                '/cart': (context) => const CartScreen(),
                '/admin/add-product': (context) => const AddEditProductScreen(),
                '/admin/inventory':
                    (context) => const InventoryManagementScreen(),
                '/change-password': (context) => const ChangePasswordScreen(),
                '/security-activity':
                    (context) => const SecurityActivityScreen(),
              },
            ),
          );
        },
      ),
    );
  }

  Widget _getInitialScreen() {
    if (!_onboardingComplete) {
      return const OnboardingScreen();
    }

    try {
      if (widget.coreServices.authService.isInitialized &&
          widget.coreServices.authService.isAuthenticated) {
        return const HomeScreen();
      } else {
        return const WelcomeScreen();
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة المصادقة: $e');
      return const WelcomeScreen();
    }
  }
}

class ErrorApp extends StatelessWidget {
  final String? errorMessage;

  const ErrorApp({super.key, this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.red,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
      ),
      locale: const Locale('ar', ''),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('خطأ في التطبيق'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 80),
                const SizedBox(height: 20),
                const Text(
                  'حدث خطأ في تهيئة التطبيق',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                if (errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      errorMessage ?? '',
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    // إعادة تشغيل التطبيق
                    main();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 30,
                      vertical: 15,
                    ),
                  ),
                  child: const Text(
                    'إعادة المحاولة',
                    style: TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
