/// نموذج إعدادات التطبيق للمستخدم
/// يتوافق مع جدول user_app_settings في قاعدة البيانات
class UserAppSettingsModel {
  final String id;
  final String userId;
  final String theme;
  final bool pushEnabled;
  final bool locationEnabled;
  final bool biometricEnabled;
  final bool autoBackup;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool enableHighContrast;
  final bool enableReducedMotion;
  final String fontFamily;
  final String timezone;
  final int textSize;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserAppSettingsModel({
    required this.id,
    required this.userId,
    this.theme = 'light',
    this.pushEnabled = true,
    this.locationEnabled = false,
    this.biometricEnabled = false,
    this.autoBackup = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.enableHighContrast = false,
    this.enableReducedMotion = false,
    this.fontFamily = 'Cairo',
    this.timezone = 'Africa/Cairo',
    this.textSize = 16,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نموذج من بيانات JSON
  factory UserAppSettingsModel.fromJson(Map<String, dynamic> json) {
    return UserAppSettingsModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      theme: json['theme'] as String? ?? 'light',
      pushEnabled: json['push_enabled'] as bool? ?? true,
      locationEnabled: json['location_enabled'] as bool? ?? false,
      biometricEnabled: json['biometric_enabled'] as bool? ?? false,
      autoBackup: json['auto_backup'] as bool? ?? true,
      soundEnabled: json['sound_enabled'] as bool? ?? true,
      vibrationEnabled: json['vibration_enabled'] as bool? ?? true,
      enableHighContrast: json['enable_high_contrast'] as bool? ?? false,
      enableReducedMotion: json['enable_reduced_motion'] as bool? ?? false,
      fontFamily: json['font_family'] as String? ?? 'Cairo',
      timezone: json['timezone'] as String? ?? 'Africa/Cairo',
      textSize: json['text_size'] as int? ?? 16,
      createdAt:
          DateTime.tryParse(json['created_at'] as String? ?? '') ??
          DateTime.now(),
      updatedAt:
          DateTime.tryParse(json['updated_at'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'theme': theme,
      'push_enabled': pushEnabled,
      'location_enabled': locationEnabled,
      'biometric_enabled': biometricEnabled,
      'auto_backup': autoBackup,
      'sound_enabled': soundEnabled,
      'vibration_enabled': vibrationEnabled,
      'enable_high_contrast': enableHighContrast,
      'enable_reduced_motion': enableReducedMotion,
      'font_family': fontFamily,
      'timezone': timezone,
      'text_size': textSize,
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  UserAppSettingsModel copyWith({
    String? id,
    String? userId,
    String? theme,
    bool? pushEnabled,
    bool? locationEnabled,
    bool? biometricEnabled,
    bool? autoBackup,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? enableHighContrast,
    bool? enableReducedMotion,
    String? fontFamily,
    String? timezone,
    int? textSize,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserAppSettingsModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      theme: theme ?? this.theme,
      pushEnabled: pushEnabled ?? this.pushEnabled,
      locationEnabled: locationEnabled ?? this.locationEnabled,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      autoBackup: autoBackup ?? this.autoBackup,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      enableHighContrast: enableHighContrast ?? this.enableHighContrast,
      enableReducedMotion: enableReducedMotion ?? this.enableReducedMotion,
      fontFamily: fontFamily ?? this.fontFamily,
      timezone: timezone ?? this.timezone,
      textSize: textSize ?? this.textSize,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// إنشاء إعدادات تطبيق افتراضية لمستخدم جديد
  static UserAppSettingsModel createDefault(String userId) {
    final now = DateTime.now();
    // استخدام UUID عشوائي بدلاً من معرف المستخدم
    final uuid = DateTime.now().millisecondsSinceEpoch.toString();
    return UserAppSettingsModel(
      id: uuid,
      userId: userId,
      theme: 'light',
      pushEnabled: true,
      locationEnabled: false,
      biometricEnabled: false,
      autoBackup: true,
      soundEnabled: true,
      vibrationEnabled: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      fontFamily: 'Cairo',
      timezone: 'Africa/Cairo',
      textSize: 16, // Default text size
      createdAt: now,
      updatedAt: now,
    );
  }

  /// إرجاع قائمة بالسمات المدعومة
  static List<String> get supportedThemes => ['light', 'dark', 'auto'];
}
