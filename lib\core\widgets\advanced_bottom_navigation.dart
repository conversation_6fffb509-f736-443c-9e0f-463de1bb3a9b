import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/theme/gradients.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';

/// شريط تنقل سفلي متقدم واحترافي
/// يمكن استخدامه في جميع شاشات التطبيق مع دعم للتأثيرات البصرية المتقدمة
class AdvancedBottomNavigation extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;
  final List<BottomNavItem> items;
  final Widget? floatingActionButton;
  final LinearGradient? gradient;
  final bool useGlassEffect;
  final double height;
  final bool showLabels;
  final bool showSelectedLabels;
  final bool showNotificationBadges;
  final bool enableItemAnimation;
  final bool roundedTop;
  final Color? backgroundColor;

  const AdvancedBottomNavigation({
    super.key,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.items,
    this.floatingActionButton,
    this.gradient,
    this.useGlassEffect = true,
    this.height = 70,
    this.showLabels = true,
    this.showSelectedLabels = true,
    this.showNotificationBadges = true,
    this.enableItemAnimation = true,
    this.roundedTop = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final isTabletOrLarger = !ResponsiveHelper.isMobile(context);
    final bottomPadding = ResponsiveHelper.getPadding(
      context,
      mobile: 8.0,
      tablet: 12.0,
      desktop: 16.0,
    );
    final effectiveHeight = isTabletOrLarger ? height + 10 : height;
    final effectiveGradient = AppGradients.blueWaveGradient;

    return Container(
      height: effectiveHeight,
      decoration: BoxDecoration(
        gradient: effectiveGradient,
        color: backgroundColor,
        borderRadius:
            roundedTop
                ? const BorderRadius.only(
                  topLeft: Radius.circular(28),
                  topRight: Radius.circular(28),
                )
                : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.18),
            blurRadius: 24,
            offset: const Offset(0, -8),
            spreadRadius: 2,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius:
            roundedTop
                ? const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                )
                : BorderRadius.zero,
        child: BackdropFilter(
          filter:
              useGlassEffect
                  ? ImageFilter.blur(sigmaX: 10, sigmaY: 10)
                  : ImageFilter.blur(sigmaX: 0, sigmaY: 0),
          child: Padding(
            padding: EdgeInsets.only(
              left: bottomPadding,
              right: bottomPadding,
              bottom: bottomPadding,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: _buildNavItems(context),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildNavItems(BuildContext context) {
    final List<Widget> navItems = [];
    final hasFab = floatingActionButton != null;
    final middleIndex = items.length ~/ 2;

    for (int i = 0; i < items.length; i++) {
      // إذا كان هناك زر عائم وكنا في منتصف العناصر، أضف مساحة
      if (hasFab && i == middleIndex) {
        navItems.add(const SizedBox(width: 40));
      }

      navItems.add(
        _buildNavItem(
          context: context,
          item: items[i],
          isSelected: selectedIndex == i,
          onTap: () => onItemSelected(i),
        ),
      );
    }

    return navItems;
  }

  Widget _buildNavItem({
    required BuildContext context,
    required BottomNavItem item,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return _AnimatedScaleOnTap(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                // أيقونة العنصر مع تأثيرات متحركة
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Colors.white.withOpacity(0.3)
                            : Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow:
                        isSelected
                            ? [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                            : null,
                  ),
                  child: Icon(
                    item.icon,
                    color:
                        isSelected
                            ? Colors.white
                            : Colors.white.withOpacity(0.7),
                    size: 24,
                  ),
                ),

                // شارة الإشعارات
                if (showNotificationBadges && item.notificationCount > 0)
                  Positioned(
                    top: -5,
                    right: -5,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: AppTheme.errorColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.errorColor.withOpacity(0.5),
                            blurRadius: 4,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 18,
                        minHeight: 18,
                      ),
                      child: Center(
                        child: Text(
                          item.notificationCount > 9
                              ? '9+'
                              : '${item.notificationCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // نص العنصر
            if ((showLabels && !isSelected) ||
                (showSelectedLabels && isSelected)) ...[
              const SizedBox(height: 4),
              Text(
                item.label,
                style: TextStyle(
                  color:
                      isSelected ? Colors.white : Colors.white.withOpacity(0.7),
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// أضف كلاس _AnimatedScaleOnTap هنا إذا لم يكن معرفاً
class _AnimatedScaleOnTap extends StatefulWidget {
  final Widget child;
 
  const _AnimatedScaleOnTap({
    required this.child,
  
  });
  @override
  State<_AnimatedScaleOnTap> createState() => _AnimatedScaleOnTapState();
}

class _AnimatedScaleOnTapState extends State<_AnimatedScaleOnTap> {
  bool _pressed = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _pressed = true),
      onTapUp: (_) => setState(() => _pressed = false),
      onTapCancel: () => setState(() => _pressed = false),
      child: AnimatedScale(
            scale: _pressed ? 0.92 : 1.0,
        duration: const Duration(milliseconds: 120),
        curve: Curves.easeOut,
        child: widget.child,
      ),
    );
  }
}

/// نموذج عنصر شريط التنقل السفلي
class BottomNavItem {
  final IconData icon;
  final String label;
  final int notificationCount;
  final String? route;

  const BottomNavItem({
    required this.icon,
    required this.label,
    this.notificationCount = 0,
    this.route,
  });
}
