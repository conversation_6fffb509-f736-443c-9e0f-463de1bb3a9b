import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:motorcycle_parts_shop/core/services/advertisement_service.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/currency_service.dart';
import 'package:motorcycle_parts_shop/core/services/error_handler.dart';
import 'package:motorcycle_parts_shop/core/services/image_recognition_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/services/vibration_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/home_screen_shortcuts.dart';
import 'package:motorcycle_parts_shop/core/widgets/shimmer_loading.dart';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:motorcycle_parts_shop/screens/product/product_details_screen.dart';
import 'package:motorcycle_parts_shop/screens/product/product_list_screen.dart';
import 'package:motorcycle_parts_shop/screens/profile/profile_screen.dart';
import 'package:motorcycle_parts_shop/screens/profile/wishlist_screen.dart';
import 'package:provider/provider.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  final AdvertisementService _adService = AdvertisementService();
  // وحدة تحكم للرسوم المتحركة
  late AnimationController _animationController;
  // رسوم متحركة للتلاشي
  late Animation<double> _fadeAnimation;
  // رسوم متحركة للانزلاق
  late Animation<Offset> _slideAnimation;

  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  List<CategoryModel> _categories = [];
  List<ProductModel> _bestSellingProducts = [];
  List<ProductModel> _offerProducts = [];
  List<ProductModel> _suggestedProducts = [];
  int _currentNavIndex = 0;

  @override
  void initState() {
    super.initState();
    // تهيئة وحدة تحكم الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // تهيئة رسوم متحركة للتلاشي
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // تهيئة رسوم متحركة للانزلاق
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutQuart),
    );

    // تحميل البيانات الأولية للشاشة
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // دالة لتحميل البيانات الأساسية للشاشة (الفئات، المنتجات الأكثر مبيعًا، العروض، المنتجات المقترحة، الإعلانات)
  Future<void> _loadData() async {
    // التأكد من أن الواجهة لا تزال موجودة قبل تحديث الحالة
    if (!mounted) return;
    // تعيين حالة التحميل إلى "جار التحميل"
    setState(() {
      _isLoading = true;
    });

    int retryCount = 0;
    const maxRetries = 3; // أقصى عدد لمحاولات إعادة التحميل
    const retryDelay = Duration(
      seconds: 2,
    ); // مدة الانتظار بين محاولات إعادة التحميل

    // حلقة لمحاولة تحميل البيانات عدة مرات في حالة الفشل
    while (retryCount < maxRetries) {
      try {
        final supabaseService = Provider.of<AuthSupabaseService>(
          context,
          listen: false,
        );
        final productService = Provider.of<ProductService>(
          context,
          listen: false,
        );

        if (!supabaseService.isInitialized) {
          await supabaseService.initialize();
        }

        final futures = await Future.wait([
          productService.getCategories(),
          productService.getBestSellingProducts(page: 0),
          productService.getFeaturedProducts(page: 0),
          productService.getPopularProducts(),
          _adService.getActiveAdvertisements(),
        ]);

        if (futures[0].isEmpty &&
            futures[1].isEmpty &&
            futures[2].isEmpty &&
            futures[3].isEmpty &&
            futures[4].isEmpty) {
          throw Exception('No data available');
        }

        setState(() {
          _categories = futures[0] as List<CategoryModel>;
          _bestSellingProducts = futures[1] as List<ProductModel>;
          _offerProducts = futures[2] as List<ProductModel>;
          _suggestedProducts = futures[3] as List<ProductModel>;
          _isLoading = false;
        });
        // تشغيل الرسوم المتحركة بعد تحميل البيانات بنجاح
        _animationController.forward();
        return; // الخروج من الدالة بعد النجاح
      } catch (e) {
        retryCount++;
        // Error loading data (attempt $retryCount): $e

        // إذا وصلت محاولات إعادة التحميل إلى الحد الأقصى، يتم عرض رسالة خطأ للمستخدم
        if (retryCount == maxRetries) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'فشل تحميل البيانات، يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا',
              ),
              backgroundColor: AppTheme.errorColor,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                onPressed: () {
                  _loadData();
                },
              ),
            ),
          );
        } else {
          await Future.delayed(retryDelay);
        }
      }
    }

    // في حالة فشل جميع محاولات إعادة التحميل، يتم تعيين حالة التحميل إلى "غير جار التحميل"
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // شعار التطبيق
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.motorcycle,
                      color: AppTheme.textLightColor,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // عنوان التطبيق
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'مرحباً بك',
                          style: AppTheme.cardSubtitle.copyWith(
                            color: AppTheme.textLightColor.withOpacity(0.9),
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          'متجر قطع الغيار',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      _buildHeaderButton(
                        icon: Icons.search_rounded,
                        onPressed:
                            () => Navigator.pushNamed(
                              context,
                              '/advanced-search',
                            ),
                        tooltip: 'البحث المتقدم',
                      ),
                      const SizedBox(width: 8),
                      _buildCartButton(),
                      const SizedBox(width: 8),

                      _buildHeaderButton(
                        icon: Icons.notifications_outlined,
                        onPressed: () {
                          // تفعيل الاهتزاز عند فتح شاشة الإشعارات
                          final vibrationService =
                              Provider.of<VibrationService>(
                                context,
                                listen: false,
                              );
                          vibrationService.lightVibrate();

                          Navigator.pushNamed(context, '/notifications');
                        },
                        tooltip: 'الإشعارات',
                        showBadge: true,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const AppLoadingIndicator(message: 'جاري تحميل المنتجات...')
              : RefreshIndicator(
                onRefresh: _loadData,
                color: Theme.of(context).primaryColor,
                child: CustomScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  slivers: [
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: EdgeInsets.all(
                          ResponsiveHelper.getPadding(context),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSearchBar(),
                            const SizedBox(height: 24),
                            // الاختصارات السريعة
                            const HomeScreenShortcuts(),
                            const SizedBox(height: 24),
                            _buildSectionTitle('الأقسام'),
                            const SizedBox(height: 16),
                            _buildCategoriesGrid(),
                            const SizedBox(height: 24),

                            _buildSectionTitle('الأكثر مبيعاً'),
                            const SizedBox(height: 16),
                            _buildProductsRow(_bestSellingProducts),
                            const SizedBox(height: 24),
                            _buildSectionTitle('العروض والخصومات'),
                            const SizedBox(height: 16),
                            _buildOffersRow(),
                            const SizedBox(height: 24),
                            _buildSectionTitle('مقترح لك'),
                            const SizedBox(height: 16),
                            _buildProductsRow(_suggestedProducts),
                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppTheme.cardColor, AppTheme.backgroundColor],
          ),
          boxShadow: AppTheme.elevatedShadow,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentNavIndex,
            onTap: (index) {
              setState(() {
                _currentNavIndex = index;
              });

              if (index == 0) {
                return;
              } else if (index == 1) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProductListScreen(),
                  ),
                ).then((_) {
                  setState(() {
                    _currentNavIndex = 0;
                  });
                });
              } else if (index == 2) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WishlistScreen(),
                  ),
                ).then((_) {
                  setState(() {
                    _currentNavIndex = 0;
                  });
                });
              } else if (index == 3) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                ).then((_) {
                  setState(() {
                    _currentNavIndex = 0;
                  });
                });
              }
            },
            elevation: 0,
            backgroundColor: Colors.transparent,
            selectedItemColor: AppTheme.primaryColor,
            unselectedItemColor: AppTheme.textTertiaryColor,
            selectedLabelStyle: AppTheme.buttonText.copyWith(
              fontSize: 12,
              color: AppTheme.primaryColor,
            ),
            unselectedLabelStyle: AppTheme.cardSubtitle.copyWith(
              fontSize: 11,
              color: AppTheme.textTertiaryColor,
            ),
            type: BottomNavigationBarType.fixed,
            items: [
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.home_outlined, 0),
                activeIcon: _buildNavIcon(
                  Icons.home_rounded,
                  0,
                  isActive: true,
                ),
                label: 'الرئيسية',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.grid_view_outlined, 1),
                activeIcon: _buildNavIcon(
                  Icons.grid_view_rounded,
                  1,
                  isActive: true,
                ),
                label: 'الأقسام',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.favorite_border_rounded, 2),
                activeIcon: _buildNavIcon(
                  Icons.favorite_rounded,
                  2,
                  isActive: true,
                ),
                label: 'المفضلة',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.person_outline_rounded, 3),
                activeIcon: _buildNavIcon(
                  Icons.person_rounded,
                  3,
                  isActive: true,
                ),
                label: 'حسابي',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final fontSize = ResponsiveHelper.getFontSize(context, 16);
        final hintFontSize = ResponsiveHelper.getFontSize(context, 14);

        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: padding * 0.2),
              decoration: BoxDecoration(
                gradient: AppTheme.cardGradient,
                borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                boxShadow: AppTheme.elevatedShadow,
                border: Border.all(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  // أيقونة البحث
                  Padding(
                    padding: EdgeInsets.only(
                      left: ResponsiveHelper.isMobile(context) ? 12 : 16,
                      right: ResponsiveHelper.isMobile(context) ? 8 : 12,
                    ),
                    child: Icon(
                      Icons.search_rounded,
                      color: AppTheme.primaryColor,
                      size: ResponsiveHelper.isMobile(context) ? 20 : 24,
                    ),
                  ),

                  // حقل البحث
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      style: AppTheme.cardTitle.copyWith(
                        fontSize: fontSize,
                        color: AppTheme.textPrimaryColor,
                      ),
                      decoration: InputDecoration(
                        hintText: 'ابحث عن قطع غيار، محركات، فرامل...',
                        hintStyle: AppTheme.cardSubtitle.copyWith(
                          color: AppTheme.textTertiaryColor,
                          fontSize: hintFontSize,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          vertical:
                              ResponsiveHelper.isMobile(context) ? 12 : 16,
                        ),
                      ),
                      onSubmitted: (value) {
                        if (value.isNotEmpty) {
                          _searchProducts(value);
                        }
                      },
                    ),
                  ),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      _buildSearchActionButton(
                        icon: Icons.camera_alt_rounded,
                        onPressed: _searchByImage,
                        tooltip: 'البحث بالصورة',
                        gradient: AppTheme.secondaryGradient,
                      ),
                      const SizedBox(width: 8),
                      _buildSearchActionButton(
                        icon: Icons.tune_rounded,
                        onPressed: () => _showFilterOptions(context),
                        tooltip: 'تصفية متقدمة',
                        gradient: AppTheme.accentGradient,
                      ),
                      const SizedBox(width: 12),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required LinearGradient gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(10),
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Icon(icon, color: AppTheme.textLightColor, size: 20),
          ),
        ),
      ),
    );
  }

  /// البحث عن المنتجات باستخدام النص
  /// [query] نص البحث المدخل من المستخدم
  void _searchProducts(String query) {
    if (query.isEmpty) return;

    // تنقل إلى شاشة قائمة المنتجات مع تمرير مصطلح البحث
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductListScreen(searchQuery: query),
      ),
    );
  }

  /// البحث عن المنتجات باستخدام الصورة
  Future<void> _searchByImage() async {
    try {
      // التقاط صورة باستخدام الكاميرا
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image == null) return;

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const AlertDialog(
            content: AppLoadingIndicator(
              message: 'جاري تحليل الصورة...',
              size: 30,
            ),
          );
        },
      );

      // تحليل الصورة والبحث عن المنتجات المشابهة
      final imageFile = File(image.path);
      final supabaseService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      final imageRecognitionService = ImageRecognitionService(
        ProductService(supabaseService.client),
      );
      final products = await imageRecognitionService.searchProductsByImage(
        imageFile,
      );

      // إغلاق مؤشر التحميل
      Navigator.pop(context);

      if (products.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لم يتم العثور على منتجات مشابهة'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
        return;
      }

      // الانتقال إلى شاشة قائمة المنتجات مع تمرير المنتجات التي تم العثور عليها
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ProductListScreen(
                title: 'نتائج البحث بالصورة',
                products: products,
              ),
        ),
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      ErrorHandler.handleError(
        context,
        e,
        customMessage: 'حدث خطأ أثناء تحليل الصورة',
        onRetry: _searchByImage,
      );
      // خطأ في البحث بالصورة: $e
    }
  }

  Widget _buildSectionTitle(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        TextButton(
          onPressed: () {
            _navigateToSeeAllItems(title);
          },
          child: const Text('عرض الكل'),
        ),
      ],
    );
  }

  void _navigateToSeeAllItems(String sectionTitle) {
    // تحديد نوع المنتجات المراد عرضها بناءً على عنوان القسم
    List<ProductModel> productsToShow = [];
    String screenTitle = '';

    if (sectionTitle == 'الأكثر مبيعاً') {
      productsToShow = _bestSellingProducts;
      screenTitle = 'الأكثر مبيعاً';
    } else if (sectionTitle == 'العروض والخصومات') {
      productsToShow = _offerProducts;
      screenTitle = 'العروض والخصومات';
    } else if (sectionTitle == 'مقترح لك') {
      productsToShow = _suggestedProducts;
      screenTitle = 'منتجات مقترحة لك';
    } else if (sectionTitle == 'الأقسام') {
      // الانتقال إلى شاشة الأقسام
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const ProductListScreen()),
      );
      return;
    }

    // الانتقال إلى شاشة قائمة المنتجات مع تمرير المنتجات المحددة
    if (productsToShow.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ProductListScreen(
                title: screenTitle,
                products: productsToShow,
              ),
        ),
      );
    }
  }

  Widget _buildCategoriesGrid() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final crossAxisCount = ResponsiveHelper.getGridColumns(
          context,
          maxColumns: 4,
        );
        final aspectRatio = ResponsiveHelper.getAspectRatio(
          context,
          mobile: 1.2,
          tablet: 1.1,
          desktop: 1.0,
        );
        final spacing = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: aspectRatio,
            crossAxisSpacing: spacing,
            mainAxisSpacing: spacing,
          ),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            return _buildCategoryCard(category);
          },
        );
      },
    );
  }

  Widget _buildCategoryCard(CategoryModel category) {
    return InkWell(
      onTap: () => _navigateToCategoryProducts(category),
      borderRadius: AppTheme.borderRadiusLarge,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: AppTheme.borderRadiusLarge,
          boxShadow: AppTheme.smoothShadow,
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.05),
              Theme.of(context).colorScheme.secondary.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Hero(
              tag: 'category_icon_${category.id}',
              child: Icon(
                category.iconData ?? Icons.category_rounded,
                size: 40,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCategoryProducts(CategoryModel category) {
    final supabaseService = Provider.of<AuthSupabaseService>(
      context,
      listen: false,
    );
    final productService = Provider.of<ProductService>(context, listen: false);

    // عرض مؤشر التحميل
    setState(() {
      _isLoading = true;
    });

    // جلب منتجات الفئة من قاعدة البيانات
    productService
        .getProducts(page: 0, category: category.id)
        .then((products) {
          setState(() {
            _isLoading = false;
          });

          // الانتقال إلى شاشة قائمة المنتجات مع تمرير المنتجات
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => ProductListScreen(
                    title: 'منتجات ${category.name}',
                    products: products,
                  ),
            ),
          );
        })
        .catchError((error) {
          setState(() {
            _isLoading = false;
          });

          // عرض رسالة خطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ أثناء تحميل المنتجات: $error'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        });
  }

  Widget _buildProductsRow(List<ProductModel> products) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final height = ResponsiveHelper.getListItemHeight(context);

        return SizedBox(
          height: height,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(
                  horizontal: ResponsiveHelper.getPadding(
                    context,
                    mobile: 4,
                    tablet: 8,
                    desktop: 12,
                  ),
                ),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return Hero(
                    tag: 'product_${product.id}',
                    child: _buildProductCard(product),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOffersRow() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final height = ResponsiveHelper.getListItemHeight(context);

        return SizedBox(
          height: height,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(
                  horizontal: ResponsiveHelper.getPadding(
                    context,
                    mobile: 4,
                    tablet: 8,
                    desktop: 12,
                  ),
                ),
                itemCount: _offerProducts.length,
                itemBuilder: (context, index) {
                  final product = _offerProducts[index];
                  return Hero(
                    tag: 'offer_${product.id}',
                    child: _buildProductCard(product, isOffer: true),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  /// الانتقال إلى صفحة تفاصيل المنتج
  void _navigateToProductDetails(ProductModel product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(product: product),
      ),
    );
  }

  /// عرض خيارات التصفية
  void _showFilterOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تصفية المنتجات',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              Text('الفئات', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children:
                    _categories
                        .map((category) => _buildFilterChip(category.name))
                        .toList(),
              ),
              const SizedBox(height: 16),
              Text(
                'نطاق السعر',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildFilterChip('أقل من 100 جنيه'),
                  _buildFilterChip('100 - 500 جنيه'),
                  _buildFilterChip('500 - 1000 جنيه'),
                  _buildFilterChip('أكثر من 1000 جنيه'),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'الترتيب حسب',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildFilterChip('الأحدث'),
                  _buildFilterChip('السعر: من الأقل إلى الأعلى'),
                  _buildFilterChip('السعر: من الأعلى إلى الأقل'),
                  _buildFilterChip('الأكثر مبيعاً'),
                ],
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // تطبيق التصفية وعرض النتائج
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم تطبيق التصفية')),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('تطبيق'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// إنشاء شريحة تصفية
  Widget _buildFilterChip(String label) {
    return FilterChip(
      label: Text(label),
      selected: false, // يمكن تغييرها لتتبع الحالة المحددة
      onSelected: (selected) {
        setState(() {
          // تنفيذ التصفية
        });
      },
    );
  }

  Widget _buildProductCard(ProductModel product, {bool isOffer = false}) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final cardWidth = ResponsiveHelper.getCardWidth(
          context,
          minWidth: 180,
          maxWidth: 250,
        );
        final margin = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );

        return Container(
          width: cardWidth,
          margin: EdgeInsets.only(right: margin),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: InkWell(
            onTap: () {
              _navigateToProductDetails(product);
            },
            borderRadius: BorderRadius.circular(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                      child: Image.asset(
                        product.imageUrlList.isNotEmpty
                            ? product.imageUrlList.first
                            : 'assets/images/placeholder.png',
                        height: 150,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder:
                            (context, error, stackTrace) => Container(
                              height: 150,
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              child: const Center(
                                child: Icon(
                                  Icons.image_not_supported_outlined,
                                  size: 40,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                            ),
                      ),
                    ),
                    if (product.discountPrice != null && isOffer)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.accentColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${(((product.price - product.discountPrice!) / product.price) * 100).toInt()}% خصم',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.categoryName ?? 'غير مصنف',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(children: [
      
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            product.brand,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[800],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  product.isAvailable
                                      ? Colors.green[100]
                                      : Colors.red[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              product.isAvailable ? 'متوفر' : 'غير متوفر',
                              style: TextStyle(
                                fontSize: 12,
                                color:
                                    product.isAvailable
                                        ? Colors.green[800]
                                        : Colors.red[800],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'SKU: ${product.sku}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          if (product.discountPrice != null) ...[
                            Expanded(
                              child: Text(
                                '${CurrencyService().formatPrice(product.discountPrice!)} ج.م',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: AppTheme.primaryColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${CurrencyService().formatPrice(product.price)} ج.م',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ] else
                            Expanded(
                              child: Text(
                                '${CurrencyService().formatPrice(product.price)} ج.م',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: AppTheme.primaryColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 🎨 دوال بناء العناصر المتطورة للهيدر
  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    bool showBadge = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: Stack(
        children: [
          IconButton(
            icon: Icon(icon, color: AppTheme.textLightColor, size: 22),
            onPressed: onPressed,
            tooltip: tooltip,
            splashRadius: 20,
            padding: const EdgeInsets.all(8),
          ),
          if (showBadge)
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.textLightColor, width: 1),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCartButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: Stack(
        children: [
          IconButton(
            icon: Icon(
              Icons.shopping_cart_outlined,
              color: AppTheme.textLightColor,
              size: 22,
            ),
            onPressed: () => Navigator.pushNamed(context, '/cart'),
            tooltip: 'سلة التسوق',
            splashRadius: 20,
            padding: const EdgeInsets.all(8),
          ),
          Positioned(
            right: 6,
            top: 6,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppTheme.secondaryColor,
                shape: BoxShape.circle,
                border: Border.all(color: AppTheme.textLightColor, width: 1),
              ),
              constraints: const BoxConstraints(minWidth: 18, minHeight: 18),
              child: Text(
                '0', // يجب استبداله بالعدد الفعلي للمنتجات في السلة
                style: TextStyle(
                  color: AppTheme.textLightColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 🎨 دالة بناء أيقونات التنقل المتطورة
  Widget _buildNavIcon(IconData icon, int index, {bool isActive = false}) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: isActive ? AppTheme.primaryGradient : null,
        borderRadius: BorderRadius.circular(12),
        boxShadow:
            isActive
                ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
      ),
      child: Icon(
        icon,
        size: 24,
        color: isActive ? AppTheme.textLightColor : AppTheme.textTertiaryColor,
      ),
    );
  }
}

// Ensuring no trailing characters after the class closing brace
