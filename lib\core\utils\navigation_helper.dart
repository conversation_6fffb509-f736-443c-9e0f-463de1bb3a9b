import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// مساعد التنقل الآمن مع معالجة الأخطاء
class NavigationHelper {
  /// التنقل الآمن باستخدام route name
  static Future<T?> pushNamed<T extends Object?>(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool showErrorOnFailure = true,
  }) async {
    try {
      debugPrint('🧭 التنقل إلى: $routeName');
      
      final result = await Navigator.pushNamed<T>(
        context,
        routeName,
        arguments: arguments,
      );
      
      debugPrint('✅ تم التنقل بنجاح إلى: $routeName');
      return result;
      
    } catch (e) {
      debugPrint('❌ خطأ في التنقل إلى $routeName: $e');
      
      if (showErrorOnFailure && context.mounted) {
        _showNavigationError(context, routeName, e.toString());
      }
      
      return null;
    }
  }

  /// التنقل الآمن مع استبدال الشاشة الحالية
  static Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    BuildContext context,
    String routeName, {
    Object? arguments,
    TO? result,
    bool showErrorOnFailure = true,
  }) async {
    try {
      debugPrint('🔄 استبدال الشاشة بـ: $routeName');
      
      final navigationResult = await Navigator.pushReplacementNamed<T, TO>(
        context,
        routeName,
        arguments: arguments,
        result: result,
      );
      
      debugPrint('✅ تم استبدال الشاشة بنجاح بـ: $routeName');
      return navigationResult;
      
    } catch (e) {
      debugPrint('❌ خطأ في استبدال الشاشة بـ $routeName: $e');
      
      if (showErrorOnFailure && context.mounted) {
        _showNavigationError(context, routeName, e.toString());
      }
      
      return null;
    }
  }

  /// التنقل الآمن مع MaterialPageRoute
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget screen, {
    String? screenName,
    bool showErrorOnFailure = true,
  }) async {
    try {
      final name = screenName ?? screen.runtimeType.toString();
      debugPrint('🧭 التنقل إلى شاشة: $name');
      
      final result = await Navigator.push<T>(
        context,
        MaterialPageRoute(builder: (context) => screen),
      );
      
      debugPrint('✅ تم التنقل بنجاح إلى شاشة: $name');
      return result;
      
    } catch (e) {
      final name = screenName ?? screen.runtimeType.toString();
      debugPrint('❌ خطأ في التنقل إلى شاشة $name: $e');
      
      if (showErrorOnFailure && context.mounted) {
        _showNavigationError(context, name, e.toString());
      }
      
      return null;
    }
  }

  /// التنقل الآمن مع استبدال الشاشة باستخدام MaterialPageRoute
  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget screen, {
    String? screenName,
    TO? result,
    bool showErrorOnFailure = true,
  }) async {
    try {
      final name = screenName ?? screen.runtimeType.toString();
      debugPrint('🔄 استبدال الشاشة بـ: $name');
      
      final navigationResult = await Navigator.pushReplacement<T, TO>(
        context,
        MaterialPageRoute(builder: (context) => screen),
        result: result,
      );
      
      debugPrint('✅ تم استبدال الشاشة بنجاح بـ: $name');
      return navigationResult;
      
    } catch (e) {
      final name = screenName ?? screen.runtimeType.toString();
      debugPrint('❌ خطأ في استبدال الشاشة بـ $name: $e');
      
      if (showErrorOnFailure && context.mounted) {
        _showNavigationError(context, name, e.toString());
      }
      
      return null;
    }
  }

  /// العودة للشاشة السابقة بشكل آمن
  static void pop<T extends Object?>(
    BuildContext context, [
    T? result,
  ]) {
    try {
      if (Navigator.canPop(context)) {
        debugPrint('⬅️ العودة للشاشة السابقة');
        Navigator.pop<T>(context, result);
        debugPrint('✅ تم العودة بنجاح');
      } else {
        debugPrint('⚠️ لا يمكن العودة - لا توجد شاشة سابقة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في العودة للشاشة السابقة: $e');
    }
  }

  /// العودة إلى شاشة معينة
  static void popUntil(
    BuildContext context,
    String routeName,
  ) {
    try {
      debugPrint('⬅️ العودة إلى: $routeName');
      
      Navigator.popUntil(context, ModalRoute.withName(routeName));
      
      debugPrint('✅ تم العودة بنجاح إلى: $routeName');
    } catch (e) {
      debugPrint('❌ خطأ في العودة إلى $routeName: $e');
    }
  }

  /// إزالة جميع الشاشات والانتقال إلى شاشة جديدة
  static Future<T?> pushNamedAndRemoveUntil<T extends Object?>(
    BuildContext context,
    String routeName,
    String untilRouteName, {
    Object? arguments,
    bool showErrorOnFailure = true,
  }) async {
    try {
      debugPrint('🔄 إزالة جميع الشاشات والانتقال إلى: $routeName');
      
      final result = await Navigator.pushNamedAndRemoveUntil<T>(
        context,
        routeName,
        ModalRoute.withName(untilRouteName),
        arguments: arguments,
      );
      
      debugPrint('✅ تم التنقل بنجاح مع إزالة الشاشات إلى: $routeName');
      return result;
      
    } catch (e) {
      debugPrint('❌ خطأ في التنقل مع إزالة الشاشات إلى $routeName: $e');
      
      if (showErrorOnFailure && context.mounted) {
        _showNavigationError(context, routeName, e.toString());
      }
      
      return null;
    }
  }

  /// التحقق من إمكانية العودة
  static bool canPop(BuildContext context) {
    return Navigator.canPop(context);
  }

  /// عرض رسالة خطأ التنقل
  static void _showNavigationError(
    BuildContext context,
    String routeName,
    String error,
  ) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'خطأ في التنقل إلى $routeName',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          textColor: Colors.white,
          onPressed: () {
            // يمكن إضافة منطق إعادة المحاولة هنا
          },
        ),
      ),
    );
  }

  /// التنقل مع animation مخصص
  static Future<T?> pushWithCustomTransition<T extends Object?>(
    BuildContext context,
    Widget screen, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    TransitionType transitionType = TransitionType.slideFromRight,
    String? screenName,
  }) async {
    try {
      final name = screenName ?? screen.runtimeType.toString();
      debugPrint('🎬 التنقل مع animation إلى: $name');

      final result = await Navigator.push<T>(
        context,
        PageRouteBuilder<T>(
          pageBuilder: (context, animation, secondaryAnimation) => screen,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildTransition(
              animation,
              child,
              transitionType,
              curve,
            );
          },
        ),
      );

      debugPrint('✅ تم التنقل بنجاح مع animation إلى: $name');
      return result;
    } catch (e) {
      final name = screenName ?? screen.runtimeType.toString();
      debugPrint('❌ خطأ في التنقل مع animation إلى $name: $e');
      return null;
    }
  }

  /// بناء التأثير المرئي للتنقل
  static Widget _buildTransition(
    Animation<double> animation,
    Widget child,
    TransitionType type,
    Curve curve,
  ) {
    final curvedAnimation = CurvedAnimation(parent: animation, curve: curve);

    switch (type) {
      case TransitionType.fade:
        return FadeTransition(opacity: curvedAnimation, child: child);
      
      case TransitionType.scale:
        return ScaleTransition(scale: curvedAnimation, child: child);
      
      case TransitionType.slideFromRight:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
      
      case TransitionType.slideFromLeft:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
      
      case TransitionType.slideFromBottom:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
    }
  }
}

/// أنواع التأثيرات المرئية للتنقل
enum TransitionType {
  fade,
  scale,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
}
