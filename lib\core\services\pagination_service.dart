import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة ترقيم الصفحات والتحميل الكسول
/// تستخدم هذه الخدمة لتحسين أداء استعلامات قاعدة البيانات عند التعامل مع مجموعات البيانات الكبيرة
/// توفر آليات لتحميل البيانات بشكل تدريجي (كسول) وترقيم الصفحات
///
/// المسؤوليات الرئيسية:
/// - تنفيذ استعلامات مُحسّنة مع ترقيم الصفحات
/// - توفير آلية للتحميل الكسول للبيانات
/// - تحسين أداء التطبيق عند التعامل مع قوائم كبيرة من المنتجات
/// - تقليل استهلاك الذاكرة والبيانات

class PaginationService extends ChangeNotifier {
  static final PaginationService _instance = PaginationService._internal();
  final AuthSupabaseService _supabaseService = AuthSupabaseService();
  final SupabaseClient _client;

  // حجم الصفحة الافتراضي (عدد العناصر في كل صفحة)
  static const int defaultPageSize = 20;

  // مؤشرات حالة التحميل
  bool _isLoading = false;
  bool _hasMoreData = true;
  String? _error;

  // الحالة الحالية
  int _currentPage = 0;
  List<ProductModel> _loadedProducts = [];

  // المرشحات الحالية
  String? _categoryId;
  String? _company;
  String _sortBy = 'name';
  bool _sortAscending = true;
  String _searchQuery = '';

  final Map<String, int> _pageSizes = {};

  // الوصول للكائن الوحيد (Singleton)
  factory PaginationService() {
    return _instance;
  }

  PaginationService._internal() : _client = Supabase.instance.client;

  // الوصول للبيانات والحالة
  bool get isLoading => _isLoading;
  bool get hasMoreData => _hasMoreData;
  String? get error => _error;
  List<ProductModel> get products => _loadedProducts;
  int get currentPage => _currentPage;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (!_supabaseService.isInitialized) {
      await _supabaseService.initialize();
    }
  }

  /// إعادة تعيين حالة التحميل والبدء من جديد
  void reset() {
    _currentPage = 0;
    _loadedProducts = [];
    _hasMoreData = true;
    _error = null;
    notifyListeners();
  }

  /// تعيين مرشحات البحث والفرز
  void setFilters({
    String? categoryId,
    String? company,
    String sortBy = 'name',
    bool sortAscending = true,
    String searchQuery = '',
  }) {
    bool filtersChanged = false;

    if (_categoryId != categoryId) {
      _categoryId = categoryId;
      filtersChanged = true;
    }

    if (_company != company) {
      _company = company;
      filtersChanged = true;
    }

    if (_sortBy != sortBy) {
      _sortBy = sortBy;
      filtersChanged = true;
    }

    if (_sortAscending != sortAscending) {
      _sortAscending = sortAscending;
      filtersChanged = true;
    }

    if (_searchQuery != searchQuery) {
      _searchQuery = searchQuery;
      filtersChanged = true;
    }

    if (filtersChanged) {
      reset();
    }
  }

  int getPageSize(String resourceType) {
    return _pageSizes[resourceType] ?? defaultPageSize;
  }

  void setPageSize(String resourceType, int size) {
    _pageSizes[resourceType] = size;
  }

  /// تحميل الصفحة التالية من المنتجات مع تحسينات الأداء
  Future<void> loadNextPage({int pageSize = defaultPageSize}) async {
    // تجنب التحميل المتزامن المتعدد
    if (_isLoading || !_hasMoreData) return;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // حساب نطاق الصفحة
      final int startIndex = _currentPage * pageSize;
      final int endIndex = startIndex + pageSize - 1;

      // تحسين الاستعلام: تحديد الأعمدة المطلوبة فقط لتقليل حجم البيانات المنقولة
      // استخدام select(*) يجلب كل الأعمدة، بينما تحديد الأعمدة المطلوبة فقط يحسن الأداء
      final String selectColumns = '''
        id, name, price, discount_price, image_url, 
        category_id, company, is_available, created_at, 
        stock_quantity, is_featured
      ''';

      // بناء الاستعلام المحسن
      dynamic query = _client
          .from(AppConstants.productsTable)
          .select(selectColumns);

      // إضافة المرشحات مع تحسين الأداء
      if (_categoryId != null && _categoryId!.isNotEmpty) {
        query = query.eq('category_id', _categoryId);
      }

      if (_company != null && _company!.isNotEmpty) {
        query = query.eq('company', _company);
      }

      // تحسين البحث النصي باستخدام فهارس النص
      if (_searchQuery.isNotEmpty) {
        // استخدام تقنية أكثر كفاءة للبحث النصي
        // استخدام ilike بدلاً من like لتجاهل حالة الأحرف
        // استخدام % في بداية ونهاية النص للبحث في أي مكان في النص
        query = query.or(
          'name.ilike.%$_searchQuery%,description.ilike.%$_searchQuery%',
        );
      }

      // إضافة الترتيب مع مراعاة الفهارس
      switch (_sortBy) {
        case 'name':
          query = query.order('name', ascending: _sortAscending);
          break;
        case 'price':
          query = query.order('price', ascending: _sortAscending);
          break;
        // Rating case removed as per requirements
        case 'newest':
          query = query.order('created_at', ascending: !_sortAscending);
          break;
        default:
          query = query.order('name', ascending: true);
      }

      // تحسين ترقيم الصفحات باستخدام تقنية الحدود بدلاً من النطاق
      // هذا يحسن الأداء خاصة مع مجموعات البيانات الكبيرة
      if (_currentPage > 0 && _loadedProducts.isNotEmpty) {
        // استخدام آخر عنصر كنقطة بداية للصفحة التالية
        final lastProduct = _loadedProducts.last;
        String columnName;
        dynamic columnValue;
        bool ascending;

        // تحديد العمود والقيمة بناءً على الترتيب الحالي
        switch (_sortBy) {
          case 'price':
            columnName = 'price';
            columnValue = lastProduct.price;
            ascending = _sortAscending;
            break;
          // Rating case removed as per requirements
          case 'newest':
            columnName = 'created_at';
            columnValue = lastProduct.createdAt.toIso8601String();
            ascending = !_sortAscending;
            break;
          case 'name':
          default:
            columnName = 'name';
            columnValue = lastProduct.name;
            ascending = _sortAscending;
        }

        // استخدام gt (أكبر من) أو lt (أقل من) بناءً على اتجاه الترتيب
        if (ascending) {
          query = query.gt(columnName, columnValue);
        } else {
          query = query.lt(columnName, columnValue);
        }

        // تحديد عدد العناصر المطلوبة
        query = query.limit(pageSize);
      } else {
        // للصفحة الأولى، استخدم النطاق التقليدي
        query = query.range(startIndex, endIndex);
      }

      // تنفيذ الاستعلام
      final response = await query;

      // تحويل النتائج إلى نماذج منتجات
      final List<ProductModel> newProducts =
          response
              .map<ProductModel>((json) => ProductModel.fromJson(json))
              .toList();

      // تحديث الحالة
      _loadedProducts.addAll(newProducts);
      _currentPage++;

      // التحقق مما إذا كانت هناك المزيد من البيانات
      _hasMoreData = newProducts.length == pageSize;

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      debugPrint('خطأ في تحميل الصفحة التالية: $e');
    }
  }

  /// البحث عن المنتجات مع ترقيم الصفحات
  Future<void> searchProducts(
    String query, {
    int pageSize = defaultPageSize,
  }) async {
    setFilters(searchQuery: query);
    await loadNextPage(pageSize: pageSize);
  }

  /// تحميل المنتجات حسب الفئة مع ترقيم الصفحات
  Future<void> loadProductsByCategory(
    String categoryId, {
    int pageSize = defaultPageSize,
  }) async {
    setFilters(categoryId: categoryId);
    await loadNextPage(pageSize: pageSize);
  }

  /// تحميل المنتجات حسب الشركة المصنعة مع ترقيم الصفحات
  Future<void> loadProductsByCompany(
    String company, {
    int pageSize = defaultPageSize,
  }) async {
    setFilters(company: company);
    await loadNextPage(pageSize: pageSize);
  }

  Future<List<Map<String, dynamic>>> getPaginatedData({
    required String table,
    required int page,
    String? filter,
    String? sortBy,
    bool ascending = true,
    Map<String, dynamic>? eqFilters,
    Map<String, dynamic>? rangeFilters,
    List<String>? selectColumns,
    int? customPageSize,
  }) async {
    try {
      final pageSize = customPageSize ?? getPageSize(table);
      final from = (page - 1) * pageSize;
      final to = from + pageSize - 1;

      dynamic query = _client
          .from(table)
          .select(selectColumns?.join(',') ?? '*');

      // تطبيق الفلاتر
      if (filter != null) {
        query = query.ilike('name', '%$filter%');
      }

      if (eqFilters != null) {
        eqFilters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      if (rangeFilters != null) {
        rangeFilters.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            if (value.containsKey('gte')) {
              query = query.gte(key, value['gte']);
            }
            if (value.containsKey('lte')) {
              query = query.lte(key, value['lte']);
            }
          }
        });
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        query = query.order(sortBy, ascending: ascending);
      }

      // تطبيق التصفح
      query = query.range(from, to);

      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('خطأ في جلب البيانات: $e');
      rethrow;
    }
  }

  Future<int> getTotalCount({
    required String table,
    String? filter,
    Map<String, dynamic>? eqFilters,
    Map<String, dynamic>? rangeFilters,
  }) async {
    try {
      PostgrestFilterBuilder query = _client.from(table).select('id');

      if (filter != null) {
        query = query.ilike('name', '%$filter%');
      }

      if (eqFilters != null) {
        eqFilters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      if (rangeFilters != null) {
        rangeFilters.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            if (value.containsKey('gte')) {
              query = query.gte(key, value['gte']);
            }
            if (value.containsKey('lte')) {
              query = query.lte(key, value['lte']);
            }
          }
        });
      }

      final response = await query;
      return response.length;
    } catch (e) {
      debugPrint('خطأ في حساب العدد الإجمالي: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getPaginatedResponse({
    required String table,
    required int page,
    String? filter,
    String? sortBy,
    bool ascending = true,
    Map<String, dynamic>? eqFilters,
    Map<String, dynamic>? rangeFilters,
    List<String>? selectColumns,
    int? customPageSize,
  }) async {
    try {
      final data = await getPaginatedData(
        table: table,
        page: page,
        filter: filter,
        sortBy: sortBy,
        ascending: ascending,
        eqFilters: eqFilters,
        rangeFilters: rangeFilters,
        selectColumns: selectColumns,
        customPageSize: customPageSize,
      );

      final total = await getTotalCount(
        table: table,
        filter: filter,
        eqFilters: eqFilters,
        rangeFilters: rangeFilters,
      );

      final pageSize = customPageSize ?? getPageSize(table);
      final totalPages = (total / pageSize).ceil();

      return {
        'data': data,
        'total': total,
        'page': page,
        'pageSize': pageSize,
        'totalPages': totalPages,
        'hasNextPage': page < totalPages,
        'hasPreviousPage': page > 1,
      };
    } catch (e) {
      debugPrint('خطأ في جلب الاستجابة: $e');
      rethrow;
    }
  }
}
