import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/models/user/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// نموذج بيانات التخزين المؤقت
class CachedData<T> {
  final T data;
  final DateTime expiryTime;
  int accessCount = 0;
  DateTime lastAccess = DateTime.now();
  final DateTime createdAt;

  CachedData(this.data, this.expiryTime, {DateTime? createdAt})
    : createdAt = createdAt ?? DateTime.now();

  bool get isExpired => DateTime.now().isAfter(expiryTime);

  T access() {
    accessCount++;
    lastAccess = DateTime.now();
    return data;
  }
}

/// خدمة التخزين الموحدة - تدمج جميع وظائف التخزين والتخزين المؤقت في مكان واحد
class UnifiedStorage extends ChangeNotifier {
  static final UnifiedStorage _instance = UnifiedStorage._internal();
  factory UnifiedStorage() => _instance;
  UnifiedStorage._internal() {
    // سيتم تهيئة client لاحقاً في initialize()
    _setupCleanupTimer();
  }

  // ===================================================================
  // المتغيرات الأساسية
  // ===================================================================

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  SupabaseClient? client;

  // ذاكرة التخزين المؤقت المحسنة
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, CachedData> _advancedCache = {};
  final Duration _cacheDuration = const Duration(hours: 24);
  final Set<String> _pendingSync = {};
  final int _cacheLimit = 1000;
  bool _autoCleanEnabled = true;

  // مؤقت التنظيف الدوري
  Timer? _cleanupTimer;
  static const Duration _cleanupInterval = Duration(minutes: 5);

  // إعدادات التخزين المؤقت الإضافية
  static const Duration _defaultExpiry = Duration(minutes: 15);
  static const int _maxMemoryCacheSizeBytes = 10 * 1024 * 1024; // 10 MB
  int _estimatedSizeBytes = 0;

  // حالة المزامنة
  final bool _isSyncing = false;
  final bool _isOfflineMode = false;

  // إحصائيات الذاكرة المؤقتة
  int _cacheHits = 0;
  int _cacheMisses = 0;

  // ===================================================================
  // Getters
  // ===================================================================

  bool get isInitialized => _isInitialized;
  bool get isOfflineMode => _isOfflineMode;
  bool get isSyncing => _isSyncing;
  bool get autoCleanEnabled => _autoCleanEnabled;
  int get cacheSize => _memoryCache.length;
  int get cacheLimit => _cacheLimit;
  bool get isCacheFull => cacheSize >= cacheLimit;
  bool get isHealthy => _isInitialized && cacheSize < cacheLimit;

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw UnifiedStorageException(
        'UnifiedStorage not initialized. Call initialize() first.',
      );
    }
    return _prefs!;
  }

  // ===================================================================
  // التهيئة
  // ===================================================================

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      
      // تهيئة Supabase client إذا كان متاحاً
      try {
        client = Supabase.instance.client;
      } catch (e) {
        debugPrint('⚠️ Supabase غير متاح بعد، سيتم استخدام التخزين المحلي فقط');
        client = null;
      }
      
      _autoCleanEnabled = _prefs!.getBool('auto_clean_enabled') ?? true;
      _isInitialized = true;
      _setupCleanupTimer(); // إعادة تهيئة مؤقت التنظيف
      notifyListeners();
      debugPrint('✅ تم تهيئة خدمة التخزين الموحدة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة التخزين: $e');
      throw UnifiedStorageException(
        'فشل في تهيئة خدمة التخزين: ${e.toString()}',
      );
    }
  }

  /// إعداد مؤقت التنظيف الدوري
  void _setupCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) {
      _cleanupExpiredEntries();
      _logCacheStats();
    });
  }

  // ===================================================================
  // وظائف التخزين الأساسية
  // ===================================================================

  /// حفظ نص
  Future<bool> setString(String key, String value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await prefs.setString(key, value);
      if (success) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ النص: $e');
      return false;
    }
  }

  /// جلب نص
  String? getString(String key) {
    if (!_isInitialized) return null;
    try {
      // فحص الذاكرة المؤقتة أولاً
      if (_memoryCache.containsKey(key)) {
        final timestamp = _cacheTimestamps[key];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < _cacheDuration) {
          return _memoryCache[key] as String?;
        }
      }

      final value = prefs.getString(key);
      if (value != null) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب النص: $e');
      return null;
    }
  }

  /// حفظ رقم صحيح
  Future<bool> setInt(String key, int value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await prefs.setInt(key, value);
      if (success) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ الرقم: $e');
      return false;
    }
  }

  /// جلب رقم صحيح
  int? getInt(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        final timestamp = _cacheTimestamps[key];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < _cacheDuration) {
          return _memoryCache[key] as int?;
        }
      }

      final value = prefs.getInt(key);
      if (value != null) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب الرقم: $e');
      return null;
    }
  }

  /// حفظ قيمة منطقية
  Future<bool> setBool(String key, bool value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await prefs.setBool(key, value);
      if (success) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ القيمة المنطقية: $e');
      return false;
    }
  }

  /// جلب قيمة منطقية
  bool? getBool(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        final timestamp = _cacheTimestamps[key];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < _cacheDuration) {
          return _memoryCache[key] as bool?;
        }
      }

      final value = prefs.getBool(key);
      if (value != null) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب القيمة المنطقية: $e');
      return null;
    }
  }

  /// حفظ رقم عشري
  Future<bool> setDouble(String key, double value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await prefs.setDouble(key, value);
      if (success) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ الرقم العشري: $e');
      return false;
    }
  }

  /// جلب رقم عشري
  double? getDouble(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        final timestamp = _cacheTimestamps[key];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < _cacheDuration) {
          return _memoryCache[key] as double?;
        }
      }

      final value = prefs.getDouble(key);
      if (value != null) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب الرقم العشري: $e');
      return null;
    }
  }

  /// حفظ قائمة نصوص
  Future<bool> setStringList(String key, List<String> value) async {
    if (!_isInitialized) await initialize();
    try {
      final success = await prefs.setStringList(key, value);
      if (success) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حفظ قائمة النصوص: $e');
      return false;
    }
  }

  /// جلب قائمة نصوص
  List<String>? getStringList(String key) {
    if (!_isInitialized) return null;
    try {
      if (_memoryCache.containsKey(key)) {
        final timestamp = _cacheTimestamps[key];
        if (timestamp != null &&
            DateTime.now().difference(timestamp) < _cacheDuration) {
          return _memoryCache[key] as List<String>?;
        }
      }

      final value = prefs.getStringList(key);
      if (value != null) {
        _memoryCache[key] = value;
        _cacheTimestamps[key] = DateTime.now();
      }
      return value;
    } catch (e) {
      debugPrint('خطأ في جلب قائمة النصوص: $e');
      return null;
    }
  }

  /// حفظ كائن JSON
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      debugPrint('خطأ في حفظ JSON: $e');
      return false;
    }
  }

  /// جلب كائن JSON
  dynamic getJson(String key) {
    try {
      final data = getString(key);
      if (data == null) return null;
      return jsonDecode(data);
    } catch (e) {
      debugPrint('Error getting JSON data: $e');
      return null;
    }
  }

  /// حذف مفتاح
  Future<bool> remove(String key) async {
    if (!_isInitialized) await initialize();
    try {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      return await prefs.remove(key);
    } catch (e) {
      debugPrint('خطأ في حذف المفتاح: $e');
      return false;
    }
  }

  /// التحقق من وجود مفتاح
  bool containsKey(String key) {
    if (!_isInitialized) return false;
    return _memoryCache.containsKey(key) || prefs.containsKey(key);
  }

  /// الحصول على جميع المفاتيح
  Set<String> getKeys() {
    if (!_isInitialized) return {};
    try {
      return prefs.getKeys();
    } catch (e) {
      debugPrint('خطأ في الحصول على المفاتيح: $e');
      return {};
    }
  }

  /// مسح جميع البيانات
  Future<bool> clear() async {
    if (!_isInitialized) await initialize();
    try {
      _memoryCache.clear();
      _cacheTimestamps.clear();
      _pendingSync.clear();
      return await prefs.clear();
    } catch (e) {
      debugPrint('خطأ في مسح البيانات: $e');
      return false;
    }
  }

  // ===================================================================
  // وظائف المصادقة والمستخدم
  // ===================================================================

  /// حفظ رمز الوصول
  Future<void> saveToken(String token) async {
    try {
      if (!_isInitialized) await initialize();
      await setString(AppConstants.storageKeyToken, token);
    } catch (e) {
      debugPrint('Error saving token: $e');
      throw UnifiedStorageException('فشل في حفظ رمز الوصول: ${e.toString()}');
    }
  }

  /// جلب رمز الوصول
  Future<String?> getToken() async {
    try {
      if (!_isInitialized) await initialize();
      return getString(AppConstants.storageKeyToken);
    } catch (e) {
      debugPrint('Error getting token: $e');
      return null;
    }
  }

  /// حذف رمز الوصول
  Future<void> deleteToken() async {
    try {
      await remove(AppConstants.storageKeyToken);
    } catch (e) {
      debugPrint('Error deleting token: $e');
      throw UnifiedStorageException('فشل في حذف رمز الوصول: ${e.toString()}');
    }
  }

  /// حفظ بيانات المستخدم
  Future<void> saveUser(UserModel user) async {
    try {
      if (!_isInitialized) await initialize();
      final userJson = jsonEncode(user.toJson());
      final encryptedData = _encryptSensitiveData(userJson);
      await setString('user_data', encryptedData);
      _logSecureOperation('save_user', {'user_id': user.id});
    } catch (e) {
      _logSecurityEvent('save_user_failed', {'error': e.toString()});
      debugPrint('Error saving user: $e');
      throw UnifiedStorageException(
        'فشل في حفظ بيانات المستخدم: ${e.toString()}',
      );
    }
  }

  /// حفظ بيانات تسجيل الدخول
  Future<void> saveLoginCredentials(String email, String password) async {
    try {
      if (!_isInitialized) await initialize();
      final credentials = {
        'email': email,
        'password': password,
        'saved_at': DateTime.now().toIso8601String(),
      };
      final credentialsJson = jsonEncode(credentials);
      final encryptedData = _encryptSensitiveData(credentialsJson);
      await setString('login_credentials', encryptedData);
      _logSecureOperation('save_credentials', {'email': email});
    } catch (e) {
      _logSecurityEvent('save_credentials_failed', {'error': e.toString()});
      debugPrint('Error saving login credentials: $e');
      throw UnifiedStorageException(
        'فشل في حفظ بيانات تسجيل الدخول: ${e.toString()}',
      );
    }
  }

  /// جلب بيانات تسجيل الدخول المحفوظة
  Map<String, String>? getSavedLoginCredentials() {
    try {
      if (!_isInitialized) {
        debugPrint('UnifiedStorage not initialized for credentials');
        return null;
      }
      final encryptedData = getString('login_credentials');
      if (encryptedData == null) return null;

      final decryptedData = _decryptSensitiveData(encryptedData);
      final credentials = jsonDecode(decryptedData) as Map<String, dynamic>;

      return {
        'email': credentials['email'] as String,
        'password': credentials['password'] as String,
      };
    } catch (e) {
      debugPrint('Error getting saved login credentials: $e');
      return null;
    }
  }

  /// حذف بيانات تسجيل الدخول المحفوظة
  Future<void> clearSavedLoginCredentials() async {
    try {
      if (!_isInitialized) await initialize();
      await remove('login_credentials');
      _logSecureOperation('clear_credentials', {});
    } catch (e) {
      debugPrint('Error clearing login credentials: $e');
    }
  }

  /// جلب بيانات المستخدم
  UserModel? getUser() {
    try {
      if (!_isInitialized) {
        debugPrint('UnifiedStorage not initialized, returning null');
        return null;
      }
      final encryptedData = getString('user_data');
      if (encryptedData != null) {
        final userJson = _decryptSensitiveData(encryptedData);
        return UserModel.fromJson(jsonDecode(userJson));
      }
      return null;
    } catch (e) {
      _logSecurityEvent('get_user_failed', {'error': e.toString()});
      debugPrint('Error getting user: $e');
      return null;
    }
  }

  /// حذف بيانات المستخدم
  Future<void> deleteUser() async {
    try {
      await remove('user_data');
    } catch (e) {
      debugPrint('Error deleting user: $e');
      throw UnifiedStorageException(
        'فشل في حذف بيانات المستخدم: ${e.toString()}',
      );
    }
  }

  // ===================================================================
  // وظائف الإعدادات
  // ===================================================================

  /// حفظ السمة
  Future<void> saveTheme(String themeMode) async {
    try {
      await setString(AppConstants.storageKeyTheme, themeMode);
    } catch (e) {
      debugPrint('Error saving theme: $e');
      throw UnifiedStorageException('فشل في حفظ وضع السمة: ${e.toString()}');
    }
  }

  /// جلب السمة
  String? getTheme() {
    try {
      return getString(AppConstants.storageKeyTheme);
    } catch (e) {
      debugPrint('Error getting theme: $e');
      return null;
    }
  }

  /// حفظ اللغة
  Future<void> saveLanguage(String languageCode) async {
    try {
      await setString(AppConstants.storageKeyLanguage, languageCode);
    } catch (e) {
      debugPrint('Error saving language: $e');
      throw UnifiedStorageException(
        'فشل في حفظ اللغة المفضلة: ${e.toString()}',
      );
    }
  }

  /// جلب اللغة
  String? getLanguage() {
    try {
      return getString(AppConstants.storageKeyLanguage);
    } catch (e) {
      debugPrint('Error getting language: $e');
      return null;
    }
  }

  /// فحص التشغيل الأول
  bool isFirstTime() {
    try {
      return getBool(AppConstants.storageKeyFirstTime) ?? true;
    } catch (e) {
      debugPrint('Error checking first time: $e');
      return true;
    }
  }

  /// تعيين عدم التشغيل الأول
  Future<void> setNotFirstTime() async {
    try {
      await setBool(AppConstants.storageKeyFirstTime, false);
    } catch (e) {
      debugPrint('Error setting not first time: $e');
      throw UnifiedStorageException(
        'فشل في تعيين حالة التشغيل الأول: ${e.toString()}',
      );
    }
  }

  // ===================================================================
  // وظائف إضافية مطلوبة للخدمات الأخرى
  // ===================================================================

  /// حفظ وقت آخر مصادقة
  Future<void> saveLastAuthenticationTime(int? timestamp) async {
    try {
      if (timestamp != null) {
        await setInt('last_authentication_time', timestamp);
      } else {
        await remove('last_authentication_time');
      }
    } catch (e) {
      debugPrint('Error saving last authentication time: $e');
    }
  }

  /// جلب وقت آخر مصادقة
  int? getLastAuthenticationTime() {
    try {
      return getInt('last_authentication_time');
    } catch (e) {
      debugPrint('Error getting last authentication time: $e');
      return null;
    }
  }

  /// حفظ نص (alias)
  Future<void> saveString(String key, String value) async {
    await setString(key, value);
  }

  /// حفظ int (alias)
  Future<void> saveInt(String key, int value) async {
    await setInt(key, value);
  }

  /// حفظ double (alias)
  Future<void> saveDouble(String key, double value) async {
    await setDouble(key, value);
  }

  /// حفظ bool (alias)
  Future<void> saveBool(String key, bool value) async {
    await setBool(key, value);
  }

  /// الحصول على double (alias)
  double? getDoubleValue(String key) {
    return getDouble(key);
  }

  /// الحصول على int (alias)
  int? getIntValue(String key) {
    return getInt(key);
  }

  /// تنظيف شامل
  Future<void> clearAll() async {
    try {
      await clear();
      clearCache();
      _pendingSync.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all data: $e');
      throw UnifiedStorageException('فشل في مسح البيانات: ${e.toString()}');
    }
  }

  // ===================================================================
  // وظائف الأمان والتشفير
  // ===================================================================

  /// تشفير البيانات الحساسة باستخدام مفتاح آمن
  String _encryptSensitiveData(String data) {
    try {
      // الحصول على مفتاح التشفير من متغيرات البيئة
      final encryptionKey =
          dotenv.env['ENCRYPTION_KEY'] ?? 'default_encryption_key';

      // إنشاء مفتاح آمن باستخدام SHA-256
      final keyBytes = utf8.encode(encryptionKey);
      final key = keyBytes.length > 32 ? keyBytes.sublist(0, 32) : keyBytes;

      // إنشاء متجه التهيئة (IV) من الـ 16 بايت الأولى من المفتاح
      final iv = key.length > 16 ? key.sublist(0, 16) : key;

      // تشفير البيانات باستخدام XOR
      final dataBytes = utf8.encode(data);
      final result = List<int>.filled(dataBytes.length, 0);

      for (int i = 0; i < dataBytes.length; i++) {
        result[i] = dataBytes[i] ^ iv[i % iv.length];
      }

      // تشفير إضافي باستخدام مفتاح التشفير
      for (int i = 0; i < result.length; i++) {
        result[i] = result[i] ^ key[i % key.length];
      }

      // تحويل النتيجة إلى Base64
      return base64Encode(result);
    } catch (e) {
      debugPrint('خطأ في تشفير البيانات: $e');
      // في حالة الخطأ، نستخدم تشفيراً بسيطاً
      final bytes = utf8.encode(data);
      return base64Encode(bytes);
    }
  }

  /// فك تشفير البيانات الحساسة باستخدام مفتاح آمن
  String _decryptSensitiveData(String encryptedData) {
    try {
      // الحصول على مفتاح التشفير من متغيرات البيئة
      final encryptionKey =
          dotenv.env['ENCRYPTION_KEY'] ?? 'default_encryption_key';

      // إنشاء مفتاح آمن باستخدام SHA-256
      final keyBytes = utf8.encode(encryptionKey);
      final key = keyBytes.length > 32 ? keyBytes.sublist(0, 32) : keyBytes;

      // إنشاء متجه التهيئة (IV) من الـ 16 بايت الأولى من المفتاح
      final iv = key.length > 16 ? key.sublist(0, 16) : key;

      // فك تشفير البيانات
      final dataBytes = base64Decode(encryptedData);
      final result = List<int>.filled(dataBytes.length, 0);

      // فك التشفير الإضافي أولاً
      for (int i = 0; i < dataBytes.length; i++) {
        result[i] = dataBytes[i] ^ key[i % key.length];
      }

      // فك التشفير الأساسي
      for (int i = 0; i < result.length; i++) {
        result[i] = result[i] ^ iv[i % iv.length];
      }

      return utf8.decode(result);
    } catch (e) {
      debugPrint('خطأ في فك تشفير البيانات: $e');
      try {
        // محاولة فك التشفير البسيط في حالة الخطأ
        final bytes = base64Decode(encryptedData);
        return utf8.decode(bytes);
      } catch (e2) {
        debugPrint('خطأ في فك التشفير البسيط: $e2');
        return encryptedData; // إرجاع البيانات كما هي في حالة الخطأ
      }
    }
  }

  /// حفظ مفتاح API بشكل آمن
  Future<bool> saveApiKey(String keyName, String keyValue) async {
    try {
      if (!_isInitialized) await initialize();

      // تشفير مفتاح API
      final encryptedKey = _encryptSensitiveData(keyValue);

      // حفظ المفتاح المشفر
      final success = await setString('api_key_$keyName', encryptedKey);

      if (success) {
        _logSecureOperation('save_api_key', {'key_name': keyName});
        debugPrint('تم حفظ مفتاح API بنجاح: $keyName');
      }

      return success;
    } catch (e) {
      _logSecurityEvent('save_api_key_failed', {
        'key_name': keyName,
        'error': e.toString(),
      });
      debugPrint('خطأ في حفظ مفتاح API: $e');
      return false;
    }
  }

  /// الحصول على مفتاح API المخزن بشكل آمن
  String? getApiKey(String keyName) {
    try {
      if (!_isInitialized) {
        debugPrint('خدمة التخزين غير مهيأة للحصول على مفتاح API');
        return null;
      }

      // الحصول على المفتاح المشفر
      final encryptedKey = getString('api_key_$keyName');
      if (encryptedKey == null) return null;

      // فك تشفير المفتاح
      final decryptedKey = _decryptSensitiveData(encryptedKey);
      _logSecureOperation('get_api_key', {'key_name': keyName});

      return decryptedKey;
    } catch (e) {
      _logSecurityEvent('get_api_key_failed', {
        'key_name': keyName,
        'error': e.toString(),
      });
      debugPrint('خطأ في الحصول على مفتاح API: $e');
      return null;
    }
  }

  /// حذف مفتاح API
  Future<bool> deleteApiKey(String keyName) async {
    try {
      if (!_isInitialized) await initialize();

      final success = await remove('api_key_$keyName');

      if (success) {
        _logSecureOperation('delete_api_key', {'key_name': keyName});
        debugPrint('تم حذف مفتاح API بنجاح: $keyName');
      }

      return success;
    } catch (e) {
      _logSecurityEvent('delete_api_key_failed', {
        'key_name': keyName,
        'error': e.toString(),
      });
      debugPrint('خطأ في حذف مفتاح API: $e');
      return false;
    }
  }

  /// تسجيل العمليات الآمنة
  void _logSecureOperation(String operation, Map<String, dynamic> metadata) {
    try {
      final logEntry = {
        'operation': operation,
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': metadata,
      };
      debugPrint('Secure operation: ${jsonEncode(logEntry)}');
    } catch (e) {
      debugPrint('Error logging secure operation: $e');
    }
  }

  /// تسجيل الأحداث الأمنية
  void _logSecurityEvent(String event, Map<String, dynamic> details) {
    try {
      final logEntry = {
        'event': event,
        'timestamp': DateTime.now().toIso8601String(),
        'details': details,
      };
      debugPrint('Security event: ${jsonEncode(logEntry)}');
    } catch (e) {
      debugPrint('Error logging security event: $e');
    }
  }

  // ===================================================================
  // وظائف إدارة الذاكرة المؤقتة المحسنة
  // ===================================================================

  /// مسح الذاكرة المؤقتة
  void clearCache() {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    _advancedCache.clear();
    _cacheHits = 0;
    _cacheMisses = 0;
    _estimatedSizeBytes = 0;
    debugPrint('🧹 تم مسح الذاكرة المؤقتة');
  }

  /// تحسين الذاكرة المؤقتة
  void optimizeCache() {
    if (isCacheFull) {
      _cleanupExpiredEntries();
      if (isCacheFull) {
        _evictLeastRecentlyUsed();
      }
    }

    // تحسين حجم الذاكرة
    if (_estimatedSizeBytes > _maxMemoryCacheSizeBytes) {
      _enforceMemoryLimits();
    }
  }

  /// مسح البيانات منتهية الصلاحية
  void _cleanupExpiredEntries() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    // تنظيف الذاكرة المؤقتة القديمة
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheDuration) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    // تنظيف الذاكرة المؤقتة المتقدمة
    final expiredAdvancedKeys = <String>[];
    for (final entry in _advancedCache.entries) {
      if (entry.value.isExpired) {
        expiredAdvancedKeys.add(entry.key);
      }
    }

    for (final key in expiredAdvancedKeys) {
      _advancedCache.remove(key);
    }

    final totalExpired = expiredKeys.length + expiredAdvancedKeys.length;
    if (totalExpired > 0) {
      debugPrint('🧹 تم مسح $totalExpired عنصر منتهي الصلاحية');
    }
  }

  /// إزالة العناصر الأقل استخداماً من الذاكرة المؤقتة
  void _evictLeastRecentlyUsed() {
    if (_advancedCache.isEmpty) return;

    // ترتيب العناصر حسب عدد مرات الوصول وتاريخ آخر وصول
    final sortedEntries =
        _advancedCache.entries.toList()..sort((a, b) {
          // أولاً نقارن عدد مرات الوصول
          final countComparison = a.value.accessCount.compareTo(
            b.value.accessCount,
          );
          if (countComparison != 0) return countComparison;

          // إذا كان عدد مرات الوصول متساوياً، نقارن تاريخ آخر وصول
          return a.value.lastAccess.compareTo(b.value.lastAccess);
        });

    // إزالة 20% من العناصر الأقل استخداماً
    final itemsToRemove = (sortedEntries.length * 0.2).ceil();
    for (int i = 0; i < itemsToRemove && i < sortedEntries.length; i++) {
      _advancedCache.remove(sortedEntries[i].key);
    }

    debugPrint('تم إزالة $itemsToRemove عنصر من الذاكرة المؤقتة (LRU)');
    _recalculateCacheSize();
  }

  /// فرض حدود الذاكرة
  void _enforceMemoryLimits() {
    // حذف العناصر الأقدم إذا تجاوزنا الحد الأقصى للعدد
    while (_advancedCache.length > _cacheLimit) {
      final oldestKey =
          _advancedCache.entries
              .reduce(
                (a, b) => a.value.createdAt.isBefore(b.value.createdAt) ? a : b,
              )
              .key;
      _advancedCache.remove(oldestKey);
    }

    // حذف العناصر إذا تجاوزنا الحد الأقصى للحجم
    while (_estimatedSizeBytes > _maxMemoryCacheSizeBytes &&
        _advancedCache.isNotEmpty) {
      final oldestKey =
          _advancedCache.entries
              .reduce(
                (a, b) => a.value.createdAt.isBefore(b.value.createdAt) ? a : b,
              )
              .key;
      _advancedCache.remove(oldestKey);
      _recalculateCacheSize();
    }
  }

  /// تحديث حجم التخزين المؤقت
  void _updateCacheSize(String key, CachedData entry) {
    // تقدير تقريبي لحجم البيانات
    final keySize = key.length * 2; // UTF-16
    final valueSize = _estimateValueSize(entry.data);
    _estimatedSizeBytes += keySize + valueSize;
  }

  /// إعادة حساب حجم التخزين المؤقت
  void _recalculateCacheSize() {
    _estimatedSizeBytes = 0;
    for (final entry in _advancedCache.entries) {
      final keySize = entry.key.length * 2;
      final valueSize = _estimateValueSize(entry.value.data);
      _estimatedSizeBytes += keySize + valueSize;
    }
  }

  /// تقدير حجم القيمة
  int _estimateValueSize(dynamic value) {
    if (value == null) return 0;
    if (value is String) return value.length * 2;
    if (value is List) return value.length * 50; // تقدير تقريبي
    if (value is Map) return value.length * 100; // تقدير تقريبي
    return 50; // تقدير افتراضي
  }

  /// تسجيل إحصائيات التخزين المؤقت
  void _logCacheStats() {
    final hitRate =
        _cacheHits + _cacheMisses > 0
            ? (_cacheHits / (_cacheHits + _cacheMisses) * 100)
            : 0;
    debugPrint(
      '📊 إحصائيات التخزين المؤقت: '
      'العناصر: ${_advancedCache.length}, '
      'الحجم: ${(_estimatedSizeBytes / 1024).toStringAsFixed(1)} KB, '
      'معدل النجاح: ${hitRate.toStringAsFixed(1)}%',
    );
  }

  /// الحصول على بيانات مخزنة مؤقتاً مع دعم التحديث التلقائي
  Future<T> getCachedData<T>(
    String key,
    Future<T> Function() fetcher, {
    Duration? expiry,
    bool forceRefresh = false,
  }) async {
    final cacheDuration = expiry ?? _cacheDuration;

    // التحقق من وجود البيانات في الذاكرة المؤقتة
    if (!forceRefresh && _advancedCache.containsKey(key)) {
      final cachedItem = _advancedCache[key] as CachedData<T>;
      if (!cachedItem.isExpired) {
        _cacheHits++;
        return cachedItem.access();
      }
    }

    // إذا لم تكن البيانات موجودة أو منتهية الصلاحية، نجلبها
    _cacheMisses++;
    try {
      final data = await fetcher();
      _advancedCache[key] = CachedData<T>(
        data,
        DateTime.now().add(cacheDuration),
      );

      _updateCacheSize(key, _advancedCache[key] as CachedData);

      // تنظيف الذاكرة المؤقتة إذا كانت ممتلئة
      if (_autoCleanEnabled &&
          (_advancedCache.length > _cacheLimit ||
              _estimatedSizeBytes > _maxMemoryCacheSizeBytes)) {
        optimizeCache();
      }

      return data;
    } catch (e) {
      debugPrint('خطأ في جلب البيانات لـ $key: $e');

      // إذا كانت البيانات موجودة في الذاكرة المؤقتة، نستخدمها حتى لو كانت منتهية الصلاحية
      if (_advancedCache.containsKey(key)) {
        debugPrint('استخدام بيانات منتهية الصلاحية لـ $key بسبب خطأ في الجلب');
        return (_advancedCache[key] as CachedData<T>).access();
      }

      rethrow;
    }
  }

  /// تخزين بيانات في الذاكرة المؤقتة يدوياً
  void setCachedData<T>(String key, T data, {Duration? expiry}) {
    final cacheDuration = expiry ?? _cacheDuration;
    final entry = CachedData<T>(data, DateTime.now().add(cacheDuration));

    _advancedCache[key] = entry;
    _updateCacheSize(key, entry);

    // تنظيف الذاكرة المؤقتة إذا كانت ممتلئة
    if (_autoCleanEnabled &&
        (_advancedCache.length > _cacheLimit ||
            _estimatedSizeBytes > _maxMemoryCacheSizeBytes)) {
      optimizeCache();
    }
  }

  /// إزالة بيانات من الذاكرة المؤقتة
  void removeCachedData(String key) {
    _advancedCache.remove(key);
  }

  /// الحصول على تاريخ انتهاء صلاحية عنصر في الذاكرة المؤقتة
  DateTime? getExpiry(String key) {
    if (_advancedCache.containsKey(key)) {
      return _advancedCache[key]?.expiryTime;
    }
    return null;
  }

  /// حفظ قيمة في التخزين المؤقت مع دعم التخزين على القرص
  Future<void> setCacheValue<T>(
    String key,
    T value, {
    Duration? expiry,
    bool persistToDisk = false,
  }) async {
    if (!_isInitialized) await initialize();

    final expiryTime = DateTime.now().add(expiry ?? _defaultExpiry);
    final entry = CachedData<T>(value, expiryTime);

    // حفظ في الذاكرة
    _advancedCache[key] = entry;
    _updateCacheSize(key, entry);

    // حفظ على القرص إذا طُلب ذلك
    if (persistToDisk) {
      try {
        final jsonData = {
          'value': _serializeValue(value),
          'expiry': expiryTime.toIso8601String(),
          'createdAt': entry.createdAt.toIso8601String(),
        };
        await setString('cache_$key', jsonEncode(jsonData));
      } catch (e) {
        debugPrint('خطأ في حفظ التخزين المؤقت على القرص: $e');
      }
    }

    // تنظيف الذاكرة إذا تجاوزت الحد الأقصى
    if (_autoCleanEnabled &&
        (_advancedCache.length > _cacheLimit ||
            _estimatedSizeBytes > _maxMemoryCacheSizeBytes)) {
      optimizeCache();
    }
  }

  /// استرداد قيمة من التخزين المؤقت مع دعم القراءة من القرص
  Future<T?> getCacheValue<T>(String key, {bool checkDisk = false}) async {
    if (!_isInitialized) await initialize();

    // البحث في الذاكرة أولاً
    if (_advancedCache.containsKey(key)) {
      final memoryEntry = _advancedCache[key] as CachedData;
      if (!memoryEntry.isExpired) {
        _cacheHits++;
        return memoryEntry.access() as T?;
      }
    }

    // البحث على القرص إذا طُلب ذلك
    if (checkDisk) {
      try {
        final diskData = getString('cache_$key');
        if (diskData != null) {
          final jsonData = jsonDecode(diskData) as Map<String, dynamic>;
          final expiry = DateTime.parse(jsonData['expiry'] as String);

          if (DateTime.now().isBefore(expiry)) {
            final value = _deserializeValue<T>(jsonData['value']);

            // التحقق من أن القيمة ليست null قبل إنشاء CachedData
            if (value != null) {
              // إعادة تحميل في الذاكرة
              final entry = CachedData<T>(
                value,
                expiry,
                createdAt: DateTime.parse(jsonData['createdAt'] as String),
              );
              _advancedCache[key] = entry;
              _updateCacheSize(key, entry);

              _cacheHits++;
              return value;
            }

            // إذا كانت القيمة null، نعيد null
            return null;
          } else {
            // حذف البيانات المنتهية الصلاحية
            await remove('cache_$key');
          }
        }
      } catch (e) {
        debugPrint('خطأ في قراءة التخزين المؤقت من القرص: $e');
      }
    }

    _cacheMisses++;
    return null;
  }

  /// تسلسل القيمة للحفظ
  dynamic _serializeValue<T>(T value) {
    if (value == null) return null;
    if (value is String || value is num || value is bool) return value;
    if (value is List || value is Map) return value;
    // للكائنات المعقدة، نحاول تحويلها إلى JSON
    try {
      return jsonEncode(value);
    } catch (e) {
      debugPrint('تعذر تسلسل القيمة: $e');
      return value.toString();
    }
  }

  /// إلغاء تسلسل القيمة
  T? _deserializeValue<T>(dynamic value) {
    if (value == null) return null;
    if (value is T) return value;

    // محاولة إلغاء تسلسل JSON
    if (value is String) {
      try {
        final decoded = jsonDecode(value);
        if (decoded is T) return decoded;
      } catch (e) {
        // إذا فشل، نعيد القيمة كما هي
      }
    }

    return value as T?;
  }

  /// إحصائيات الذاكرة المؤقتة المحسنة
  Map<String, dynamic> getCacheStats() {
    // حساب معدل نجاح الذاكرة المؤقتة
    final totalAccesses = _cacheHits + _cacheMisses;
    final hitRate = totalAccesses > 0 ? (_cacheHits / totalAccesses) * 100 : 0;

    // تحليل أعمار العناصر في الذاكرة المؤقتة
    final now = DateTime.now();
    int itemsUnder1Min = 0;
    int itemsUnder1Hour = 0;
    int itemsUnder1Day = 0;
    int itemsOver1Day = 0;

    for (final entry in _advancedCache.values) {
      final age = now.difference(entry.lastAccess);
      if (age < const Duration(minutes: 1)) {
        itemsUnder1Min++;
      } else if (age < const Duration(hours: 1)) {
        itemsUnder1Hour++;
      } else if (age < const Duration(days: 1)) {
        itemsUnder1Day++;
      } else {
        itemsOver1Day++;
      }
    }

    return {
      'total_items': _advancedCache.length,
      'legacy_items': _memoryCache.length,
      'cache_hits': _cacheHits,
      'cache_misses': _cacheMisses,
      'hit_rate_percentage': hitRate.toStringAsFixed(2),
      'items_under_1min': itemsUnder1Min,
      'items_under_1hour': itemsUnder1Hour,
      'items_under_1day': itemsUnder1Day,
      'items_over_1day': itemsOver1Day,
      'oldest_item':
          _advancedCache.values.isNotEmpty
              ? _advancedCache.values
                  .map((e) => e.lastAccess)
                  .reduce((a, b) => a.isBefore(b) ? a : b)
              : null,
      'newest_item':
          _advancedCache.values.isNotEmpty
              ? _advancedCache.values
                  .map((e) => e.lastAccess)
                  .reduce((a, b) => a.isAfter(b) ? a : b)
              : null,
    };
  }

  // ===================================================================
  // وظائف التحليلات
  // ===================================================================

  /// حفظ إعدادات التحليلات
  Future<void> saveAnalyticsEnabled(bool enabled) async {
    try {
      if (!_isInitialized) await initialize();
      await setBool('analytics_enabled', enabled);
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving analytics enabled: $e');
      throw UnifiedStorageException(
        'فشل في حفظ إعدادات التحليلات: ${e.toString()}',
      );
    }
  }

  /// جلب إعدادات التحليلات
  bool? getAnalyticsEnabled() {
    try {
      if (!_isInitialized) {
        debugPrint('UnifiedStorage not initialized for analytics');
        return true;
      }
      return getBool('analytics_enabled') ?? true;
    } catch (e) {
      debugPrint('Error getting analytics enabled: $e');
      return true;
    }
  }

  /// حفظ بيانات التحليلات
  Future<void> saveAnalyticsData(String key, dynamic data) async {
    try {
      await setJson('analytics_$key', data);
    } catch (e) {
      debugPrint('Error saving analytics data: $e');
    }
  }

  /// جلب بيانات التحليلات
  dynamic getAnalyticsData(String key) {
    try {
      final data = getJson('analytics_$key');
      return data;
    } catch (e) {
      debugPrint('Error getting analytics data: $e');
      return null;
    }
  }

  /// مسح بيانات التحليلات
  Future<void> clearAnalyticsData() async {
    try {
      if (!_isInitialized) await initialize();
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('analytics_')) {
          await remove(key);
        }
      }
    } catch (e) {
      debugPrint('Error clearing analytics data: $e');
    }
  }

  // ===================================================================
  // وظائف المزامنة
  // ===================================================================

  /// مزامنة البيانات مع السيرفر
  Future<void> syncWithServer() async {
    try {
      debugPrint('تم تعطيل مزامنة التخزين المحلي مع السيرفر');
      _pendingSync.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في مزامنة البيانات: $e');
    }
  }

  /// جلب البيانات من السيرفر
  Future<void> syncFromServer() async {
    try {
      debugPrint('تم تعطيل جلب البيانات من السيرفر - التخزين محلي فقط');
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في جلب البيانات: $e');
    }
  }

  /// التخلص من الموارد
  @override
  void dispose() {
    _cleanupTimer?.cancel();
    super.dispose();
    debugPrint('♻️ تم التخلص من موارد خدمة التخزين الموحدة');
  }

  // ===================================================================
  // وظائف إضافية للتوافق مع الخدمات القديمة
  // ===================================================================

  /// حفظ البيانات (alias)
  Future<void> saveData(String key, dynamic data) async {
    try {
      if (data is Map<String, dynamic>) {
        await setJson(key, data);
      } else if (data is List) {
        await setString(key, jsonEncode(data));
      } else if (data is String) {
        await setString(key, data);
      } else if (data is int) {
        await setInt(key, data);
      } else if (data is double) {
        await setDouble(key, data);
      } else if (data is bool) {
        await setBool(key, data);
      } else {
        await setString(key, data.toString());
      }
    } catch (e) {
      debugPrint('Error saving data: $e');
    }
  }

  /// جلب البيانات (alias)
  Future<dynamic> getData(String key) async {
    try {
      if (!_isInitialized) await initialize();

      // محاولة جلب كـ String أولاً
      final stringData = getString(key);
      if (stringData != null) {
        // محاولة تحويل إلى JSON إذا كان ممكناً
        try {
          return jsonDecode(stringData);
        } catch (e) {
          return stringData;
        }
      }

      // محاولة جلب كـ int
      final intData = getInt(key);
      if (intData != null) return intData;

      // محاولة جلب كـ double
      final doubleData = getDouble(key);
      if (doubleData != null) return doubleData;

      // محاولة جلب كـ bool
      final boolData = getBool(key);
      if (boolData != null) return boolData;

      return null;
    } catch (e) {
      debugPrint('Error getting data: $e');
      throw UnifiedStorageException('Failed to get data: $e');
    }
  }
}

/// استثناء خدمة التخزين الموحدة
class UnifiedStorageException implements Exception {
  final String message;
  UnifiedStorageException(this.message);

  @override
  String toString() => 'UnifiedStorageException: $message';
}
