import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/models/orders/order_model.dart';
import 'package:motorcycle_parts_shop/screens/orders/order_details_screen.dart';
import 'package:provider/provider.dart';

class OrderHistoryScreen extends StatefulWidget {
  const OrderHistoryScreen({super.key});

  @override
  State<OrderHistoryScreen> createState() => _OrderHistoryScreenState();
}

class _OrderHistoryScreenState extends State<OrderHistoryScreen> {
  final ScrollController _scrollController = ScrollController();

  List<OrderModel> _orders = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreOrders = true;
  int _currentPage = 0;
  static const int _pageSize = 10;
  String? _error;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _setupScrollListener();
    _loadOrders();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        if (!_isLoadingMore && _hasMoreOrders) {
          _loadMoreOrders();
        }
      }
    });
  }

  Future<void> _loadMoreOrders() async {
    if (_isLoadingMore || !_hasMoreOrders) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // هنا يمكن إضافة استدعاء API للحصول على المزيد من الطلبات
      // final moreOrders = await _orderService.getOrders(
      //   limit: _pageSize,
      //   offset: _currentPage * _pageSize,
      // );

      // مؤقتاً نضع قائمة فارغة - استخدام _currentPage للحساب
      final offset = _currentPage * _pageSize;
      debugPrint('تحميل الصفحة $_currentPage مع offset: $offset');
      final moreOrders = <OrderModel>[];

      setState(() {
        if (moreOrders.isNotEmpty) {
          _orders.addAll(moreOrders);
          _currentPage++;
          _hasMoreOrders = moreOrders.length >= _pageSize;
        } else {
          _hasMoreOrders = false;
        }
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      debugPrint('خطأ في تحميل المزيد من الطلبات: $e');
    }
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      Provider.of<AuthSupabaseService>(context, listen: false);
      // استخدام دالة وهمية للطلبات حتى يتم تطوير الدالة الحقيقية
      final orders = <OrderModel>[];

      setState(() {
        _orders = orders;
        _currentPage = 0; // إعادة تعيين الصفحة الحالية
        _hasMoreOrders = orders.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<OrderModel> get _filteredOrders {
    if (_selectedFilter == 'all') return _orders;
    return _orders.where((order) => order.status == _selectedFilter).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 70 : 80,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            boxShadow: AppTheme.cardShadow,
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  // زر الرجوع المحسن
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // أيقونة الطلبات والعنوان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.receipt_long_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'طلباتي',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        if (_orders.isNotEmpty)
                          Text(
                            '${_filteredOrders.length} من ${_orders.length} طلب',
                            style: AppTheme.cardSubtitle.copyWith(
                              color: AppTheme.textLightColor.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // زر التصفية
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list_rounded,
                        color: AppTheme.textLightColor,
                        size: 22,
                      ),
                      onPressed: _showFilterDialog,
                      tooltip: 'تصفية الطلبات',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? _buildErrorView()
              : _orders.isEmpty
              ? _buildEmptyView()
              : _buildOrdersList(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: AppTheme.cardGradient,
                shape: BoxShape.circle,
                boxShadow: AppTheme.cardShadow,
              ),
              child: Icon(
                Icons.error_outline_rounded,
                size: 80,
                color: AppTheme.errorColor,
              ),
            ),

            const SizedBox(height: 32),

            Text(
              'حدث خطأ في تحميل الطلبات',
              style: AppTheme.heroTitle.copyWith(
                fontSize: 24,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 12),

            Text(
              _error ?? 'خطأ غير معروف',
              style: AppTheme.cardSubtitle.copyWith(fontSize: 16, height: 1.5),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: AppTheme.cardShadow,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _loadOrders,
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.refresh_rounded,
                          color: AppTheme.textLightColor,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'إعادة المحاولة',
                          style: AppTheme.buttonText.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                gradient: AppTheme.cardGradient,
                shape: BoxShape.circle,
                boxShadow: AppTheme.cardShadow,
              ),
              child: Icon(
                Icons.receipt_long_outlined,
                size: 80,
                color: AppTheme.textTertiaryColor,
              ),
            ),

            const SizedBox(height: 32),

            Text(
              'لا توجد طلبات بعد',
              style: AppTheme.heroTitle.copyWith(
                fontSize: 24,
                color: AppTheme.textPrimaryColor,
              ),
            ),

            const SizedBox(height: 12),

            Text(
              'ابدأ بتصفح المنتجات وإضافتها\nإلى سلة التسوق لإنشاء طلبك الأول',
              style: AppTheme.cardSubtitle.copyWith(fontSize: 16, height: 1.5),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: AppTheme.cardShadow,
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => Navigator.pushNamed(context, '/home'),
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.shopping_bag_rounded,
                          color: AppTheme.textLightColor,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'تصفح المنتجات',
                          style: AppTheme.buttonText.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrdersList() {
    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
        itemCount: _filteredOrders.length + (_hasMoreOrders ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredOrders.length) {
            return _buildLoadingIndicator();
          }
          final order = _filteredOrders[index];
          return _buildOrderCard(order);
        },
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child:
          _isLoadingMore
              ? const CircularProgressIndicator()
              : const SizedBox.shrink(),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final margin = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );

        return Container(
          margin: EdgeInsets.only(bottom: margin),
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: AppTheme.cardShadow,
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.08),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _navigateToOrderDetails(order),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              child: Padding(
                padding: EdgeInsets.all(padding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رأس الطلب
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'طلب #${order.id}',
                          style: AppTheme.cardTitle.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        _buildOrderStatus(order.status),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // تفاصيل الطلب
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today_rounded,
                          color: AppTheme.textTertiaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatDate(order.createdAt),
                          style: AppTheme.cardSubtitle.copyWith(fontSize: 14),
                        ),
                        const Spacer(),
                        Icon(
                          Icons.shopping_bag_rounded,
                          color: AppTheme.textTertiaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${order.items.length} منتج',
                          style: AppTheme.cardSubtitle.copyWith(fontSize: 14),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // السعر الإجمالي
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.attach_money_rounded,
                            color: AppTheme.textLightColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${order.totalAmount.toStringAsFixed(0)} ج.م',
                            style: AppTheme.priceText.copyWith(
                              color: AppTheme.textLightColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOrderStatus(String status) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (status.toLowerCase()) {
      case 'pending':
        statusColor = AppTheme.warningColor;
        statusText = 'قيد المعالجة';
        statusIcon = Icons.pending_rounded;
        break;
      case 'confirmed':
        statusColor = AppTheme.infoColor;
        statusText = 'مؤكد';
        statusIcon = Icons.check_circle_rounded;
        break;
      case 'shipped':
        statusColor = AppTheme.primaryColor;
        statusText = 'تم الشحن';
        statusIcon = Icons.local_shipping_rounded;
        break;
      case 'delivered':
        statusColor = AppTheme.successColor;
        statusText = 'تم التسليم';
        statusIcon = Icons.done_all_rounded;
        break;
      case 'cancelled':
        statusColor = AppTheme.errorColor;
        statusText = 'ملغي';
        statusIcon = Icons.cancel_rounded;
        break;
      default:
        statusColor = AppTheme.textTertiaryColor;
        statusText = status;
        statusIcon = Icons.help_rounded;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusIcon, color: statusColor, size: 16),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: AppTheme.cardSubtitle.copyWith(
              color: statusColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _navigateToOrderDetails(OrderModel order) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => OrderDetailsScreen(order: order)),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('تصفية الطلبات', style: AppTheme.cardTitle),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildFilterOption('all', 'جميع الطلبات'),
                _buildFilterOption('pending', 'قيد المعالجة'),
                _buildFilterOption('confirmed', 'مؤكدة'),
                _buildFilterOption('shipped', 'تم الشحن'),
                _buildFilterOption('delivered', 'تم التسليم'),
                _buildFilterOption('cancelled', 'ملغية'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إغلاق',
                  style: AppTheme.cardTitle.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildFilterOption(String value, String label) {
    return RadioListTile<String>(
      title: Text(label, style: AppTheme.cardTitle.copyWith(fontSize: 14)),
      value: value,
      groupValue: _selectedFilter,
      onChanged: (String? newValue) {
        setState(() {
          _selectedFilter = newValue ?? 'all';
        });
        Navigator.pop(context);
      },
      activeColor: AppTheme.primaryColor,
    );
  }
}
