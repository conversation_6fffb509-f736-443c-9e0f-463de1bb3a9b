import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:motorcycle_parts_shop/core/services/coupon_service.dart';
import 'package:motorcycle_parts_shop/models/coupon_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CouponManagementScreen extends StatefulWidget {
  const CouponManagementScreen({super.key});

  @override
  State<CouponManagementScreen> createState() => _CouponManagementScreenState();
}

class _CouponManagementScreenState extends State<CouponManagementScreen> {
  final _couponService = CouponService(Supabase.instance.client);
  List<CouponModel> _coupons = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadCoupons();
  }

  Future<void> _loadCoupons() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final coupons = await _couponService.getActiveCoupons();
      setState(() {
        _coupons = coupons;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _showAddCouponDialog() async {
    final formKey = GlobalKey<FormState>();
    String code = '';
    double discountAmount = 0;
    String discountType = 'percentage';
    DateTime? expiryDate;
    double? minPurchaseAmount;
    String description = '';
    double? maxDiscountAmount;
    DateTime? startDate;
    int? usageLimit;
    bool isActive = true;

    await showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة كوبون جديد'),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'رمز الكوبون',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال رمز الكوبون';
                        }
                        return null;
                      },
                      onSaved: (value) => code = value!,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'قيمة الخصم',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال قيمة الخصم';
                        }
                        if (double.tryParse(value) == null) {
                          return 'الرجاء إدخال رقم صحيح';
                        }
                        return null;
                      },
                      onSaved: (value) => discountAmount = double.parse(value!),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: discountType,
                      decoration: const InputDecoration(labelText: 'نوع الخصم'),
                      items: const [
                        DropdownMenuItem(
                          value: 'percentage',
                          child: Text('نسبة مئوية'),
                        ),
                        DropdownMenuItem(
                          value: 'fixed',
                          child: Text('قيمة ثابتة'),
                        ),
                      ],
                      onChanged: (value) => discountType = value!,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'الحد الأدنى للشراء',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'الرجاء إدخال رقم صحيح';
                          }
                        }
                        return null;
                      },
                      onSaved:
                          (value) =>
                              minPurchaseAmount =
                                  value?.isNotEmpty == true
                                      ? double.parse(value!)
                                      : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'وصف الكوبون',
                      ),
                      onSaved: (value) => description = value ?? '',
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'أقصى قيمة للخصم (اختياري)',
                      ),
                      keyboardType: TextInputType.number,
                      onSaved:
                          (value) =>
                              maxDiscountAmount =
                                  value?.isNotEmpty == true
                                      ? double.parse(value!)
                                      : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'عدد مرات الاستخدام (اختياري)',
                      ),
                      keyboardType: TextInputType.number,
                      onSaved:
                          (value) =>
                              usageLimit =
                                  value?.isNotEmpty == true
                                      ? int.parse(value!)
                                      : null,
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('نشط'),
                      value: isActive,
                      onChanged: (value) => setState(() => isActive = value),
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('تاريخ البداية'),
                      subtitle: Text(
                        startDate != null
                            ? DateFormat('yyyy-MM-dd').format(startDate!)
                            : 'لم يتم تحديد تاريخ',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now().subtract(
                            const Duration(days: 365),
                          ),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (date != null) {
                          setState(() => startDate = date);
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('تاريخ الانتهاء'),
                      subtitle: Text(
                        expiryDate != null
                            ? DateFormat('yyyy-MM-dd').format(expiryDate!)
                            : 'لم يتم تحديد تاريخ',
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now().add(
                            const Duration(days: 30),
                          ),
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (date != null) {
                          setState(() => expiryDate = date);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    formKey.currentState!.save();
                    if (expiryDate == null || startDate == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('الرجاء تحديد تاريخ البداية والانتهاء'),
                        ),
                      );
                      return;
                    }

                    try {
                      final coupon = CouponModel(
                        id: '',
                        code: code,
                        description: description,
                        discountType: discountType,
                        discountValue: discountAmount,
                        minPurchaseAmount: minPurchaseAmount,
                        maxDiscountAmount: maxDiscountAmount,
                        startDate: startDate!,
                        endDate: expiryDate!,
                        usageLimit: usageLimit,
                        usageCount: 0,
                        isActive: isActive,
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                        createdBy: null,
                      );

                      await _couponService.createCoupon(coupon);
                      if (mounted) {
                        Navigator.pop(context);
                        _loadCoupons();
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(
                          context,
                        ).showSnackBar(SnackBar(content: Text(e.toString())));
                      }
                    }
                  }
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الكوبونات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddCouponDialog,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(child: Text(_error!))
              : _coupons.isEmpty
              ? const Center(child: Text('لا توجد كوبونات'))
              : ListView.builder(
                itemCount: _coupons.length,
                itemBuilder: (context, index) {
                  final coupon = _coupons[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: ListTile(
                      title: Text(coupon.code),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الخصم: ${coupon.discountValue}${coupon.discountType == 'percentage' ? '%' : ' ريال'}',
                          ),
                          if (coupon.minPurchaseAmount != null)
                            Text(
                              'الحد الأدنى للشراء: ${coupon.minPurchaseAmount} ريال',
                            ),
                          Text(
                            'تاريخ الانتهاء: ${DateFormat('yyyy-MM-dd').format(coupon.endDate)}',
                          ),
                        ],
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () async {
                          final confirmed = await showDialog<bool>(
                            context: context,
                            builder:
                                (context) => AlertDialog(
                                  title: const Text('تأكيد الحذف'),
                                  content: const Text(
                                    'هل أنت متأكد من حذف هذا الكوبون؟',
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed:
                                          () => Navigator.pop(context, false),
                                      child: const Text('إلغاء'),
                                    ),
                                    ElevatedButton(
                                      onPressed:
                                          () => Navigator.pop(context, true),
                                      child: const Text('حذف'),
                                    ),
                                  ],
                                ),
                          );

                          if (confirmed == true) {
                            try {
                              await _couponService.deleteCoupon(coupon.id);
                              _loadCoupons();
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(content: Text(e.toString())),
                                );
                              }
                            }
                          }
                        },
                      ),
                    ),
                  );
                },
              ),
    );
  }
}
