import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:motorcycle_parts_shop/core/services/image_recognition_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ImageSearchScreen extends StatefulWidget {
  const ImageSearchScreen({super.key});

  @override
  State<ImageSearchScreen> createState() => ImageSearchScreenState();
}

class ImageSearchScreenState extends State<ImageSearchScreen> {
  late final ImageRecognitionService _imageRecognitionService;
  File? _selectedImage;
  List<ProductModel> _searchResults = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final supabaseClient = Supabase.instance.client;
    _imageRecognitionService = ImageRecognitionService(
      ProductService(supabaseClient),
    );
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() => _selectedImage = File(pickedFile.path));
    }
  }

  Future<void> _searchByImage() async {
    if (_selectedImage == null) return;
    setState(() => _isLoading = true);
    try {
      final results = await _imageRecognitionService.searchProductsByImage(
        _selectedImage!,
      );
      setState(() => _searchResults = results);
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error searching by image: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Search by Image')),
      body: Column(
        children: [
          ElevatedButton(onPressed: _pickImage, child: Text('Pick Image')),
          if (_selectedImage != null) Image.file(_selectedImage!, height: 200),
          ElevatedButton(onPressed: _searchByImage, child: Text('Search')),
          if (_isLoading) CircularProgressIndicator(),
          Expanded(
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder:
                  (context, index) =>
                      ListTile(title: Text(_searchResults[index].name)),
            ),
          ),
        ],
      ),
    );
  }
}
