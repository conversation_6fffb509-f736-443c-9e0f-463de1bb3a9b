# تحسينات كود Dart المحددة
## Specific Dart Code Improvements

---

## 🚀 تحسين main.dart

### المشكلة الحالية
```dart
// تحميل جميع الخدمات مرة واحدة - يسبب بطء
Future<AppServices> _initializeEssentialServices() async {
  final supabaseService = AuthSupabaseService();
  final themeService = ThemeService();
  final cartService = CartService();
  // ... 30+ خدمة أخرى
  
  await Future.wait([
    themeService.initialize(),
    cartService.initialize(),
    // ... تهيئة جميع الخدمات
  ]);
}
```

### الحل المحسن
```dart
// تحميل تدريجي للخدمات
Future<CoreServices> _initializeCoreServices() async {
  // الخدمات الأساسية فقط (3-5 خدمات)
  final authService = AuthSupabaseService();
  final themeService = ThemeService();
  final navigationService = NavigationService();
  
  await Future.wait([
    authService.initialize(),
    themeService.initialize(),
    navigationService.initialize(),
  ]);
  
  return CoreServices(
    authService: authService,
    themeService: themeService,
    navigationService: navigationService,
  );
}

// تحميل الخدمات الثانوية في الخلفية
void _initializeSecondaryServices() {
  Future.delayed(Duration(milliseconds: 500), () async {
    final cartService = CartService();
    final wishlistService = WishlistService();
    // ... باقي الخدمات
    
    await Future.wait([
      cartService.initialize(),
      wishlistService.initialize(),
    ]);
    
    // تسجيل الخدمات في ServiceLocator
    ServiceLocator.registerLazySingleton(() => cartService);
    ServiceLocator.registerLazySingleton(() => wishlistService);
  });
}
```

---

## 🔧 تحسين AuthSupabaseService

### إصلاح تسريب الذاكرة
```dart
class AuthSupabaseService extends ChangeNotifier {
  Timer? _sessionRefreshTimer;
  Timer? _connectionCheckTimer;
  StreamSubscription? _authStateSubscription;
  
  @override
  void dispose() {
    // إلغاء جميع المؤقتات والاشتراكات
    _sessionRefreshTimer?.cancel();
    _connectionCheckTimer?.cancel();
    _authStateSubscription?.cancel();
    
    // تنظيف الذاكرة المؤقتة
    _cache.clear();
    
    super.dispose();
  }
  
  // تحسين إدارة الجلسة
  void _setupSessionRefresh() {
    _sessionRefreshTimer?.cancel();
    _sessionRefreshTimer = Timer.periodic(
      _sessionRefreshInterval,
      (_) => _refreshSessionIfNeeded(),
    );
  }
  
  Future<void> _refreshSessionIfNeeded() async {
    if (!_isAuthenticated) return;
    
    try {
      final session = _client.auth.currentSession;
      if (session?.expiresAt != null) {
        final expiresIn = session!.expiresAt! - DateTime.now().millisecondsSinceEpoch ~/ 1000;
        
        // تجديد الجلسة إذا كانت ستنتهي خلال 5 دقائق
        if (expiresIn < 300) {
          await _client.auth.refreshSession();
        }
      }
    } catch (e) {
      debugPrint('خطأ في تجديد الجلسة: $e');
    }
  }
}
```

---

## 📱 تحسين ProductService

### تطبيق Lazy Loading والتخزين المؤقت
```dart
class ProductService {
  final SupabaseClient _client;
  final Map<String, CachedData<List<ProductModel>>> _cache = {};
  static const Duration _cacheDuration = Duration(minutes: 5);
  
  ProductService(this._client);
  
  // تحميل المنتجات مع Pagination
  Future<List<ProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? category,
    String? searchQuery,
  }) async {
    final cacheKey = 'products_${page}_${limit}_${category}_$searchQuery';
    
    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final cachedData = _cache[cacheKey]!;
      if (!cachedData.isExpired) {
        return cachedData.data;
      }
    }
    
    try {
      var query = _client
          .from('products')
          .select('''
            *,
            categories(name),
            companies(name)
          ''')
          .eq('is_available', true)
          .range((page - 1) * limit, page * limit - 1);
      
      if (category != null) {
        query = query.eq('category_id', category);
      }
      
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.textSearch('name', searchQuery);
      }
      
      final response = await query;
      final products = (response as List)
          .map((json) => ProductModel.fromJson(json))
          .toList();
      
      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = CachedData(
        data: products,
        timestamp: DateTime.now(),
        duration: _cacheDuration,
      );
      
      return products;
    } catch (e) {
      throw ProductServiceException('فشل في تحميل المنتجات: $e');
    }
  }
  
  // تنظيف الذاكرة المؤقتة المنتهية الصلاحية
  void _cleanExpiredCache() {
    _cache.removeWhere((key, value) => value.isExpired);
  }
}

class CachedData<T> {
  final T data;
  final DateTime timestamp;
  final Duration duration;
  
  CachedData({
    required this.data,
    required this.timestamp,
    required this.duration,
  });
  
  bool get isExpired => DateTime.now().difference(timestamp) > duration;
}
```

---

## 🛒 تحسين CartService

### تحسين الأداء وإدارة الحالة
```dart
class CartService extends ChangeNotifier {
  final Map<String, CartServiceItem> _items = {};
  final SupabaseClient _client;
  bool _isLoading = false;
  Timer? _syncTimer;
  
  CartService(this._client);
  
  // إضافة منتج للسلة مع تحسين الأداء
  Future<void> addItem(ProductModel product, {int quantity = 1}) async {
    // التحقق من صحة المدخلات
    if (quantity <= 0) {
      throw ArgumentError('الكمية يجب أن تكون أكبر من صفر');
    }
    
    if (product.stockQuantity < quantity) {
      throw InsufficientStockException('الكمية المطلوبة غير متوفرة');
    }
    
    final existingItem = _items[product.id];
    
    if (existingItem != null) {
      // تحديث الكمية للمنتج الموجود
      final newQuantity = existingItem.quantity + quantity;
      if (newQuantity > product.stockQuantity) {
        throw InsufficientStockException('تجاوز الكمية المتاحة');
      }
      
      existingItem.quantity = newQuantity;
    } else {
      // إضافة منتج جديد
      _items[product.id] = CartServiceItem(
        id: _generateItemId(),
        product: product,
        quantity: quantity,
        addedAt: DateTime.now(),
      );
    }
    
    // تحديث الواجهة فوراً
    notifyListeners();
    
    // مزامنة مع الخادم في الخلفية
    _scheduleSyncWithServer();
  }
  
  // مزامنة مع الخادم بشكل دوري
  void _scheduleSyncWithServer() {
    _syncTimer?.cancel();
    _syncTimer = Timer(Duration(seconds: 2), () {
      _syncWithServer();
    });
  }
  
  Future<void> _syncWithServer() async {
    if (!_isAuthenticated) return;
    
    try {
      // مزامنة العناصر المحلية مع الخادم
      for (final item in _items.values) {
        await _client.from('cart_items').upsert({
          'id': item.id,
          'user_id': _getCurrentUserId(),
          'product_id': item.product.id,
          'quantity': item.quantity,
          'updated_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      debugPrint('خطأ في مزامنة السلة: $e');
    }
  }
  
  @override
  void dispose() {
    _syncTimer?.cancel();
    super.dispose();
  }
}

// استثناءات مخصصة
class InsufficientStockException implements Exception {
  final String message;
  InsufficientStockException(this.message);
}

class ProductServiceException implements Exception {
  final String message;
  ProductServiceException(this.message);
}
```

---

## 🎨 تحسين HomeScreen

### تحسين الأداء والرسوم المتحركة
```dart
class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  
  // الحفاظ على حالة الشاشة
  @override
  bool get wantKeepAlive => true;
  
  // تحميل البيانات بشكل تدريجي
  Future<void> _loadData() async {
    if (!mounted) return;
    
    setState(() => _isLoading = true);
    
    try {
      // تحميل البيانات الأساسية أولاً
      await _loadEssentialData();
      
      // تحميل البيانات الثانوية في الخلفية
      _loadSecondaryDataInBackground();
      
    } catch (e) {
      _handleError(e);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        _animationController.forward();
      }
    }
  }
  
  Future<void> _loadEssentialData() async {
    // تحميل الفئات والمنتجات المميزة فقط
    final futures = await Future.wait([
      _productService.getCategories(limit: 8),
      _productService.getFeaturedProducts(limit: 6),
    ]);
    
    _categories = futures[0] as List<CategoryModel>;
    _featuredProducts = futures[1] as List<ProductModel>;
  }
  
  void _loadSecondaryDataInBackground() {
    Future.delayed(Duration(milliseconds: 500), () async {
      if (!mounted) return;
      
      try {
        final futures = await Future.wait([
          _productService.getBestSelling(limit: 10),
          _productService.getOffers(limit: 8),
          _adService.getActiveAds(),
        ]);
        
        if (mounted) {
          setState(() {
            _bestSellingProducts = futures[0] as List<ProductModel>;
            _offerProducts = futures[1] as List<ProductModel>;
            _advertisements = futures[2] as List<Advertisement>;
          });
        }
      } catch (e) {
        debugPrint('خطأ في تحميل البيانات الثانوية: $e');
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    super.build(context); // مطلوب لـ AutomaticKeepAliveClientMixin
    
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            if (_isLoading)
              SliverFillRemaining(child: _buildLoadingShimmer())
            else ...[
              _buildSearchSection(),
              _buildCategoriesSection(),
              _buildFeaturedProductsSection(),
              if (_bestSellingProducts.isNotEmpty)
                _buildBestSellingSection(),
              if (_offerProducts.isNotEmpty)
                _buildOffersSection(),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildLoadingShimmer() {
    return ListView(
      children: List.generate(
        5,
        (index) => Padding(
          padding: EdgeInsets.all(16),
          child: ShimmerLoading(
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
```

---

## 🔒 تحسين الأمان

### إدارة آمنة للبيانات الحساسة
```dart
class SecureDataManager {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );
  
  // تشفير وحفظ البيانات
  static Future<void> storeSecureData(String key, String value) async {
    try {
      final encryptedValue = await _encryptData(value);
      await _secureStorage.write(key: key, value: encryptedValue);
    } catch (e) {
      throw SecureStorageException('فشل في حفظ البيانات الآمنة: $e');
    }
  }
  
  // قراءة وفك تشفير البيانات
  static Future<String?> getSecureData(String key) async {
    try {
      final encryptedValue = await _secureStorage.read(key: key);
      if (encryptedValue == null) return null;
      
      return await _decryptData(encryptedValue);
    } catch (e) {
      debugPrint('خطأ في قراءة البيانات الآمنة: $e');
      return null;
    }
  }
  
  // تشفير البيانات باستخدام AES
  static Future<String> _encryptData(String data) async {
    final key = Key.fromSecureRandom(32);
    final iv = IV.fromSecureRandom(16);
    final encrypter = Encrypter(AES(key));
    
    final encrypted = encrypter.encrypt(data, iv: iv);
    
    // حفظ المفتاح و IV بشكل آمن
    await _secureStorage.write(key: 'encryption_key', value: key.base64);
    await _secureStorage.write(key: 'encryption_iv', value: iv.base64);
    
    return encrypted.base64;
  }
  
  static Future<String> _decryptData(String encryptedData) async {
    final keyString = await _secureStorage.read(key: 'encryption_key');
    final ivString = await _secureStorage.read(key: 'encryption_iv');
    
    if (keyString == null || ivString == null) {
      throw Exception('مفاتيح التشفير غير موجودة');
    }
    
    final key = Key.fromBase64(keyString);
    final iv = IV.fromBase64(ivString);
    final encrypter = Encrypter(AES(key));
    
    final encrypted = Encrypted.fromBase64(encryptedData);
    return encrypter.decrypt(encrypted, iv: iv);
  }
}

class SecureStorageException implements Exception {
  final String message;
  SecureStorageException(this.message);
}
```

---

## 📊 نظام معالجة الأخطاء الموحد

```dart
class ErrorHandler {
  static final Map<Type, ErrorHandlerFunction> _handlers = {
    NetworkException: _handleNetworkError,
    AuthException: _handleAuthError,
    ValidationException: _handleValidationError,
    InsufficientStockException: _handleStockError,
  };
  
  static void handleError(
    dynamic error, {
    BuildContext? context,
    String? userMessage,
    bool showSnackBar = true,
  }) {
    final handler = _handlers[error.runtimeType];
    
    if (handler != null) {
      handler(error, context, userMessage, showSnackBar);
    } else {
      _handleGenericError(error, context, userMessage, showSnackBar);
    }
    
    // تسجيل الخطأ للتحليل
    _logError(error);
  }
  
  static void _handleNetworkError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final message = userMessage ?? 'مشكلة في الاتصال بالإنترنت';
    
    if (showSnackBar && context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          action: SnackBarAction(
            label: 'إعادة المحاولة',
            onPressed: () => _retryLastOperation(),
          ),
        ),
      );
    }
  }
  
  static void _logError(dynamic error) {
    // تسجيل الخطأ في خدمة التحليلات
    AnalyticsService.logError(
      error.toString(),
      stackTrace: StackTrace.current,
      additionalData: {
        'timestamp': DateTime.now().toIso8601String(),
        'user_id': AuthService.currentUserId,
      },
    );
  }
}

typedef ErrorHandlerFunction = void Function(
  dynamic error,
  BuildContext? context,
  String? userMessage,
  bool showSnackBar,
);
```

هذه التحسينات ستؤدي إلى تحسن كبير في الأداء والأمان وتجربة المستخدم.
