import 'dart:async';

import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/database_optimizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../analytics/unified_analytics.dart';
import '../backup/backup_manager.dart';
import '../monitoring/app_logger.dart';
import '../monitoring/performance_monitor.dart';

/// مدير البنية التحتية للتطبيق
/// يدير جميع الخدمات الأساسية ويضمن التكامل السلس بينها
class AppInfrastructure {
  static final AppInfrastructure _instance = AppInfrastructure._internal();
  factory AppInfrastructure() => _instance;
  AppInfrastructure._internal();

  // الخدمات الأساسية
  late final SupabaseClient _supabase;
  late final PerformanceMonitor _performanceMonitor;
  late final AppLogger _logger;
  late final BackupManager _backupManager;
  late final UnifiedAnalyticsService _userAnalytics;
  late final DatabaseOptimizer _databaseOptimizer;

  bool _isInitialized = false;

  /// تهيئة البنية التحتية
  Future<void> initialize({
    required String supabaseUrl,
    required String supabaseAnonKey,
    String? userId,
  }) async {
    if (_isInitialized) return;

    try {
      // تهيئة Supabase
      await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
      _supabase = Supabase.instance.client;

      // تهيئة النظم الفرعية
      await _initializeSubsystems(userId);

      _isInitialized = true;
      _logger.info('✅ تم تهيئة البنية التحتية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة البنية التحتية: $e');
      rethrow;
    }
  }

  /// تهيئة الأنظمة الفرعية
  Future<void> _initializeSubsystems(String? userId) async {
    // تهيئة نظام التسجيل أولاً
    _logger = AppLogger();
    _logger.initialize(_supabase);

    // تهيئة مراقب الأداء
    _performanceMonitor = PerformanceMonitor();
    await _performanceMonitor.initialize(_supabase);

    // تهيئة مدير النسخ الاحتياطي
    _backupManager = BackupManager();
    await _backupManager.initialize(_supabase);

    // تهيئة نظام التحليلات
    _userAnalytics = UnifiedAnalyticsService();
    final prefs = await SharedPreferences.getInstance();
    await _userAnalytics.initialize(
      supabase: _supabase,
      prefs: prefs,
      userId: userId,
    );

    // تهيئة محسن قاعدة البيانات
    _databaseOptimizer = DatabaseOptimizer(_supabase);

    // تسجيل تهيئة الأنظمة
    _logger.info('تم تهيئة جميع الأنظمة الفرعية');
  }

  /// الحصول على العملاء والخدمات
  SupabaseClient get supabase => _supabase;
  PerformanceMonitor get performanceMonitor => _performanceMonitor;
  AppLogger get logger => _logger;
  BackupManager get backupManager => _backupManager;
  UnifiedAnalyticsService get userAnalytics => _userAnalytics;
  DatabaseOptimizer get databaseOptimizer => _databaseOptimizer;

  /// إجراء فحص دوري للصحة
  Future<HealthCheckResult> performHealthCheck() async {
    final healthCheck = HealthCheckResult();

    try {
      // فحص اتصال قاعدة البيانات
      _performanceMonitor.startOperation('health_check_database');
      final dbResponse = await _supabase
          .from('health_check')
          .select('1')
          .limit(1);
      _performanceMonitor.endOperation('health_check_database');
      healthCheck.databaseStatus = HealthStatus.healthy;

      // فحص حالة النسخ الاحتياطي
      final backups = await _backupManager.getBackupsList();
      healthCheck.backupStatus =
          backups.isNotEmpty ? HealthStatus.healthy : HealthStatus.warning;

      // فحص الأداء
      final performanceStats = _performanceMonitor.getPerformanceStats();
      final avgDuration = performanceStats['average_duration'] ?? 0;
      healthCheck.performanceStatus =
          avgDuration < 2000 ? HealthStatus.healthy : HealthStatus.warning;

      // فحص التحليلات
      final analyticsData = await _userAnalytics.getAnalyticsData();
      healthCheck.analyticsStatus =
          analyticsData.currentSessionEvents > 0
              ? HealthStatus.healthy
              : HealthStatus.warning;

      _logger.info('تم إجراء فحص الصحة', data: healthCheck.toMap());
    } catch (e) {
      _logger.error('خطأ في فحص الصحة', error: e);
      healthCheck.overallStatus = HealthStatus.error;
    }

    return healthCheck;
  }

  /// تنفيذ مهام الصيانة الدورية
  Future<void> performMaintenance() async {
    try {
      _logger.info('بدء مهام الصيانة الدورية');

      // تنظيف النسخ الاحتياطية القديمة
      await _backupManager.cleanupOldBackups();

      // إرسال البيانات المتراكمة
      await _logger.dispose();
      await _userAnalytics.dispose();

      // إعادة تهيئة الأنظمة
      await _initializeSubsystems(null);

      _logger.info('تم إنجاز مهام الصيانة بنجاح');
    } catch (e) {
      _logger.error('خطأ في مهام الصيانة', error: e);
    }
  }

  /// إنشاء نسخة احتياطية طارئة
  Future<bool> createEmergencyBackup({String? reason}) async {
    try {
      _logger.info('إنشاء نسخة احتياطية طارئة', data: {'reason': reason});

      final result = await _backupManager.createFullBackup(
        description: 'نسخة احتياطية طارئة: ${reason ?? 'غير محدد'}',
      );

      if (result.success) {
        _logger.info('تم إنشاء النسخة الاحتياطية الطارئة بنجاح');
        return true;
      } else {
        _logger.error(
          'فشل في إنشاء النسخة الاحتياطية الطارئة: ${result.message}',
        );
        return false;
      }
    } catch (e) {
      _logger.error('خطأ في إنشاء النسخة الاحتياطية الطارئة', error: e);
      return false;
    }
  }

  /// الحصول على إحصائيات شاملة
  Future<AppStats> getAppStats() async {
    try {
      final performanceStats = _performanceMonitor.getPerformanceStats();
      final analyticsData = await _userAnalytics.getAnalyticsData();
      final backups = await _backupManager.getBackupsList();

      return AppStats(
        performanceStats: performanceStats,
        userStats: analyticsData,
        backupCount: backups.length,
        lastBackupDate: backups.isNotEmpty ? backups.first.timestamp : null,
      );
    } catch (e) {
      _logger.error('خطأ في جلب إحصائيات التطبيق', error: e);
      return AppStats.empty();
    }
  }

  /// معالج الأخطاء الشامل
  void handleError(dynamic error, StackTrace stackTrace, {String? context}) {
    // تسجيل الخطأ
    _logger.error('خطأ في التطبيق', error: error, stackTrace: stackTrace);

    // تسجيل الخطأ في نظام المراقبة
    _performanceMonitor.recordError(error, stackTrace, context: context);

    // تتبع الخطأ في التحليلات
    _userAnalytics.trackError(error.toString(), context: context);

    // إنشاء نسخة احتياطية في حالة الأخطاء الحرجة
    if (_isCriticalError(error)) {
      createEmergencyBackup(reason: 'خطأ حرج: ${error.toString()}');
    }
  }

  /// التحقق من كون الخطأ حرج
  bool _isCriticalError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('database') ||
        errorString.contains('network') ||
        errorString.contains('authentication') ||
        errorString.contains('permission');
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    try {
      _performanceMonitor.dispose();
      await _logger.dispose();
      await _userAnalytics.dispose();

      debugPrint('تم تنظيف موارد البنية التحتية');
    } catch (e) {
      debugPrint('خطأ في تنظيف الموارد: $e');
    }
  }
}

/// نتيجة فحص الصحة
class HealthCheckResult {
  HealthStatus overallStatus = HealthStatus.healthy;
  HealthStatus databaseStatus = HealthStatus.unknown;
  HealthStatus backupStatus = HealthStatus.unknown;
  HealthStatus performanceStatus = HealthStatus.unknown;
  HealthStatus analyticsStatus = HealthStatus.unknown;

  Map<String, dynamic> toMap() => {
    'overall_status': overallStatus.name,
    'database_status': databaseStatus.name,
    'backup_status': backupStatus.name,
    'performance_status': performanceStatus.name,
    'analytics_status': analyticsStatus.name,
  };
}

/// حالة الصحة
enum HealthStatus { healthy, warning, error, unknown }

/// إحصائيات التطبيق
class AppStats {
  final Map<String, dynamic> performanceStats;
  final dynamic userStats;
  final int backupCount;
  final DateTime? lastBackupDate;

  AppStats({
    required this.performanceStats,
    required this.userStats,
    required this.backupCount,
    this.lastBackupDate,
  });

  factory AppStats.empty() =>
      AppStats(performanceStats: {}, userStats: null, backupCount: 0);
}

/// ويدجت مراقب البنية التحتية
class InfrastructureMonitorWidget extends StatefulWidget {
  final Widget child;

  const InfrastructureMonitorWidget({super.key, required this.child});

  @override
  State<InfrastructureMonitorWidget> createState() =>
      _InfrastructureMonitorWidgetState();
}

class _InfrastructureMonitorWidgetState
    extends State<InfrastructureMonitorWidget> {
  final _infrastructure = AppInfrastructure();

  @override
  void initState() {
    super.initState();
    // إجراء فحص صحة دوري كل 5 دقائق
    Timer.periodic(const Duration(minutes: 5), (_) {
      _infrastructure.performHealthCheck();
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
