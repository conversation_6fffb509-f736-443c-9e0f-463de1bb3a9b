-- ===================================================================
-- جداول التسويق
-- ===================================================================
-- جدول حملات الإعلانات
CREATE TABLE IF NOT EXISTS ad_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    budget DECIMAL(12,2) NOT NULL CHECK (budget >= 0),
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_name ON ad_campaigns(name);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_is_active ON ad_campaigns(is_active);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_start_date ON ad_campaigns(start_date);
CREATE INDEX IF NOT EXISTS idx_ad_campaigns_end_date ON ad_campaigns(end_date);

-- ===================================================================
-- 2. جدول العروض والخصومات
-- ===================================================================
CREATE TABLE IF NOT EXISTS offers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    discount_percentage DECIMAL(5,2) NOT NULL CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
    discount_amount DECIMAL(12,2),
    min_purchase_amount DECIMAL(12,2),
    max_discount_amount DECIMAL(12,2),
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    applies_to VARCHAR(20) DEFAULT 'products' CHECK (applies_to IN ('products', 'categories', 'all')),
    target_ids JSONB DEFAULT '[]'::jsonb, -- معرفات المنتجات أو الفئات
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_offers_is_active ON offers(is_active);
CREATE INDEX IF NOT EXISTS idx_offers_start_date ON offers(start_date);
CREATE INDEX IF NOT EXISTS idx_offers_end_date ON offers(end_date);
CREATE INDEX IF NOT EXISTS idx_offers_created_by ON offers(created_by);

-- ===================================================================
-- 3. جدول الكوبونات
-- ===================================================================
CREATE TABLE IF NOT EXISTS coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value DECIMAL(12,2) NOT NULL CHECK (discount_value > 0),
    min_purchase_amount DECIMAL(12,2),
    max_discount_amount DECIMAL(12,2),
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    usage_limit INTEGER,
    usage_count INTEGER DEFAULT 0,
    user_usage_limit INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_is_active ON coupons(is_active);
CREATE INDEX IF NOT EXISTS idx_coupons_start_date ON coupons(start_date);
CREATE INDEX IF NOT EXISTS idx_coupons_end_date ON coupons(end_date);

-- ===================================================================
-- 4. جدول استخدام الكوبونات
-- ===================================================================
CREATE TABLE IF NOT EXISTS coupon_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    coupon_id UUID NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    discount_amount DECIMAL(12,2) NOT NULL,
    used_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(coupon_id, user_id, order_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_coupon_usage_coupon_id ON coupon_usage(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_user_id ON coupon_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usage_order_id ON coupon_usage(order_id);

-- ===================================================================
-- 5. جدول الإعلانات
-- ===================================================================
CREATE TABLE IF NOT EXISTS advertisements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    image_url TEXT,
    target_url TEXT, -- تم تغيير link_url إلى target_url ليتوافق مع النموذج
    display_order INTEGER DEFAULT 0, -- تم إضافة display_order
    campaign_id UUID REFERENCES ad_campaigns(id) ON DELETE SET NULL, -- تم إضافة campaign_id
    position VARCHAR(50) NOT NULL, -- 'banner', 'sidebar', 'popup', 'inline'
    target_audience JSONB DEFAULT '{}'::jsonb, -- معايير الاستهداف
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    click_count INTEGER DEFAULT 0,
    impression_count INTEGER DEFAULT 0, -- تم تغيير view_count إلى impression_count ليتوافق مع النموذج
    budget DECIMAL(10,2),
    cost_per_click DECIMAL(6,2),
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (end_date > start_date)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_advertisements_position ON advertisements(position);
CREATE INDEX IF NOT EXISTS idx_advertisements_is_active ON advertisements(is_active);
CREATE INDEX IF NOT EXISTS idx_advertisements_start_date ON advertisements(start_date);
CREATE INDEX IF NOT EXISTS idx_advertisements_end_date ON advertisements(end_date);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================
-- دالة لزيادة عدد مرات ظهور الإعلان
CREATE OR REPLACE FUNCTION increment_ad_impression_count(ad_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    UPDATE advertisements
    SET impression_count = impression_count + 1
    WHERE id = ad_id;
END;
$$;

-- دالة لزيادة عدد النقرات على الإعلان
CREATE OR REPLACE FUNCTION increment_ad_click_count(ad_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    UPDATE advertisements
    SET click_count = click_count + 1
    WHERE id = ad_id;
END;
$$;

