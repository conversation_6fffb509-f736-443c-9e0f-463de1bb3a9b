import 'package:flutter/material.dart';

/// نموذج الإشعارات الموحد
/// تم دمج NotificationModel و EnhancedNotificationModel في نموذج واحد
class NotificationModel {
  final String id;
  final String userId;
  final String? templateId;
  final String title;
  final String body;
  final String type;
  final Map<String, dynamic>? data;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? sentAt;
  final String? deliveryStatus;
  final String? priority;

  // الحقول المضافة من EnhancedNotificationModel
  final String? channel; // email, sms, push, in_app
  final Map<String, dynamic>? channelData;
  final bool isDelivered;
  final DateTime? deliveredAt;
  final bool isActionable;
  final String? actionType;
  final String? actionData;
  final int priorityLevel;
  final bool isArchived;

  NotificationModel({
    required this.id,
    required this.userId,
    this.templateId,
    required this.title,
    required this.body,
    required this.type,
    this.data,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
    this.sentAt,
    this.deliveryStatus,
    this.priority = 'medium',
    this.channel,
    this.channelData,
    this.isDelivered = false,
    this.deliveredAt,
    this.isActionable = false,
    this.actionType,
    this.actionData,
    int? priorityLevel,
    this.isArchived = false,
  }) : priorityLevel = priorityLevel ?? _getPriorityLevel(priority ?? 'medium');

  // دالة مساعدة لتحويل قيمة priority النصية إلى رقم
  static int _getPriorityLevel(String priority) {
    switch (priority) {
      case 'low':
        return 1;
      case 'medium':
        return 2;
      case 'high':
        return 3;
      case 'urgent':
        return 4;
      default:
        return 2;
    }
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String? ?? '',
      userId: json['user_id'] as String? ?? '',
      templateId: json['template_id'] as String?,
      title: json['title'] as String? ?? '',
      body: json['body'] as String? ?? '',
      type: json['type'] as String? ?? '',
      data: json['data'] as Map<String, dynamic>?,
      isRead: json['is_read'] as bool? ?? false,
      createdAt:
          DateTime.tryParse(json['created_at'] as String? ?? '') ??
          DateTime.now(),
      readAt:
          json['read_at'] != null
              ? DateTime.tryParse(json['read_at'] as String)
              : null,
      sentAt:
          json['sent_at'] != null
              ? DateTime.tryParse(json['sent_at'] as String)
              : null,
      deliveryStatus: json['delivery_status'] as String?,
      priority: json['priority'] as String? ?? 'medium',
      channel: json['channel'] as String?,
      channelData: json['channel_data'] as Map<String, dynamic>?,
      isDelivered: json['is_delivered'] as bool? ?? false,
      deliveredAt:
          json['delivered_at'] != null
              ? DateTime.tryParse(json['delivered_at'] as String)
              : null,
      isActionable: json['is_actionable'] as bool? ?? false,
      actionType: json['action_type'] as String?,
      actionData: json['action_data'] as String?,
      priorityLevel: _getPriorityLevel(json['priority'] as String? ?? 'medium'),
      isArchived: json['is_archived'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'template_id': templateId,
      'title': title,
      'body': body,
      'type': type,
      'data': data,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'delivery_status': deliveryStatus,
      'priority': priority,
      'channel': channel,
      'channel_data': channelData,
      'is_delivered': isDelivered,
      'delivered_at': deliveredAt?.toIso8601String(),
      'is_actionable': isActionable,
      'action_type': actionType,
      'action_data': actionData,
      'is_archived': isArchived,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? templateId,
    String? title,
    String? body,
    String? type,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? sentAt,
    String? deliveryStatus,
    String? priority,
    String? channel,
    Map<String, dynamic>? channelData,
    bool? isDelivered,
    DateTime? deliveredAt,
    bool? isActionable,
    String? actionType,
    String? actionData,
    int? priorityLevel,
    bool? isArchived,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      templateId: templateId ?? this.templateId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      sentAt: sentAt ?? this.sentAt,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      priority: priority ?? this.priority,
      channel: channel ?? this.channel,
      channelData: channelData ?? this.channelData,
      isDelivered: isDelivered ?? this.isDelivered,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      isActionable: isActionable ?? this.isActionable,
      actionType: actionType ?? this.actionType,
      actionData: actionData ?? this.actionData,
      priorityLevel:
          priority != null
              ? _getPriorityLevel(priority)
              : (priorityLevel ?? this.priorityLevel),
      isArchived: isArchived ?? this.isArchived,
    );
  }

  String get channelNameArabic {
    if (channel == null) return '';

    switch (channel) {
      case 'email':
        return 'بريد إلكتروني';
      case 'sms':
        return 'رسالة نصية';
      case 'push':
        return 'إشعار فوري';
      case 'in_app':
        return 'داخل التطبيق';
      default:
        return channel!;
    }
  }

  String get priorityNameArabic {
    switch (priorityLevel) {
      case 1:
        return 'منخفضة';
      case 2:
        return 'متوسطة';
      case 3:
        return 'عالية';
      case 4:
        return 'عاجلة';
      default:
        return 'متوسطة';
    }
  }

  Color get priorityColor {
    switch (priorityLevel) {
      case 1:
        return Colors.green;
      case 2:
        return Colors.blue;
      case 3:
        return Colors.orange;
      case 4:
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}
