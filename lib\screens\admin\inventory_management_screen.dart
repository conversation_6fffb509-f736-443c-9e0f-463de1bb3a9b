import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/inventory_report_service.dart';
import 'package:motorcycle_parts_shop/core/services/inventory_service.dart';
import 'package:motorcycle_parts_shop/core/widgets/custom_app_bar.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/models/inventory/inventory_movement_model.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:provider/provider.dart';

/// شاشة إدارة المخزون
/// تستخدم للمشرفين لإدارة مخزون المنتجات وعرض حركة المخزون
class InventoryManagementScreen extends StatefulWidget {
  const InventoryManagementScreen({super.key});

  @override
  State<InventoryManagementScreen> createState() =>
      _InventoryManagementScreenState();
}

class _InventoryManagementScreenState extends State<InventoryManagementScreen>
    with SingleTickerProviderStateMixin {
  final InventoryService _inventoryService = InventoryService();
  final InventoryReportService _reportService = InventoryReportService();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  late TabController _tabController;
  List<ProductModel> _products = [];
  List<ProductModel> _filteredProducts = [];
  List<ProductModel> _lowStockProducts = [];
  List<InventoryMovementModel> _recentMovements = [];
  bool _isLoading = true;
  String? _selectedProductId;
  ProductModel? _selectedProduct;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تهيئة خدمة المخزون
      if (!_inventoryService.isInitialized) {
        await _inventoryService.initialize();
      }

      // الحصول على جميع المنتجات
      final supabaseService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      final products = await supabaseService.getAllProducts();

      // الحصول على المنتجات منخفضة المخزون (أقل من 10 قطع)
      final lowStockProducts = await _inventoryService.getLowStockProducts(10);

      // الحصول على آخر 20 حركة مخزون
      final recentMovements = await _getRecentMovements();

      setState(() {
        _products = products;
        _filteredProducts = products;
        _lowStockProducts = lowStockProducts;
        _recentMovements = recentMovements;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تحميل البيانات: $e');
    }
  }

  /// الحصول على آخر حركات المخزون
  Future<List<InventoryMovementModel>> _getRecentMovements() async {
    try {
      final movements = await _inventoryService.getInventoryMovementsByReason(
        'admin_adjustment',
      );
      return movements.take(20).toList(); // الحصول على آخر 20 حركة فقط
    } catch (e) {
      debugPrint('خطأ في الحصول على حركات المخزون: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  void _searchProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = _products;
      } else {
        _filteredProducts =
            _products.where((product) {
              return product.name.contains(query) ||
                  product.sku.contains(query) ||
                  product.brand.contains(query);
            }).toList();
      }
    });
  }

  /// تحديث كمية المنتج
  Future<void> _updateProductQuantity() async {
    if (_selectedProductId == null) {
      _showErrorSnackBar('الرجاء اختيار منتج أولاً');
      return;
    }

    final quantityText = _quantityController.text.trim();
    if (quantityText.isEmpty) {
      _showErrorSnackBar('الرجاء إدخال الكمية');
      return;
    }

    final newQuantity = int.tryParse(quantityText);
    if (newQuantity == null || newQuantity < 0) {
      _showErrorSnackBar('الرجاء إدخال كمية صحيحة');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final notes = _notesController.text.trim();
      final success = await _inventoryService.updateProductQuantity(
        productId: _selectedProductId!,
        newQuantity: newQuantity,
        notes: notes.isNotEmpty ? notes : null,
      );

      if (success) {
        await _loadData();
        _quantityController.clear();
        _notesController.clear();
        _showSuccessSnackBar('تم تحديث كمية المنتج بنجاح');
      } else {
        _showErrorSnackBar('فشل تحديث كمية المنتج');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تحديث كمية المنتج: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// الانتقال إلى شاشة إضافة منتج جديد
  void _navigateToAddProduct() {
    Navigator.pushNamed(context, '/add-product').then((_) {
      // إعادة تحميل البيانات عند العودة من شاشة إضافة المنتج
      _loadData();
    });
  }

  /// تعديل منتج
  void _editProduct(ProductModel product) {
    Navigator.pushNamed(
      context,
      '/edit-product',
      arguments: {'product': product},
    ).then((_) {
      // إعادة تحميل البيانات عند العودة من شاشة تعديل المنتج
      _loadData();
    });
  }

  /// عرض تأكيد حذف المنتج
  void _showDeleteConfirmation(ProductModel product) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف المنتج "${product.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deleteProduct(product);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  /// حذف منتج
  Future<void> _deleteProduct(ProductModel product) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final supabaseService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      final success = await supabaseService.deleteProduct(product.id);

      if (success) {
        _showSuccessSnackBar('تم حذف المنتج بنجاح');
        // إعادة تحميل البيانات بعد الحذف
        await _loadData();
      } else {
        _showErrorSnackBar('فشل حذف المنتج');
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء حذف المنتج: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// اختيار منتج
  void _selectProduct(ProductModel product) {
    setState(() {
      _selectedProductId = product.id;
      _selectedProduct = product;
      _quantityController.text = product.stockQuantity.toString();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'إدارة المخزون', showBackButton: true),
      body:
          _isLoading
              ? const Center(child: LoadingIndicator())
              : Column(
                children: [
                  _buildTabBar(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildProductsTab(),
                        _buildLowStockTab(),
                        _buildMovementsTab(),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  /// بناء شريط التبويب
  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).primaryColor,
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(text: 'المنتجات'),
              Tab(text: 'المخزون المنخفض'),
              Tab(text: 'حركة المخزون'),
            ],
          ),
          Container(
            color: Theme.of(context).primaryColor.withOpacity(0.8),
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.add_circle, color: Colors.white),
                  label: const Text(
                    'إضافة منتج جديد',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: _navigateToAddProduct,
                ),
                TextButton.icon(
                  icon: const Icon(Icons.print, color: Colors.white),
                  label: const Text(
                    'تصدير تقرير',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: _showExportOptions,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب المنتجات
  Widget _buildProductsTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن منتج...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            onChanged: _searchProducts,
          ),
        ),
        Expanded(
          child:
              _filteredProducts.isEmpty
                  ? const Center(child: Text('لا توجد منتجات متاحة'))
                  : ListView.builder(
                    itemCount: _filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = _filteredProducts[index];
                      final isSelected = product.id == _selectedProductId;
                      return ListTile(
                        title: Text(product.name),
                        subtitle: Text(
                          'الكود: ${product.sku} | المخزون: ${product.stockQuantity}',
                        ),
                        leading:
                            product.imageUrls.isNotEmpty
                                ? Image.network(
                                  product.imageUrls.first,
                                  width: 50,
                                  height: 50,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.image_not_supported,
                                    );
                                  },
                                )
                                : const Icon(Icons.inventory),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit, color: Colors.blue),
                              onPressed: () => _editProduct(product),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _showDeleteConfirmation(product),
                            ),
                            Icon(
                              Icons.check_circle,
                              color:
                                  isSelected
                                      ? Colors.green
                                      : Colors.transparent,
                            ),
                          ],
                        ),
                        selected: isSelected,
                        onTap: () => _selectProduct(product),
                      );
                    },
                  ),
        ),
        if (_selectedProduct != null) _buildQuantityUpdateForm(),
      ],
    );
  }

  /// بناء نموذج تحديث الكمية
  Widget _buildQuantityUpdateForm() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تحديث مخزون: ${_selectedProduct?.name}',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16.0),
          ),
          const SizedBox(height: 16.0),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _quantityController,
                  decoration: const InputDecoration(
                    labelText: 'الكمية الجديدة',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16.0),
              ElevatedButton(
                onPressed: _updateProductQuantity,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                ),
                child: const Text('تحديث'),
              ),
            ],
          ),
          const SizedBox(height: 16.0),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'ملاحظات',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  /// بناء تبويب المخزون المنخفض
  Widget _buildLowStockTab() {
    return _lowStockProducts.isEmpty
        ? const Center(child: Text('لا توجد منتجات منخفضة المخزون'))
        : ListView.builder(
          itemCount: _lowStockProducts.length,
          itemBuilder: (context, index) {
            final product = _lowStockProducts[index];
            return ListTile(
              title: Text(product.name),
              subtitle: Text(
                'الكود: ${product.sku} | المخزون: ${product.stockQuantity}',
                style: TextStyle(
                  color:
                      product.stockQuantity <= 5 ? Colors.red : Colors.orange,
                ),
              ),
              leading:
                  product.imageUrls.isNotEmpty
                      ? Image.network(
                        product.imageUrls.first,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.image_not_supported);
                        },
                      )
                      : const Icon(Icons.inventory),
              trailing: IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _selectProduct(product),
              ),
            );
          },
        );
  }

  /// بناء تبويب حركة المخزون
  Widget _buildMovementsTab() {
    return _recentMovements.isEmpty
        ? const Center(child: Text('لا توجد حركات مخزون حديثة'))
        : ListView.builder(
          itemCount: _recentMovements.length,
          itemBuilder: (context, index) {
            final movement = _recentMovements[index];
            final product = _products.firstWhere(
              (p) => p.id == movement.productId,
              orElse:
                  () => ProductModel(
                    id: 'غير معروف',
                    name: 'منتج غير معروف',
                    description: '',
                    price: 0,
                    categoryId: '',
                    companyId: '',
                    imageUrls: [],
                    specifications: {},
                    stockQuantity: 0,
                    viewCount: 0,
                    isFeatured: false,
                    isBestSelling: false,
                    isNew: false,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                    sku: '',
                    brand: '',
                    isAvailable: false,
                  ),
            );

            final isPositive = movement.quantityChange > 0;

            return ListTile(
              title: Text(product.name),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التغيير: ${isPositive ? "+" : ""}${movement.quantityChange}',
                    style: TextStyle(
                      color: isPositive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('السبب: ${movement.reason}'),
                  if (movement.notes != null && movement.notes!.isNotEmpty)
                    Text('ملاحظات: ${movement.notes}'),
                  Text(
                    'التاريخ: ${_formatDate(movement.createdAt)}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ],
              ),
              leading: const Icon(Icons.history),
            );
          },
        );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day} ${date.hour}:${date.minute}';
  }

  /// عرض خيارات التصدير
  void _showExportOptions() {
    final currentTab = _tabController.index;
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'تصدير التقرير',
                  style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16.0),
                ListTile(
                  leading: const Icon(Icons.picture_as_pdf),
                  title: const Text('تصدير كملف PDF'),
                  onTap: () {
                    Navigator.pop(context);
                    _exportAsPdf(currentTab);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.table_chart),
                  title: const Text('تصدير كملف CSV'),
                  onTap: () {
                    Navigator.pop(context);
                    _exportAsCsv(currentTab);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.print),
                  title: const Text('طباعة التقرير'),
                  onTap: () {
                    Navigator.pop(context);
                    _printReport(currentTab);
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// تصدير كملف PDF
  Future<void> _exportAsPdf(int tabIndex) async {
    try {
      setState(() {
        _isLoading = true;
      });

      String fileName = '';
      String? filePath;

      switch (tabIndex) {
        case 0: // تبويب المنتجات
          final pdf = await _reportService.generateProductsReport(_products);
          fileName = 'products_report_${DateTime.now().millisecondsSinceEpoch}';
          filePath = await _reportService.saveReportAsPdf(pdf, fileName);
          break;
        case 1: // تبويب المخزون المنخفض
          final pdf = await _reportService.generateLowStockReport(
            _lowStockProducts,
          );
          fileName =
              'low_stock_report_${DateTime.now().millisecondsSinceEpoch}';
          filePath = await _reportService.saveReportAsPdf(pdf, fileName);
          break;
        case 2: // تبويب حركة المخزون
          final pdf = await _reportService.generateMovementsReport(
            _recentMovements,
            _products,
          );
          fileName =
              'inventory_movements_report_${DateTime.now().millisecondsSinceEpoch}';
          filePath = await _reportService.saveReportAsPdf(pdf, fileName);
          break;
      }

      setState(() {
        _isLoading = false;
      });

      if (filePath != null) {
        _showSuccessSnackBar('تم تصدير التقرير بنجاح إلى: $filePath');
      } else {
        _showErrorSnackBar('فشل تصدير التقرير');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تصدير التقرير: $e');
    }
  }

  /// تصدير كملف CSV
  Future<void> _exportAsCsv(int tabIndex) async {
    try {
      setState(() {
        _isLoading = true;
      });

      String fileName = '';
      String? filePath;

      switch (tabIndex) {
        case 0: // تبويب المنتجات
          fileName = 'products_report_${DateTime.now().millisecondsSinceEpoch}';
          filePath = await _reportService.exportProductsAsCsv(
            _products,
            fileName,
          );
          break;
        case 1: // تبويب المخزون المنخفض
          fileName =
              'low_stock_report_${DateTime.now().millisecondsSinceEpoch}';
          filePath = await _reportService.exportProductsAsCsv(
            _lowStockProducts,
            fileName,
          );
          break;
        default:
          _showErrorSnackBar('تصدير CSV غير متاح لهذا النوع من التقارير');
          setState(() {
            _isLoading = false;
          });
          return;
      }

      setState(() {
        _isLoading = false;
      });

      if (filePath != null) {
        _showSuccessSnackBar('تم تصدير التقرير بنجاح إلى: $filePath');
      } else {
        _showErrorSnackBar('فشل تصدير التقرير');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء تصدير التقرير: $e');
    }
  }

  /// طباعة التقرير
  Future<void> _printReport(int tabIndex) async {
    try {
      setState(() {
        _isLoading = true;
      });

      switch (tabIndex) {
        case 0: // تبويب المنتجات
          final pdf = await _reportService.generateProductsReport(_products);
          await _reportService.printReport(pdf);
          break;
        case 1: // تبويب المخزون المنخفض
          final pdf = await _reportService.generateLowStockReport(
            _lowStockProducts,
          );
          await _reportService.printReport(pdf);
          break;
        case 2: // تبويب حركة المخزون
          final pdf = await _reportService.generateMovementsReport(
            _recentMovements,
            _products,
          );
          await _reportService.printReport(pdf);
          break;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('حدث خطأ أثناء طباعة التقرير: $e');
    }
  }
}
