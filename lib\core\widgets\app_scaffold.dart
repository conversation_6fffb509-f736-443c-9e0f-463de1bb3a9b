import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/widgets/advanced_app_bar.dart';
import 'package:motorcycle_parts_shop/core/widgets/advanced_bottom_navigation.dart';

/// هيكل تطبيق موحد يجمع بين شريط التطبيق العلوي وشريط التنقل السفلي
/// يمكن استخدامه في جميع شاشات التطبيق لتوحيد المظهر والتجربة
class AppScaffold extends StatefulWidget {
  final Widget body;
  final String title;
  final String? subtitle;
  final bool showBackButton;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final bool showBottomNavigation;
  final int currentIndex;
  final Function(int)? onNavItemSelected;
  final bool extendBodyBehindAppBar;
  final bool extendBody;
  final Color? backgroundColor;
  final Widget? drawer;
  final Widget? endDrawer;
  final bool showSearchButton;
  final bool showNotificationButton;
  final int notificationCount;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onNotificationPressed;
  final bool showBadge;
  final String? badgeText;
  final bool showAppBar;
  final PreferredSizeWidget? bottom;
  final LinearGradient? appBarGradient;
  final LinearGradient? bottomNavGradient;
  final bool useGlassEffect;
  final bool roundedAppBar;
  final bool roundedBottomNav;
  final Widget? customTitle;
  final bool centerTitle;
  final bool showLogo;
  final Widget? logo;
  final bool expandedAppBar;
  final Widget? appBarBackground;
  final List<BottomNavItem>? customNavItems;

  const AppScaffold({
    super.key,
    required this.body,
    this.title = '',
    this.subtitle,
    this.showBackButton = true,
    this.actions,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.showBottomNavigation = true,
    this.currentIndex = 0,
    this.onNavItemSelected,
    this.extendBodyBehindAppBar = false,
    this.extendBody = true,
    this.backgroundColor,
    this.drawer,
    this.endDrawer,
    this.showSearchButton = false,
    this.showNotificationButton = false,
    this.notificationCount = 0,
    this.onSearchPressed,
    this.onNotificationPressed,
    this.showBadge = false,
    this.badgeText,
    this.showAppBar = true,
    this.bottom,
    this.appBarGradient,
    this.bottomNavGradient,
    this.useGlassEffect = true,
    this.roundedAppBar = true,
    this.roundedBottomNav = true,
    this.customTitle,
    this.centerTitle = false,
    this.showLogo = false,
    this.logo,
    this.expandedAppBar = false,
    this.appBarBackground,
    this.customNavItems,
  });

  @override
  State<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold> {
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
  }

  @override
  void didUpdateWidget(AppScaffold oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _currentIndex = widget.currentIndex;
    }
  }

  void _handleNavItemSelected(int index) {
    setState(() {
      _currentIndex = index;
    });

    if (widget.onNavItemSelected != null) {
      widget.onNavItemSelected!(index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: widget.extendBodyBehindAppBar,
      extendBody: widget.extendBody,
      backgroundColor: widget.backgroundColor ?? AppTheme.backgroundColor,
      appBar:
          widget.showAppBar
              ? AdvancedAppBar(
                title: widget.title,
                subtitle: widget.subtitle,
                showBackButton: widget.showBackButton,
                actions: widget.actions,
                bottom: widget.bottom,
                gradient: widget.appBarGradient,
                useGlassEffect: widget.useGlassEffect,
                showBadge: widget.showBadge,
                badgeText: widget.badgeText,
                roundedCorners: widget.roundedAppBar,
                customTitle: widget.customTitle,
                centerTitle: widget.centerTitle,
                showSearchButton: widget.showSearchButton,
                onSearchPressed: widget.onSearchPressed,
                showNotificationButton: widget.showNotificationButton,
                notificationCount: widget.notificationCount,
                onNotificationPressed: widget.onNotificationPressed,
                showLogo: widget.showLogo,
                logo: widget.logo,
                expandedHeight: widget.expandedAppBar,
                background: widget.appBarBackground,
              )
              : null,
      drawer: widget.drawer,
      endDrawer: widget.endDrawer,
      body: Column(children: [Expanded(child: widget.body), _AppFooter()]),
      floatingActionButton: widget.floatingActionButton,
      floatingActionButtonLocation:
          widget.floatingActionButtonLocation ??
          (widget.floatingActionButton != null
              ? FloatingActionButtonLocation.centerDocked
              : null),
      bottomNavigationBar:
          widget.showBottomNavigation
              ? AdvancedBottomNavigation(
                selectedIndex: _currentIndex,
                onItemSelected: _handleNavItemSelected,
                floatingActionButton: widget.floatingActionButton,
                gradient: widget.bottomNavGradient,
                useGlassEffect: widget.useGlassEffect,
                roundedTop: widget.roundedBottomNav,
                items: widget.customNavItems ?? _getDefaultNavItems(),
              )
              : null,
    );
  }

  List<BottomNavItem> _getDefaultNavItems() {
    return [
      const BottomNavItem(icon: Icons.home_rounded, label: 'الرئيسية'),
      const BottomNavItem(icon: Icons.category_rounded, label: 'الفئات'),
      const BottomNavItem(
        icon: Icons.shopping_cart_rounded,
        label: 'السلة',
        notificationCount: 0,
      ),
      const BottomNavItem(icon: Icons.person_rounded, label: 'حسابي'),
    ];
  }
}

/// شريط حالة صغير في الأسفل
class _AppFooter extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        border: const Border(
          top: BorderSide(color: Colors.white24, width: 0.5),
        ),
      ),
      child: Center(
        child: Text(
          'جميع الحقوق محفوظة © 2024 متجر قطع غيار الدراجات النارية',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 13,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.2,
          ),
        ),
      ),
    );
  }
}
