import 'package:flutter/foundation.dart';
import 'package:motorcycle_parts_shop/core/utils/service_locator.dart';
import 'package:motorcycle_parts_shop/models/user/user_app_settings_model.dart';
import 'package:motorcycle_parts_shop/models/user/user_privacy_settings_model.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة إدارة إعدادات المستخدم
/// تتعامل مع إعدادات التطبيق وإعدادات الخصوصية
class UserSettingsService extends ChangeNotifier {
  final SupabaseClient _client;
  final _storage = ServiceLocator.storage;

  UserAppSettingsModel? _appSettings;
  UserPrivacySettingsModel? _privacySettings;

  bool _isLoading = false;
  String? _error;

  UserSettingsService({required SupabaseClient client}) : _client = client;

  /// الحصول على إعدادات التطبيق
  UserAppSettingsModel? get appSettings => _appSettings;

  /// الحصول على إعدادات الخصوصية
  UserPrivacySettingsModel? get privacySettings => _privacySettings;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get error => _error;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      // محاولة استرداد الإعدادات من التخزين المحلي أولاً
      await _loadFromLocalStorage();

      // ثم تحديثها من الخادم إذا كان المستخدم مسجلاً
      final userId = _client.auth.currentUser?.id;
      if (userId != null) {
        await fetchUserSettings(userId);
      } else {
        _error = 'لم يتم العثور على معرف المستخدم';
        debugPrint(_error);
      }
    } catch (e) {
      _error = 'فشل في تهيئة خدمة إعدادات المستخدم: $e';
      debugPrint(_error);
      notifyListeners();
    }
  }

  /// تحميل الإعدادات من التخزين المحلي
  Future<void> _loadFromLocalStorage() async {
    try {
      final appSettingsJson = await _storage.getData('user_app_settings');
      if (appSettingsJson != null) {
        _appSettings = UserAppSettingsModel.fromJson(appSettingsJson);
      }

      final privacySettingsJson = await _storage.getData(
        'user_privacy_settings',
      );
      if (privacySettingsJson != null) {
        _privacySettings = UserPrivacySettingsModel.fromJson(
          privacySettingsJson,
        );
      }
      notifyListeners();
    } catch (e) {
      debugPrint('فشل في تحميل الإعدادات من التخزين المحلي: $e');
    }
  }

  /// حفظ الإعدادات في التخزين المحلي
  Future<void> _saveToLocalStorage() async {
    try {
      if (_appSettings != null) {
        await _storage.saveData('user_app_settings', _appSettings!.toJson());
      }
      if (_privacySettings != null) {
        await _storage.saveData(
          'user_privacy_settings',
          _privacySettings!.toJson(),
        );
      }
    } catch (e) {
      debugPrint('فشل في حفظ الإعدادات في التخزين المحلي: $e');
    }
  }

  /// جلب إعدادات المستخدم من الخادم
  Future<void> fetchUserSettings(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // جلب إعدادات التطبيق
      final appSettingsResponse =
          await _client
              .from('user_app_settings')
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      if (appSettingsResponse != null) {
        _appSettings = UserAppSettingsModel.fromJson(appSettingsResponse);
      } else {
        // إذا لم يتم العثور على إعدادات التطبيق، إنشاء إعدادات افتراضية
        final defaultAppSettings = UserAppSettingsModel.createDefault(userId);
        await _client
            .from('user_app_settings')
            .insert(defaultAppSettings.toJson());
        _appSettings = defaultAppSettings;
      }

      // جلب إعدادات الخصوصية
      final privacySettingsResponse =
          await _client
              .from('user_privacy_settings')
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      if (privacySettingsResponse != null) {
        _privacySettings = UserPrivacySettingsModel.fromJson(
          privacySettingsResponse,
        );
      } else {
        // إذا لم يتم العثور على إعدادات الخصوصية، إنشاء إعدادات افتراضية
        final defaultPrivacySettings = UserPrivacySettingsModel.createDefault(
          userId,
        );
        await _client
            .from('user_privacy_settings')
            .insert(defaultPrivacySettings.toJson());
        _privacySettings = defaultPrivacySettings;
      }

      // حفظ الإعدادات في التخزين المحلي
      await _saveToLocalStorage();
    } catch (e) {
      _error = 'فشل في جلب إعدادات المستخدم: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحديث إعدادات التطبيق
  Future<bool> updateAppSettings(UserAppSettingsModel settings) async {
    if (_appSettings == null) {
      _error = 'لا توجد بيانات إعدادات التطبيق للتحديث';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // تحديث الإعدادات في الخادم
      await _client
          .from('user_app_settings')
          .update(settings.toJson())
          .eq('id', settings.id);

      // تحديث الإعدادات المحلية
      _appSettings = settings;
      await _saveToLocalStorage();
      return true;
    } catch (e) {
      _error = 'فشل في تحديث إعدادات التطبيق: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحديث إعدادات الخصوصية
  Future<bool> updatePrivacySettings(UserPrivacySettingsModel settings) async {
    if (_privacySettings == null) {
      _error = 'لا توجد بيانات إعدادات الخصوصية للتحديث';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // تحديث الإعدادات في الخادم
      await _client
          .from('user_privacy_settings')
          .update(settings.toJson())
          .eq('id', settings.id);

      // تحديث الإعدادات المحلية
      _privacySettings = settings;
      await _saveToLocalStorage();
      return true;
    } catch (e) {
      _error = 'فشل في تحديث إعدادات الخصوصية: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إنشاء إعدادات افتراضية لمستخدم جديد
  Future<bool> createDefaultSettings(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('إنشاء إعدادات افتراضية للمستخدم: $userId');

      // إنشاء إعدادات التطبيق الافتراضية
      final appSettings = UserAppSettingsModel.createDefault(userId);
      debugPrint('إعدادات التطبيق الافتراضية: ${appSettings.toJson()}');

      // تحقق من وجود إعدادات للمستخدم قبل الإدراج
      final existingSettings =
          await _client
              .from('user_app_settings')
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      if (existingSettings == null) {
        // إدراج إعدادات جديدة فقط إذا لم تكن موجودة
        await _client.from('user_app_settings').insert(appSettings.toJson());
        debugPrint('تم إنشاء إعدادات التطبيق بنجاح');
      } else {
        debugPrint('إعدادات التطبيق موجودة بالفعل: $existingSettings');
      }

      // إنشاء إعدادات الخصوصية الافتراضية
      final privacySettings = UserPrivacySettingsModel.createDefault(userId);

      // تحقق من وجود إعدادات الخصوصية قبل الإدراج
      final existingPrivacy =
          await _client
              .from('user_privacy_settings')
              .select()
              .eq('user_id', userId)
              .maybeSingle();

      if (existingPrivacy == null) {
        // إدراج إعدادات جديدة فقط إذا لم تكن موجودة
        await _client
            .from('user_privacy_settings')
            .insert(privacySettings.toJson());
        debugPrint('تم إنشاء إعدادات الخصوصية بنجاح');
      } else {
        debugPrint('إعدادات الخصوصية موجودة بالفعل: $existingPrivacy');
      }

      // تحديث الإعدادات المحلية
      _appSettings =
          existingSettings != null
              ? UserAppSettingsModel.fromJson(existingSettings)
              : appSettings;
      _privacySettings =
          existingPrivacy != null
              ? UserPrivacySettingsModel.fromJson(existingPrivacy)
              : privacySettings;

      await _saveToLocalStorage();
      return true;
    } catch (e) {
      _error = 'فشل في إنشاء إعدادات افتراضية: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تغيير سمة التطبيق
  Future<bool> changeTheme(String theme) async {
    if (_appSettings == null) {
      _error = 'لا توجد بيانات إعدادات التطبيق';
      notifyListeners();
      return false;
    }
    if (!UserAppSettingsModel.supportedThemes.contains(theme)) {
      _error = 'السمة غير مدعومة';
      notifyListeners();
      return false;
    }

    final updatedSettings = _appSettings!.copyWith(
      theme: theme,
      updatedAt: DateTime.now(),
    );

    return await updateAppSettings(updatedSettings);
  }

  // تم إزالة وظيفة تغيير اللغة لأن التطبيق يدعم اللغة العربية فقط

  /// تغيير حجم النص
  Future<bool> changeTextSize(int textSize) async {
    if (_appSettings == null) {
      _error = 'لا توجد بيانات إعدادات التطبيق';
      notifyListeners();
      return false;
    }
    if (textSize < 12 || textSize > 24) {
      _error = 'حجم النص يجب أن يكون بين 12 و24';
      notifyListeners();
      return false;
    }

    final updatedSettings = _appSettings!.copyWith(
      textSize: textSize,
      updatedAt: DateTime.now(),
    );

    return await updateAppSettings(updatedSettings);
  }

  /// تفعيل/تعطيل الوضع الداكن
  Future<bool> toggleDarkMode(bool enable) async {
    if (_appSettings == null) {
      _error = 'لا توجد بيانات إعدادات التطبيق';
      notifyListeners();
      return false;
    }

    final updatedSettings = _appSettings!.copyWith(
      theme: enable ? 'dark' : 'light',
      updatedAt: DateTime.now(),
    );

    return await updateAppSettings(updatedSettings);
  }

  /// تفعيل/تعطيل الإشعارات
  Future<bool> toggleNotifications(bool enable) async {
    if (_appSettings == null) {
      _error = 'لا توجد بيانات إعدادات التطبيق';
      notifyListeners();
      return false;
    }

    final updatedSettings = _appSettings!.copyWith(
      pushEnabled: enable,
      updatedAt: DateTime.now(),
    );

    return await updateAppSettings(updatedSettings);
  }

  /// تفعيل/تعطيل مشاركة بيانات الملف الشخصي
  Future<bool> toggleShareProfileData(bool enable) async {
    if (_privacySettings == null) {
      _error = 'لا توجد بيانات إعدادات الخصوصية';
      notifyListeners();
      return false;
    }

    final updatedSettings = _privacySettings!.copyWith(
      shareProfileData: enable,
      updatedAt: DateTime.now(),
    );

    return await updatePrivacySettings(updatedSettings);
  }

  /// تفعيل/تعطيل مشاركة سجل الطلبات
  Future<bool> toggleShareOrderHistory(bool enable) async {
    if (_privacySettings == null) {
      _error = 'لا توجد بيانات إعدادات الخصوصية';
      notifyListeners();
      return false;
    }

    final updatedSettings = _privacySettings!.copyWith(
      shareOrderHistory: enable,
      updatedAt: DateTime.now(),
    );

    return await updatePrivacySettings(updatedSettings);
  }

  /// تفعيل/تعطيل الإعلانات المستهدفة
  Future<bool> toggleTargetedAds(bool enable) async {
    if (_privacySettings == null) {
      _error = 'لا توجد بيانات إعدادات الخصوصية';
      notifyListeners();
      return false;
    }

    final updatedSettings = _privacySettings!.copyWith(
      allowTargetedAds: enable,
      updatedAt: DateTime.now(),
    );

    return await updatePrivacySettings(updatedSettings);
  }

  /// مسح البيانات المحلية عند تسجيل الخروج
  Future<void> clearLocalData() async {
    await _storage.remove(
      'user_app_settings',
    ); // Changed from removeData to remove
    await _storage.remove(
      'user_privacy_settings',
    ); // Changed from removeData to remove
    _appSettings = null;
    _privacySettings = null;
    _error = null;
    notifyListeners();
  }
}
