// نموذج بيانات المستخدم

import 'dart:convert';

import 'package:motorcycle_parts_shop/models/validation_result.dart';


class UserModel {
  // معرف المستخدم الفريد (UUID)
  final String id;

  // عدد الطلبات (اختياري)
  final int? orderCount;

  // عدد العناصر في قائمة الرغبات (اختياري)
  final int? wishlistCount;

  // تم حذف حقل التقييم حسب المتطلبات

  // إجمالي المشتريات (اختياري)
  final double? totalSpent;

  // اسم المستخدم
  final String name;

  // البريد الإلكتروني للمستخدم
  final String email;

  // عنوان المستخدم (اختياري) - للتوافق الخلفي
  final String? address;

  // رقم الهاتف (اختياري) - للتوافق الخلفي
  final String? phone;

  // نوع الملف الشخصي (customer أو admin)
  final String profileType;

  // صلاحيات المستخدم (مصفوفة نصوص)
  // final List<String>? permissions;

  // تاريخ الميلاد (اختياري)
  final DateTime? birthDate;

  // المحافظة (اختياري)
  final String? governorate;

  // المركز (اختياري)
  final String? center;

  // تاريخ آخر تسجيل دخول (اختياري)
  final DateTime? lastLoginAt;

  // تاريخ إنشاء المستخدم
  final DateTime createdAt;

  // تاريخ آخر تحديث للمستخدم
  final DateTime updatedAt;

  // بيانات وصفية خام للمستخدم (اختياري)
  final Map<String, dynamic>? rawUserMetaData;

  // الحقول الجديدة المضافة للتوافق مع قاعدة البيانات
  final int? unreadNotificationsCount;
  final int? totalNotificationsCount;
  final DateTime? lastNotificationCheck;
  final bool isActive;

  // حالة التحقق من البريد الإلكتروني
  // final bool emailVerified;

  // تم دمج بيانات المستخدم الإضافية مباشرة في النموذج الرئيسي

  // بناء كائن UserModel
  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.address,
    this.phone,
    required this.profileType,
    this.birthDate,
    this.governorate,
    this.center,
    this.lastLoginAt,
    required this.createdAt,
    required this.updatedAt,
    this.orderCount,
    this.wishlistCount,
    // تم حذف averageRating
    this.totalSpent,
    this.rawUserMetaData,
    this.unreadNotificationsCount,
    this.totalNotificationsCount,
    this.lastNotificationCheck,
    this.isActive = true,
  }) : assert(
         profileType == 'customer' || profileType == 'admin',
         'profileType يجب أن يكون إما "customer" أو "admin"',
       );

  /// إنشاء كائن UserModel من JSON مع معالجة متقدمة للأخطاء
  factory UserModel.fromJson(Map<String, dynamic> json) {
    try {
      // التحقق من وجود الحقول الأساسية
      if (json['id'] == null) {
        throw FormatException('معرف المستخدم مفقود في بيانات JSON');
      }

      // معالجة التواريخ بشكل آمن
      DateTime? birthDateValue;
      if (json['birth_date'] != null) {
        try {
          birthDateValue = DateTime.parse(json['birth_date'].toString());
        } catch (e) {
          print('خطأ في تحليل تاريخ الميلاد: $e');
        }
      }

      DateTime lastLoginAtValue;
      try {
        lastLoginAtValue =
            DateTime.tryParse(json['last_login_at']?.toString() ?? '') ??
            DateTime.now();
      } catch (e) {
        print('خطأ في تحليل تاريخ آخر تسجيل دخول: $e');
        lastLoginAtValue = DateTime.now();
      }

      DateTime createdAtValue;
      try {
        createdAtValue =
            DateTime.tryParse(json['created_at']?.toString() ?? '') ??
            DateTime.now();
      } catch (e) {
        print('خطأ في تحليل تاريخ الإنشاء: $e');
        createdAtValue = DateTime.now();
      }

      DateTime updatedAtValue;
      try {
        updatedAtValue =
            DateTime.tryParse(json['updated_at']?.toString() ?? '') ??
            DateTime.now();
      } catch (e) {
        print('خطأ في تحليل تاريخ التحديث: $e');
        updatedAtValue = DateTime.now();
      }

      DateTime? lastNotificationCheckValue;
      if (json['last_notification_check'] != null) {
        try {
          lastNotificationCheckValue = DateTime.parse(
            json['last_notification_check'].toString(),
          );
        } catch (e) {
          print('خطأ في تحليل تاريخ آخر فحص للإشعارات: $e');
        }
      }

      // معالجة البيانات الوصفية بشكل آمن
      Map<String, dynamic>? metaData;
      if (json['raw_user_meta_data'] != null) {
        try {
          if (json['raw_user_meta_data'] is Map) {
            metaData = Map<String, dynamic>.from(
              json['raw_user_meta_data'] as Map,
            );
          } else if (json['raw_user_meta_data'] is String) {
            try {
              metaData = jsonDecode(json['raw_user_meta_data'] as String);
            } catch (e) {
              print('خطأ في تحليل البيانات الوصفية: $e');
            }
          }
        } catch (e) {
          print('خطأ في معالجة البيانات الوصفية: $e');
        }
      }

      // معالجة الأعداد بشكل آمن
      int? orderCountValue;
      if (json['order_count'] != null) {
        try {
          if (json['order_count'] is int) {
            orderCountValue = json['order_count'] as int;
          } else if (json['order_count'] is num) {
            orderCountValue = (json['order_count'] as num).toInt();
          } else if (json['order_count'] is String) {
            orderCountValue = int.tryParse(json['order_count'] as String);
          }
        } catch (e) {
          print('خطأ في تحليل عدد الطلبات: $e');
        }
      }

      int? wishlistCountValue;
      if (json['wishlist_count'] != null) {
        try {
          if (json['wishlist_count'] is int) {
            wishlistCountValue = json['wishlist_count'] as int;
          } else if (json['wishlist_count'] is num) {
            wishlistCountValue = (json['wishlist_count'] as num).toInt();
          } else if (json['wishlist_count'] is String) {
            wishlistCountValue = int.tryParse(json['wishlist_count'] as String);
          }
        } catch (e) {
          print('خطأ في تحليل عدد عناصر قائمة الرغبات: $e');
        }
      }

      double? totalSpentValue;
      if (json['total_spent'] != null) {
        try {
          if (json['total_spent'] is double) {
            totalSpentValue = json['total_spent'] as double;
          } else if (json['total_spent'] is num) {
            totalSpentValue = (json['total_spent'] as num).toDouble();
          } else if (json['total_spent'] is String) {
            totalSpentValue = double.tryParse(json['total_spent'] as String);
          }
        } catch (e) {
          print('خطأ في تحليل إجمالي المشتريات: $e');
        }
      }

      int? unreadNotificationsCountValue;
      if (json['unread_notifications_count'] != null) {
        try {
          if (json['unread_notifications_count'] is int) {
            unreadNotificationsCountValue =
                json['unread_notifications_count'] as int;
          } else if (json['unread_notifications_count'] is num) {
            unreadNotificationsCountValue =
                (json['unread_notifications_count'] as num).toInt();
          } else if (json['unread_notifications_count'] is String) {
            unreadNotificationsCountValue = int.tryParse(
              json['unread_notifications_count'] as String,
            );
          }
        } catch (e) {
          print('خطأ في تحليل عدد الإشعارات غير المقروءة: $e');
        }
      }

      int? totalNotificationsCountValue;
      if (json['total_notifications_count'] != null) {
        try {
          if (json['total_notifications_count'] is int) {
            totalNotificationsCountValue =
                json['total_notifications_count'] as int;
          } else if (json['total_notifications_count'] is num) {
            totalNotificationsCountValue =
                (json['total_notifications_count'] as num).toInt();
          } else if (json['total_notifications_count'] is String) {
            totalNotificationsCountValue = int.tryParse(
              json['total_notifications_count'] as String,
            );
          }
        } catch (e) {
          print('خطأ في تحليل إجمالي عدد الإشعارات: $e');
        }
      }

      // التحقق من نوع الملف الشخصي وتصحيحه إذا لزم الأمر
      String profileTypeValue = 'customer'; // القيمة الافتراضية
      if (json['profile_type'] != null) {
        String type = json['profile_type'].toString();
        if (type == 'admin' || type == 'customer') {
          profileTypeValue = type;
        } else {
          print(
            'تحذير: نوع الملف الشخصي غير صالح: $type، سيتم استخدام "customer" كقيمة افتراضية',
          );
        }
      }

      // إنشاء كائن المستخدم مع معالجة آمنة لجميع الحقول
      return UserModel(
        id: json['id'].toString(),
        name: json['name']?.toString() ?? 'مستخدم جديد',
        email: json['email']?.toString() ?? '<EMAIL>',
        address: json['address']?.toString(),
        phone: json['phone']?.toString(),
        profileType: profileTypeValue,
        birthDate: birthDateValue,
        governorate: json['governorate']?.toString(),
        center: json['center']?.toString(),
        lastLoginAt: lastLoginAtValue,
        createdAt: createdAtValue,
        updatedAt: updatedAtValue,
        orderCount: orderCountValue,
        wishlistCount: wishlistCountValue,
        totalSpent: totalSpentValue,
        rawUserMetaData: metaData,
        unreadNotificationsCount: unreadNotificationsCountValue,
        totalNotificationsCount: totalNotificationsCountValue,
        lastNotificationCheck: lastNotificationCheckValue,
        isActive: json['is_active'] is bool ? json['is_active'] as bool : true,
      );
    } catch (e, stackTrace) {
      // تسجيل الخطأ الرئيسي مع تفاصيل كاملة
      print('خطأ خطير في تحويل بيانات المستخدم: $e');
      print('تفاصيل الخطأ: $stackTrace');

      // إرجاع مستخدم افتراضي أو إعادة رمي الاستثناء حسب الحاجة
      if (json['id'] != null) {
        // محاولة إنشاء مستخدم بالحد الأدنى من البيانات
        return UserModel(
          id: json['id'].toString(),
          name: json['name']?.toString() ?? 'مستخدم غير معروف',
          email: json['email']?.toString() ?? '<EMAIL>',
          profileType: 'customer',
          lastLoginAt: DateTime.now(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else {
        // إذا لم يكن هناك معرف، أعد رمي الاستثناء
        throw FormatException('بيانات المستخدم غير صالحة أو مفقودة: $e');
      }
    }
  }

  // تحويل كائن UserModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'address': address,
      'phone': phone,
      'profile_type': profileType,
      'birth_date': birthDate?.toIso8601String(),
      'governorate': governorate,
      'center': center,
      'last_login_at': lastLoginAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'order_count': orderCount,
      'wishlist_count': wishlistCount,
      'total_spent': totalSpent,
      'raw_user_meta_data': rawUserMetaData,
      'unread_notifications_count': unreadNotificationsCount,
      'total_notifications_count': totalNotificationsCount,
      'last_notification_check': lastNotificationCheck?.toIso8601String(),
      'is_active': isActive,
    };
  }

  // إنشاء نسخة معدلة من الكائن مع إمكانية تغيير بعض القيم
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? address,
    String? phone,
    String? profileType,
    DateTime? birthDate,
    String? governorate,
    String? center,
    DateTime? lastLoginAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? orderCount,
    int? wishlistCount,
    double? totalSpent,
    Map<String, dynamic>? rawUserMetaData,
    int? unreadNotificationsCount,
    int? totalNotificationsCount,
    DateTime? lastNotificationCheck,
    bool? isActive,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      profileType: profileType ?? this.profileType,
      birthDate: birthDate ?? this.birthDate,
      governorate: governorate ?? this.governorate,
      center: center ?? this.center,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      orderCount: orderCount ?? this.orderCount,
      wishlistCount: wishlistCount ?? this.wishlistCount,
      totalSpent: totalSpent ?? this.totalSpent,
      rawUserMetaData: rawUserMetaData ?? this.rawUserMetaData,
      unreadNotificationsCount:
          unreadNotificationsCount ?? this.unreadNotificationsCount,
      totalNotificationsCount:
          totalNotificationsCount ?? this.totalNotificationsCount,
      lastNotificationCheck:
          lastNotificationCheck ?? this.lastNotificationCheck,
      isActive: isActive ?? this.isActive,
    );
  }

  // التحقق مما إذا كان المستخدم هو مدير
  bool get isAdmin => profileType == 'admin';

  // التحقق مما إذا كان المستخدم هو عميل
  bool get isCustomer => profileType == 'customer';

  // التحقق من وجود إشعارات غير مقروءة
  bool get hasUnreadNotifications => (unreadNotificationsCount ?? 0) > 0;

  // الحصول على عدد الإشعارات غير المقروءة
  int get unreadCount => unreadNotificationsCount ?? 0;

  // الحصول على إجمالي عدد الإشعارات
  int get totalNotifications => totalNotificationsCount ?? 0;

  /// التحقق من صحة بيانات المستخدم
  ValidationResult validateUser() {
    final errors = <String>[];
    final warnings = <String>[];

    // التحقق من الحقول الأساسية
    if (id.isEmpty) errors.add('معرف المستخدم مطلوب');
    if (name.isEmpty) errors.add('اسم المستخدم مطلوب');
    if (name.length < 2) warnings.add('اسم المستخدم قصير جداً');
    if (name.length > 100) errors.add('اسم المستخدم طويل جداً');

    // التحقق من البريد الإلكتروني
    if (email.isEmpty) errors.add('البريد الإلكتروني مطلوب');
    if (!_isValidEmail(email)) errors.add('البريد الإلكتروني غير صالح');

    // التحقق من رقم الهاتف
    if (phone != null && phone!.isNotEmpty && !_isValidPhone(phone!)) {
      warnings.add('رقم الهاتف قد يكون غير صالح');
    }

    // التحقق من نوع الملف الشخصي
    if (!['customer', 'admin'].contains(profileType)) {
      errors.add('نوع الملف الشخصي غير صالح');
    }

    // التحقق من تاريخ الميلاد
    if (birthDate != null) {
      final now = DateTime.now();
      final age = now.difference(birthDate!).inDays ~/ 365;
      if (age < 13) warnings.add('العمر صغير جداً');
      if (age > 120) warnings.add('العمر كبير جداً');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف
  bool _isValidPhone(String phone) {
    // التحقق من الأرقام المصرية
    return RegExp(r'^(\+20|0)?1[0-9]{9}$').hasMatch(phone.replaceAll(' ', ''));
  }
}
