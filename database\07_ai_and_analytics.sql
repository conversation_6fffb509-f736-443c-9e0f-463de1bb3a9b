-- ===================================================================
-- الذكاء الاصطناعي والتحليلات
==================================================================

-- 1. جدول تحليل المنتجات بالذكاء الاصطناعي
-- ===================================================================
CREATE TABLE IF NOT EXISTS product_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL, -- 'image_recognition', 'text_analysis', 'compatibility'
    input_data JSONB NOT NULL,
    analysis_result JSONB NOT NULL,
    confidence_score DECIMAL(5,4), -- 0.0000 to 1.0000
    processing_time_ms INTEGER,
    model_version VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_product_analysis_product_id ON product_analysis(product_id);
CREATE INDEX IF NOT EXISTS idx_product_analysis_analysis_type ON product_analysis(analysis_type);
CREATE INDEX IF NOT EXISTS idx_product_analysis_created_at ON product_analysis(created_at);
CREATE INDEX IF NOT EXISTS idx_product_analysis_confidence ON product_analysis(confidence_score) WHERE confidence_score > 0.8;

-- ===================================================================
-- 2. جدول سجل البحث الذكي
-- ===================================================================
CREATE TABLE IF NOT EXISTS smart_search_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    search_query TEXT NOT NULL,
    search_type VARCHAR(20) NOT NULL, -- 'text', 'voice', 'image'
    search_filters JSONB DEFAULT '{}'::jsonb,
    results_count INTEGER DEFAULT 0,
    selected_result_id UUID,
    search_duration_ms INTEGER,
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_smart_search_log_user_id ON smart_search_log(user_id);
CREATE INDEX IF NOT EXISTS idx_smart_search_log_search_type ON smart_search_log(search_type);
CREATE INDEX IF NOT EXISTS idx_smart_search_log_created_at ON smart_search_log(created_at);

CREATE INDEX IF NOT EXISTS idx_smart_search_performance ON smart_search_log(search_duration_ms, results_count);

-- ===================================================================
-- 3. جدول تحليلات سلوك المستخدمين
-- ===================================================================
CREATE TABLE IF NOT EXISTS user_behavior_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    event_type VARCHAR(50) NOT NULL, -- 'page_view', 'product_view', 'add_to_cart', 'purchase', etc.
    event_data JSONB DEFAULT '{}'::jsonb,
    page_url TEXT,
    referrer_url TEXT,
    user_agent TEXT,
    device_type VARCHAR(20), -- 'mobile', 'tablet', 'desktop'
    ip_address INET,
    duration_seconds INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_behavior_analytics_user_id ON user_behavior_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_analytics_session_id ON user_behavior_analytics(session_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_analytics_event_type ON user_behavior_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_user_behavior_analytics_created_at ON user_behavior_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_user_behavior_user_action ON user_behavior_analytics(user_id, event_type, created_at);

-- ===================================================================
-- 4. جدول سجلات البحث
-- ===================================================================
CREATE TABLE IF NOT EXISTS search_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    keyword TEXT NOT NULL,
    results_count INTEGER DEFAULT 0,
    clicked_product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    device_type VARCHAR(20),
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء

CREATE INDEX IF NOT EXISTS idx_search_logs_user_id ON search_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_search_logs_created_at ON search_logs(created_at);

-- ===================================================================
-- 5. جدول تحليلات البحث
-- ===================================================================
CREATE TABLE IF NOT EXISTS search_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_term TEXT NOT NULL UNIQUE,
    results_count INTEGER DEFAULT 0,
    click_through_rate DECIMAL(5,4), -- نسبة النقر
    conversion_rate DECIMAL(5,4), -- نسبة التحويل
    search_count INTEGER DEFAULT 1,
    last_searched_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء

CREATE INDEX IF NOT EXISTS idx_search_analytics_search_count ON search_analytics(search_count);
CREATE INDEX IF NOT EXISTS idx_search_analytics_last_searched_at ON search_analytics(last_searched_at);

-- ===================================================================
-- 5. جدول حركة المخزون
-- ===================================================================
CREATE TABLE IF NOT EXISTS inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'return')),
    quantity INTEGER NOT NULL,
    previous_quantity INTEGER NOT NULL,
    new_quantity INTEGER NOT NULL,
    reference_type VARCHAR(20), -- 'order', 'purchase', 'adjustment', 'return'
    reference_id UUID,
    notes TEXT,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_inventory_movements_product_id ON inventory_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_movement_type ON inventory_movements(movement_type);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_created_at ON inventory_movements(created_at);

-- ===================================================================
-- الدوال المساعدة للذكاء الاصطناعي
-- ===================================================================

-- دالة البحث المتقدم في المنتجات
CREATE OR REPLACE FUNCTION search_products(
    search_term TEXT,
    category_filter UUID DEFAULT NULL,
    company_filter UUID DEFAULT NULL,
    min_price DECIMAL DEFAULT NULL,
    max_price DECIMAL DEFAULT NULL,
    -- تم حذف min_rating حسب المتطلبات
    sort_by TEXT DEFAULT 'relevance',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    description TEXT,
    price DECIMAL,
    discount_price DECIMAL,
    category_id UUID,
    company_id UUID,
    image_urls JSONB,
    relevance_score REAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.description,
        p.price,
        p.discount_price,
        p.category_id,
        p.company_id,
        p.image_urls,
        CASE
            WHEN search_term IS NOT NULL AND search_term != '' THEN
                ts_rank(to_tsvector('arabic', COALESCE(p.name, '') || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, '')),
                       plainto_tsquery('arabic', search_term))
            ELSE 0
        END AS relevance_score
    FROM products p
    WHERE
        p.is_available = true
        AND (category_filter IS NULL OR p.category_id = category_filter)
        AND (company_filter IS NULL OR p.company_id = company_filter)
        AND (min_price IS NULL OR p.price >= min_price)
        AND (max_price IS NULL OR p.price <= max_price)
        AND (
            search_term IS NULL OR search_term = '' OR
            to_tsvector('arabic', COALESCE(p.name, '') || ' ' || COALESCE(p.description, '') || ' ' || COALESCE(p.brand, ''))
            @@ plainto_tsquery('arabic', search_term)
        )
    ORDER BY
        CASE
            WHEN sort_by = 'relevance' THEN relevance_score
            WHEN sort_by = 'price_asc' THEN p.price
            WHEN sort_by = 'price_desc' THEN -p.price
            WHEN sort_by = 'newest' THEN EXTRACT(EPOCH FROM p.created_at)
            ELSE relevance_score
        END DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$;

-- دالة الحصول على توصيات المنتجات
CREATE OR REPLACE FUNCTION get_product_recommendations(
    user_id_param UUID,
    product_id_param UUID DEFAULT NULL,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    price DECIMAL,
    discount_price DECIMAL,
    image_urls JSONB,
    recommendation_score REAL
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    WITH user_preferences AS (
        -- تحليل تفضيلات المستخدم بناءً على المشتريات والمشاهدات
        SELECT
            p.category_id,
            p.company_id,
            COUNT(*) as interaction_count
        FROM products p
        INNER JOIN (
            -- المشتريات
            SELECT oi.product_id FROM order_items oi
            INNER JOIN orders o ON oi.order_id = o.id
            WHERE o.user_id = user_id_param
            UNION ALL
            -- المشاهدات (فقط إذا كان الجدول موجود)
            SELECT pv.product_id FROM product_views pv
            WHERE pv.user_id = user_id_param
            AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_views')
            UNION ALL
            -- قائمة الرغبات (فقط إذا كان الجدول موجود)
            SELECT w.product_id FROM wishlists w
            WHERE w.user_id = user_id_param
            AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'wishlists')
        ) interactions ON p.id = interactions.product_id
        GROUP BY p.category_id, p.company_id
    ),
    similar_products AS (
        SELECT
            p.id,
            p.name,
            p.price,
            p.discount_price,
            p.image_urls,
            (
                COALESCE(up.interaction_count, 0) * 0.4 +  -- تفضيلات المستخدم
                (p.sales_count::FLOAT / GREATEST(p.view_count, 1)) * 0.5 + -- معدل التحويل
                CASE WHEN p.is_featured THEN 0.1 ELSE 0 END -- المنتجات المميزة
            ) as recommendation_score
        FROM products p
        LEFT JOIN user_preferences up ON p.category_id = up.category_id OR p.company_id = up.company_id
        WHERE
            p.is_available = true
            AND p.stock_quantity > 0
            AND (product_id_param IS NULL OR p.id != product_id_param)
            AND p.id NOT IN (
                SELECT product_id FROM order_items oi
                INNER JOIN orders o ON oi.order_id = o.id
                WHERE o.user_id = user_id_param
            )
    )
    SELECT
        sp.id,
        sp.name,
        sp.price,
        sp.discount_price,
        sp.image_urls,
        -- تم حذف average_rating حسب المتطلبات
        sp.recommendation_score
    FROM similar_products sp
    ORDER BY sp.recommendation_score DESC
    LIMIT limit_count;
END;
$$;

-- دالة إحصائيات المبيعات
CREATE OR REPLACE FUNCTION get_sales_statistics(
    start_date TIMESTAMPTZ DEFAULT NULL,
    end_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    total_orders BIGINT,
    total_revenue DECIMAL,
    average_order_value DECIMAL,
    top_selling_product_id UUID,
    top_selling_product_name VARCHAR,
    total_customers BIGINT,
    new_customers BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    start_date_filter TIMESTAMPTZ;
    end_date_filter TIMESTAMPTZ;
BEGIN
    -- تعيين التواريخ الافتراضية إذا لم يتم تمريرها
    start_date_filter := COALESCE(start_date, DATE_TRUNC('month', NOW()));
    end_date_filter := COALESCE(end_date, NOW());

    RETURN QUERY
    WITH order_stats AS (
        SELECT
            COUNT(*) as total_orders,
            SUM(total_amount) as total_revenue,
            AVG(total_amount) as average_order_value,
            COUNT(DISTINCT user_id) as total_customers
        FROM orders
        WHERE created_at BETWEEN start_date_filter AND end_date_filter
        AND status = 'delivered'
    ),
    top_product AS (
        SELECT
            p.id,
            p.name,
            SUM(oi.quantity) as total_sold
        FROM order_items oi
        INNER JOIN orders o ON oi.order_id = o.id
        INNER JOIN products p ON oi.product_id = p.id
        WHERE o.created_at BETWEEN start_date_filter AND end_date_filter
        AND o.status = 'delivered'
        GROUP BY p.id, p.name
        ORDER BY total_sold DESC
        LIMIT 1
    ),
    new_customers_count AS (
        SELECT COUNT(*) as new_customers
        FROM profiles
        WHERE created_at BETWEEN start_date_filter AND end_date_filter
    )
    SELECT
        os.total_orders,
        os.total_revenue,
        os.average_order_value,
        tp.id,
        tp.name,
        os.total_customers,
        ncc.new_customers
    FROM order_stats os
    CROSS JOIN top_product tp
    CROSS JOIN new_customers_count ncc;
END;
$$;

-- دالة تحديث المخزون
CREATE OR REPLACE FUNCTION update_inventory()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- تقليل المخزون عند إضافة عنصر طلب
        IF TG_TABLE_NAME = 'order_items' THEN
            UPDATE products SET stock_quantity = stock_quantity - NEW.quantity WHERE id = NEW.product_id;

            -- إضافة سجل حركة المخزون (فقط إذا كان الجدول موجود)
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'inventory_movements') THEN
                INSERT INTO inventory_movements (product_id, movement_type, quantity, previous_quantity, new_quantity, reference_type, reference_id)
                SELECT NEW.product_id, 'out', NEW.quantity, stock_quantity + NEW.quantity, stock_quantity, 'order', NEW.order_id
                FROM products WHERE id = NEW.product_id;
            END IF;
        END IF;

        RETURN NEW;
    END IF;

    RETURN NULL;
END;
$$;
-- ===================================================================

-- دالة إنشاء توصيات ذكية للمستخدم
CREATE OR REPLACE FUNCTION generate_user_recommendations(
    user_id_param UUID,
    recommendation_type_param VARCHAR DEFAULT 'personalized',
    limit_count INTEGER DEFAULT 10
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    recommendations_count INTEGER := 0;
BEGIN
    -- حذف التوصيات القديمة للمستخدم من نفس النوع
    DELETE FROM ai_recommendations
    WHERE user_id = user_id_param
    AND recommendation_type = recommendation_type_param;

    -- إنشاء توصيات جديدة بناءً على النوع
    IF recommendation_type_param = 'personalized' THEN
        -- التحقق من وجود جدول user_preferences
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_preferences') THEN
            INSERT INTO ai_recommendations (user_id, product_id, recommendation_type, recommendation_score, recommendation_reason)
            SELECT
                user_id_param,
                pr.id,
                'personalized',
                pr.recommendation_score,
                jsonb_build_object(
                    'reason', 'based_on_preferences',
                    'category_match', CASE WHEN up.preference_type = 'category' THEN true ELSE false END,
                    'company_match', CASE WHEN up.preference_type = 'company' THEN true ELSE false END
                )
            FROM get_product_recommendations(user_id_param, NULL, limit_count) pr
            LEFT JOIN user_preferences up ON up.user_id = user_id_param AND up.is_active = true
            LIMIT limit_count;
        ELSE
            -- إنشاء توصيات بسيطة بدون user_preferences
            INSERT INTO ai_recommendations (user_id, product_id, recommendation_type, recommendation_score, recommendation_reason)
            SELECT
                user_id_param,
                pr.id,
                'personalized',
                pr.recommendation_score,
                jsonb_build_object('reason', 'based_on_activity')
            FROM get_product_recommendations(user_id_param, NULL, limit_count) pr
            LIMIT limit_count;
        END IF;

        GET DIAGNOSTICS recommendations_count = ROW_COUNT;
    END IF;

    RETURN recommendations_count;
END;
$$;
-- ===================================================================

-- دالة تحديث تفضيلات المستخدم تلقائياً
CREATE OR REPLACE FUNCTION update_user_preferences_from_behavior(
    user_id_param UUID
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    preferences_updated INTEGER := 0;
BEGIN
    -- التحقق من وجود جدول user_preferences
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_preferences') THEN
        RETURN 0;
    END IF;
    
    -- تحديث تفضيلات الفئات بناءً على المشتريات
    INSERT INTO user_preferences (user_id, preference_type, preference_value, preference_weight)
    SELECT
        user_id_param,
        'category',
        to_jsonb(p.category_id),
        LEAST(COUNT(*) * 0.1, 1.0)
    FROM order_items oi
    INNER JOIN orders o ON oi.order_id = o.id
    INNER JOIN products p ON oi.product_id = p.id
    WHERE o.user_id = user_id_param
    GROUP BY p.category_id
    HAVING COUNT(*) >= 2
    ON CONFLICT (user_id, preference_type, preference_value)
    DO UPDATE SET
        preference_weight = LEAST(user_preferences.preference_weight + 0.1, 1.0),
        updated_at = NOW();

    GET DIAGNOSTICS preferences_updated = ROW_COUNT;

    -- تحديث تفضيلات الشركات بناءً على المشتريات
    INSERT INTO user_preferences (user_id, preference_type, preference_value, preference_weight)
    SELECT
        user_id_param,
        'company',
        to_jsonb(p.company_id),
        LEAST(COUNT(*) * 0.1, 1.0)
    FROM order_items oi
    INNER JOIN orders o ON oi.order_id = o.id
    INNER JOIN products p ON oi.product_id = p.id
    WHERE o.user_id = user_id_param
    GROUP BY p.company_id
    HAVING COUNT(*) >= 2
    ON CONFLICT (user_id, preference_type, preference_value)
    DO UPDATE SET
        preference_weight = LEAST(user_preferences.preference_weight + 0.1, 1.0),
        updated_at = NOW();

    RETURN preferences_updated;
END;
$$;
-- ===================================================================

-- دالة الحصول على المنتجات الجديدة
CREATE OR REPLACE FUNCTION get_new_products(
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    description TEXT,
    price DECIMAL,
    discount_price DECIMAL,
    category_id UUID,
    company_id UUID,
    image_urls JSONB,
    created_at TIMESTAMPTZ,
    days_since_added INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        p.description,
        p.price,
        p.discount_price,
        p.category_id,
        p.company_id,
        p.image_urls,
        p.created_at,
        EXTRACT(DAY FROM (NOW() - p.created_at))::INTEGER as days_since_added
    FROM products p
    WHERE
        p.is_available = true
        AND p.is_new = true
        AND p.new_until > NOW()
    ORDER BY p.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$;

-- ===================================================================
-- 6. جدول التوصيات الذكية
-- ===================================================================
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    recommendation_type VARCHAR(50) NOT NULL, -- 'similar_products', 'frequently_bought', 'trending', 'personalized'
    recommendation_score DECIMAL(5,4) NOT NULL CHECK (recommendation_score >= 0 AND recommendation_score <= 1),
    recommendation_reason JSONB DEFAULT '{}'::jsonb, -- سبب التوصية
    is_clicked BOOLEAN DEFAULT false,
    is_purchased BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '7 days'
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_user_id ON ai_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_product_id ON ai_recommendations(product_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_score ON ai_recommendations(recommendation_score);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_expires_at ON ai_recommendations(expires_at);

-- ===================================================================
-- 7. جدول تفضيلات المستخدمين
-- ===================================================================
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    preference_type VARCHAR(50) NOT NULL, -- 'category', 'company', 'price_range', 'brand'
    preference_value JSONB NOT NULL, -- القيمة المفضلة
    preference_weight DECIMAL(3,2) DEFAULT 1.0 CHECK (preference_weight >= 0 AND preference_weight <= 1),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, preference_type, preference_value)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_type ON user_preferences(preference_type);
CREATE INDEX IF NOT EXISTS idx_user_preferences_active ON user_preferences(is_active);

-- ===================================================================

--  تنظيف التوصيات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_recommendations()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    DELETE FROM ai_recommendations WHERE expires_at < NOW();
    RETURN NULL;
END;
$$;

-- ===================================================================
-- الدوال المحسنة للتوصيات الذكية
-- ===================================================================

-- دالة التوصيات الذكية المتقدمة
CREATE OR REPLACE FUNCTION generate_smart_recommendations(
    p_user_id UUID DEFAULT NULL,
    p_product_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    product_id UUID,
    product_name VARCHAR(200),
    product_price DECIMAL,
    discount_price DECIMAL,
    image_url TEXT,
    recommendation_score DECIMAL,
    recommendation_reason TEXT,
    category_name VARCHAR(100),
    company_name VARCHAR(100)
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    WITH recommendation_scores AS (
        SELECT
            p.id,
            p.name,
            p.price,
            p.discount_price,
            COALESCE((p.image_urls->0)::TEXT, '') as image_url,
            c.name as category_name,
            co.name as company_name,
            -- حساب نقاط التوصية
            (
                (COALESCE(p.view_count, 0) * 0.1 + COALESCE(p.sales_count, 0) * 0.2) * 0.3 +
                -- تم حذف معامل التقييم من حساب النقاط
                (CASE WHEN p.stock_quantity > 0 THEN 50 ELSE 0 END) * 0.15 +
                (CASE WHEN p.is_on_sale THEN 30 ELSE 0 END) * 0.1 +
                (CASE WHEN p.is_featured THEN 40 ELSE 0 END) * 0.1 +
                (CASE WHEN p.is_best_selling THEN 35 ELSE 0 END) * 0.1
            ) as score,

            CASE
                WHEN p.is_featured AND p.is_on_sale THEN 'منتج مميز بعرض خاص'
                WHEN p.is_best_selling THEN 'من أكثر المنتجات مبيعاً'
                -- تم حذف تصنيف المنتج حسب التقييم
                WHEN p.is_on_sale THEN 'عرض خاص - خصم مميز'
                WHEN p.is_featured THEN 'منتج مميز'
                ELSE 'منتج موصى به'
            END as reason

        FROM products p
        JOIN categories c ON p.category_id = c.id
        JOIN companies co ON p.company_id = co.id
        WHERE p.is_available = true
        AND p.stock_quantity > 0
        AND (p_product_id IS NULL OR p.id != p_product_id)
    )
    SELECT
        rs.id,
        rs.name,
        rs.price,
        rs.discount_price,
        rs.image_url,
        ROUND(rs.score, 2),
        rs.reason,
        rs.category_name,
        rs.company_name
    FROM recommendation_scores rs
    ORDER BY rs.score DESC, rs.price ASC
    LIMIT p_limit;
END;
$$;
-- ===================================================================

-- دالة التوصيات المتكاملة
CREATE OR REPLACE FUNCTION get_complementary_products(
    p_product_id UUID,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    product_id UUID,
    product_name TEXT,
    product_price DECIMAL,
    discount_price DECIMAL,
    image_url TEXT,
    compatibility_score DECIMAL,
    complement_reason TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_product_category UUID;
    v_product_company UUID;
    v_product_tags TEXT[];
BEGIN
    -- جلب بيانات المنتج الأساسي
    SELECT category_id, company_id, tags
    INTO v_product_category, v_product_company, v_product_tags
    FROM products
    WHERE id = p_product_id;

    RETURN QUERY
    WITH complementary_scores AS (
        SELECT
            p.id,
            p.name,
            p.price,
            p.discount_price,
            COALESCE((p.image_urls->0)::TEXT, '') as image_url,
            (
                -- نفس الشركة (40%)
                (CASE WHEN p.company_id = v_product_company THEN 40 ELSE 0 END) +

                -- فئة متكاملة (30%)
                (CASE
                    WHEN v_product_category IN (
                        SELECT id FROM categories WHERE name = 'الإطارات والعجلات'
                    ) AND p.category_id IN (
                        SELECT id FROM categories WHERE name IN ('المكابح ونظام الفرامل', 'نظام التعليق والامتصاص')
                    ) THEN 30
                    WHEN v_product_category IN (
                        SELECT id FROM categories WHERE name = 'قطع المحرك'
                    ) AND p.category_id IN (
                        SELECT id FROM categories WHERE name IN ('نظام العادم', 'الزيوت والسوائل')
                    ) THEN 30
                    WHEN v_product_category IN (
                        SELECT id FROM categories WHERE name = 'نظام الإضاءة'
                    ) AND p.category_id IN (
                        SELECT id FROM categories WHERE name IN ('الإضاءة والكهرباء', 'الإكسسوارات')
                    ) THEN 30
                    ELSE 0
                END) +

                -- تشابه العلامات (20%)
                (CASE
                    WHEN v_product_tags IS NOT NULL AND p.tags && v_product_tags THEN 20
                    ELSE 0
                END) +

                -- جودة المنتج (10%)
                -- تم حذف معامل التقييم من حساب التوصية
                10 -- إضافة قيمة ثابتة (10%) بدلاً من التقييم المحذوف
            ) as compatibility_score,

            CASE
                WHEN p.company_id = v_product_company THEN 'نفس العلامة التجارية'
                WHEN v_product_tags IS NOT NULL AND p.tags && v_product_tags THEN 'منتجات مترابطة'
                WHEN p.category_id != v_product_category THEN 'منتج متكامل'
                ELSE 'منتج موصى به'
            END as reason

        FROM products p
        WHERE p.is_available = true
        AND p.stock_quantity > 0
        AND p.id != p_product_id
    )
    SELECT
        cs.id,
        cs.name,
        cs.price,
        cs.discount_price,
        cs.image_url,
        ROUND(cs.compatibility_score, 2),
        cs.reason
    FROM complementary_scores cs
    WHERE cs.compatibility_score > 10
    ORDER BY cs.compatibility_score DESC, cs.price ASC
    LIMIT p_limit;
END;
$$;
-- ===================================================================

-- دالة تحديث نقاط التوصية
CREATE OR REPLACE FUNCTION update_recommendation_scores()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- حذف التوصيات القديمة
    DELETE FROM ai_recommendations
    WHERE created_at < NOW() - INTERVAL '7 days';

    -- تحديث نقاط التوصية
    UPDATE ai_recommendations
    SET
        recommendation_score = (
            SELECT
                LEAST(100, GREATEST(0,
                    (COALESCE(p.view_count, 0) * 0.1) +
                    (COALESCE(p.sales_count, 0) * 0.3) +
                    -- تم حذف معامل التقييم من حساب النقاط
                    (CASE WHEN p.is_featured THEN 20 ELSE 0 END) +
                    (CASE WHEN p.is_on_sale THEN 15 ELSE 0 END)
                )) / 100.0
            FROM products p
            WHERE p.id = ai_recommendations.product_id
        ),
        expires_at = NOW() + INTERVAL '7 days'
    WHERE created_at > NOW() - INTERVAL '7 days';

    RAISE NOTICE 'تم تحديث نقاط التوصية بنجاح';
END;
$$;






