class UserLocationModel {
  final String id;
  final String userId;
  final double latitude;
  final double longitude;
  final String? addressText;
  final String locationType;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserLocationModel({
    required this.id,
    required this.userId,
    required this.latitude,
    required this.longitude,
    this.addressText,
    this.locationType = 'delivery',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserLocationModel.fromJson(Map<String, dynamic> json) {
    return UserLocationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      addressText: json['address_text'] as String?,
      locationType: json['location_type'] as String? ?? 'delivery',
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'latitude': latitude,
      'longitude': longitude,
      'address_text': addressText,
      'location_type': locationType,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserLocationModel copyWith({
    String? id,
    String? userId,
    double? latitude,
    double? longitude,
    String? addressText,
    String? locationType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserLocationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      addressText: addressText ?? this.addressText,
      locationType: locationType ?? this.locationType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}