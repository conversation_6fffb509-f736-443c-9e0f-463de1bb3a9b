-- ===================================================================
-- سياسات الأمان والصلاحيات المثالية لمتجر إلكتروني متعدد المستخدمين
-- تشمل RLS (CREATE POLICY) + الصلاحيات (GRANT)
-- ===================================================================

-- =================== الصلاحيات العامة (GRANT) ===================
-- للمستخدم المجهول (anon): فقط قراءة الجداول العامة
GRANT SELECT ON products TO anon;
GRANT SELECT ON categories TO anon;
GRANT SELECT ON companies TO anon;
GRANT SELECT ON offers TO anon;
GRANT SELECT ON advertisements TO anon;
GRANT SELECT ON shipping_methods TO anon;

-- للمستخدم المصادق (authenticated): كل العمليات على الجداول الخاصة به
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON addresses TO authenticated;
GRANT ALL ON notification_settings TO authenticated;
GRANT ALL ON user_locations TO authenticated;
GRANT ALL ON carts TO authenticated;
GRANT ALL ON cart_items TO authenticated;
GRANT ALL ON wishlists TO authenticated;
GRANT ALL ON orders TO authenticated;
GRANT ALL ON order_items TO authenticated;
GRANT ALL ON notifications TO authenticated;
GRANT ALL ON user_notification_stats TO authenticated;
GRANT ALL ON support_interactions TO authenticated;
GRANT ALL ON coupon_usage TO authenticated;
GRANT ALL ON product_reviews TO authenticated;
GRANT ALL ON user_app_settings TO authenticated;
GRANT ALL ON user_devices TO authenticated;
GRANT ALL ON user_privacy_settings TO authenticated;
GRANT ALL ON user_security_settings TO authenticated;
GRANT ALL ON wishlist_collections TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;
GRANT ALL ON search_logs TO authenticated;
GRANT ALL ON notification_analytics TO authenticated;
GRANT ALL ON app_events TO authenticated;
GRANT ALL ON user_behavior_analytics TO authenticated;
GRANT ALL ON user_sessions TO authenticated;
GRANT ALL ON active_sessions TO authenticated;
GRANT ALL ON temp_data TO authenticated;

-- للنظام (service_role): كل الصلاحيات على كل الجداول
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;

-- ===================================================================
-- تفعيل RLS وسياسة موحدة لكل جدول حساس
-- ===================================================================

-- ========== الجداول العامة (سياسة مفتوحة) ========== 
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
CREATE POLICY "products_public" ON products FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
CREATE POLICY "categories_public" ON categories FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
CREATE POLICY "companies_public" ON companies FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "offers_public" ON offers FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE advertisements ENABLE ROW LEVEL SECURITY;
CREATE POLICY "advertisements_public" ON advertisements FOR ALL USING (true) WITH CHECK (true);
ALTER TABLE shipping_methods ENABLE ROW LEVEL SECURITY;
CREATE POLICY "shipping_methods_public" ON shipping_methods FOR ALL USING (true) WITH CHECK (true);

-- ========== الجداول الحساسة (سياسة لكل مستخدم ومدير) ========== 
-- profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "profiles_user_or_admin" ON profiles FOR ALL USING (id = auth.uid() OR is_admin()) WITH CHECK (id = auth.uid() OR is_admin());
-- addresses
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
CREATE POLICY "addresses_user_or_admin" ON addresses FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- orders
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY "orders_user_or_admin" ON orders FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- order_items
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "order_items_user_or_admin" ON order_items FOR ALL USING (EXISTS (SELECT 1 FROM orders WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()) OR is_admin()) WITH CHECK (EXISTS (SELECT 1 FROM orders WHERE orders.id = order_items.order_id AND orders.user_id = auth.uid()) OR is_admin());
-- carts
ALTER TABLE carts ENABLE ROW LEVEL SECURITY;
CREATE POLICY "carts_user_or_admin" ON carts FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- cart_items
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "cart_items_user_or_admin" ON cart_items FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- wishlists
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
CREATE POLICY "wishlists_user_or_admin" ON wishlists FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- notifications
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "notifications_user_or_admin" ON notifications FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_notification_stats
ALTER TABLE user_notification_stats ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_notification_stats_user_or_admin" ON user_notification_stats FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- support_interactions
ALTER TABLE support_interactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "support_interactions_user_or_admin" ON support_interactions FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- coupon_usage
ALTER TABLE coupon_usage ENABLE ROW LEVEL SECURITY;
CREATE POLICY "coupon_usage_user_or_admin" ON coupon_usage FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- product_reviews
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;
CREATE POLICY "product_reviews_user_or_admin" ON product_reviews FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_app_settings
ALTER TABLE user_app_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_app_settings_user_or_admin" ON user_app_settings FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_devices
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_devices_user_or_admin" ON user_devices FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_privacy_settings
ALTER TABLE user_privacy_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_privacy_settings_user_or_admin" ON user_privacy_settings FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_security_settings
ALTER TABLE user_security_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_security_settings_user_or_admin" ON user_security_settings FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- wishlist_collections
ALTER TABLE wishlist_collections ENABLE ROW LEVEL SECURITY;
CREATE POLICY "wishlist_collections_user_or_admin" ON wishlist_collections FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- ai_recommendations
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "ai_recommendations_user_or_admin" ON ai_recommendations FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- search_logs
ALTER TABLE search_logs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "search_logs_user_or_admin" ON search_logs FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- notification_analytics (مرتبط بالإشعارات التي لها user_id)
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;
CREATE POLICY "notification_analytics_user_or_admin" ON notification_analytics FOR ALL USING (
    EXISTS (SELECT 1 FROM notifications WHERE notifications.id = notification_analytics.notification_id AND notifications.user_id = auth.uid()) OR is_admin()
) WITH CHECK (
    EXISTS (SELECT 1 FROM notifications WHERE notifications.id = notification_analytics.notification_id AND notifications.user_id = auth.uid()) OR is_admin()
);
-- app_events
ALTER TABLE app_events ENABLE ROW LEVEL SECURITY;
CREATE POLICY "app_events_user_or_admin" ON app_events FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_behavior_analytics
ALTER TABLE user_behavior_analytics ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_behavior_analytics_user_or_admin" ON user_behavior_analytics FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- user_sessions
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "user_sessions_user_or_admin" ON user_sessions FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- active_sessions
ALTER TABLE active_sessions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "active_sessions_user_or_admin" ON active_sessions FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());
-- temp_data (جدول البيانات المؤقتة - لا يحتوي على user_id)
ALTER TABLE temp_data ENABLE ROW LEVEL SECURITY;
CREATE POLICY "temp_data_public" ON temp_data FOR ALL USING (true) WITH CHECK (true);

-- ===================================================================
-- سياسات الجداول الناقصة/الثانوية
-- ===================================================================

-- product_views: سياسة مفتوحة (أي مستخدم)
ALTER TABLE product_views ENABLE ROW LEVEL SECURITY;
CREATE POLICY "product_views_public" ON product_views FOR ALL USING (true) WITH CHECK (true);

-- product_analysis: سياسة للمدير فقط
ALTER TABLE product_analysis ENABLE ROW LEVEL SECURITY;
CREATE POLICY "product_analysis_admin" ON product_analysis FOR ALL USING (is_admin()) WITH CHECK (is_admin());

-- product_comparisons: سياسة للمستخدم أو المدير
ALTER TABLE product_comparisons ENABLE ROW LEVEL SECURITY;
CREATE POLICY "product_comparisons_user_or_admin" ON product_comparisons FOR ALL USING (user_id = auth.uid() OR is_admin()) WITH CHECK (user_id = auth.uid() OR is_admin());

-- search_analytics: سياسة للمدير فقط
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;
CREATE POLICY "search_analytics_admin" ON search_analytics FOR ALL USING (is_admin()) WITH CHECK (is_admin());

-- smart_search_log: سياسة مفتوحة (أي مستخدم)
ALTER TABLE smart_search_log ENABLE ROW LEVEL SECURITY;
CREATE POLICY "smart_search_log_public" ON smart_search_log FOR ALL USING (true) WITH CHECK (true);

-- notification_types: سياسة مفتوحة (أي مستخدم)
ALTER TABLE notification_types ENABLE ROW LEVEL SECURITY;
CREATE POLICY "notification_types_public" ON notification_types FOR ALL USING (true) WITH CHECK (true);

-- app_settings: سياسة مفتوحة (أي مستخدم)
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "app_settings_public" ON app_settings FOR ALL USING (true) WITH CHECK (true);

-- new_product_settings: سياسة للمدير فقط
ALTER TABLE new_product_settings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "new_product_settings_admin" ON new_product_settings FOR ALL USING (is_admin()) WITH CHECK (is_admin());

-- backup_logs: سياسة للمدير فقط
ALTER TABLE backup_logs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "backup_logs_admin" ON backup_logs FOR ALL USING (is_admin()) WITH CHECK (is_admin());

-- ad_campaigns: سياسة للمدير فقط
ALTER TABLE ad_campaigns ENABLE ROW LEVEL SECURITY;
CREATE POLICY "ad_campaigns_admin" ON ad_campaigns FOR ALL USING (is_admin()) WITH CHECK (is_admin());

-- coupons: سياسة مفتوحة (أي مستخدم)
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;
CREATE POLICY "coupons_public" ON coupons FOR ALL USING (true) WITH CHECK (true);

-- wishlist_items: سياسة للمستخدم مالك المجموعة أو المدير
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "wishlist_items_user_or_admin" ON wishlist_items FOR ALL USING (
  EXISTS (SELECT 1 FROM wishlist_collections WHERE id = collection_id AND user_id = auth.uid()) OR is_admin()
) WITH CHECK (
  EXISTS (SELECT 1 FROM wishlist_collections WHERE id = collection_id AND user_id = auth.uid()) OR is_admin()
); 