import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/widgets/product_card.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';

/// مكون عرض المنتجات الموصى بها في شكل قائمة أفقية أو شبكة
/// يستخدم لعرض التوصيات المخصصة للمستخدم أو المنتجات المشابهة
/// مدمج مع EnhancedRecommendationCarousel لتقليل التكرار
class RecommendationCarousel extends StatefulWidget {
  /// قائمة المنتجات الموصى بها
  final List<ProductModel> products;

  /// عنوان القسم
  final String title;

  /// وصف القسم (اختياري)
  final String? subtitle;

  /// دالة يتم استدعاؤها عند النقر على زر "عرض الكل"
  final VoidCallback? onViewAllPressed;

  /// ارتفاع البطاقة
  final double cardHeight;

  /// عرض البطاقة
  final double cardWidth;

  /// هل يتم عرض زر "عرض الكل"؟
  final bool showViewAll;

  /// هل يتم عرض شريط التمرير؟
  final bool showScrollbar;

  /// هل يتم عرض مؤشر التحميل عند عدم وجود منتجات؟
  final bool showLoadingOnEmpty;

  /// دالة يتم استدعاؤها عند النقر على منتج
  final Function(ProductModel)? onProductTap;

  /// أيقونة القسم (اختياري)
  final IconData? sectionIcon;

  /// لون الخلفية (اختياري)
  final Color? backgroundColor;

  /// نمط العرض: 'carousel' أو 'grid'
  final String viewMode;

  /// حالة التحميل
  final bool isLoading;

  const RecommendationCarousel({
    super.key,
    required this.products,
    required this.title,
    this.subtitle,
    this.onViewAllPressed,
    this.cardHeight = 220,
    this.cardWidth = 160,
    this.showViewAll = true,
    this.showScrollbar = true,
    this.showLoadingOnEmpty = false,
    this.onProductTap,
    this.sectionIcon,
    this.backgroundColor,
    this.viewMode = 'carousel',
    this.isLoading = false,
  });

  @override
  State<RecommendationCarousel> createState() => _RecommendationCarouselState();
}

class _RecommendationCarouselState extends State<RecommendationCarousel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scrollController = ScrollController();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع زر "عرض الكل"
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (widget.subtitle != null)
                      Text(
                        widget.subtitle!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
              if (widget.showViewAll && widget.onViewAllPressed != null)
                TextButton(
                  onPressed: widget.onViewAllPressed,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('عرض الكل'),
                      Icon(Icons.arrow_forward_ios, size: 14),
                    ],
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // قائمة المنتجات
        SizedBox(height: widget.cardHeight, child: _buildProductsList()),
      ],
    );
  }

  /// بناء قائمة المنتجات
  Widget _buildProductsList() {
    if (widget.products.isEmpty) {
      return widget.showLoadingOnEmpty
          ? Center(child: CircularProgressIndicator())
          : Center(
            child: Text(
              'لا توجد منتجات متاحة',
              style: TextStyle(color: Colors.grey),
            ),
          );
    }

    final listView = ListView.builder(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      itemCount: widget.products.length,
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, index) {
        // إضافة تأثير انتقالي للعناصر
        return AnimatedOpacity(
          duration: Duration(milliseconds: 500),
          opacity: 1.0,
          curve: Curves.easeInOut,
          child: Padding(
            padding: EdgeInsets.only(right: 12),
            child: SizedBox(
              width: widget.cardWidth,
              child: ProductCard(
                product: widget.products[index],
                onTap: () {
                  if (widget.onProductTap != null) {
                    widget.onProductTap!(widget.products[index]);
                  }
                },
                // إظهار شارة "جديد" للمنتجات الجديدة
                showNewBadge: true,
                // إظهار شارة الخصم للمنتجات التي عليها خصم
                showDiscountBadge: true,
              ),
            ),
          ),
        );
      },
    );

    return widget.showScrollbar
        ? Scrollbar(
          controller: _scrollController,
          thickness: 3,
          radius: Radius.circular(10),
          child: listView,
        )
        : listView;
  }
}
