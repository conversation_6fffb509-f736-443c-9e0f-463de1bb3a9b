import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:motorcycle_parts_shop/core/backup/backup_manager.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class BackupManagementScreen extends StatefulWidget {
  const BackupManagementScreen({super.key});

  @override
  State<BackupManagementScreen> createState() => _BackupManagementScreenState();
}

class _BackupManagementScreenState extends State<BackupManagementScreen> {
  final _backupService = BackupManager();
  List<BackupInfo> _backups = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _backupService.initialize(Supabase.instance.client);
      final backups = await _backupService.getBackupsList();
      setState(() {
        _backups = backups;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _createBackup() async {
    try {
      final formKey = GlobalKey<FormState>();
      String? backupName;

      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('إنشاء نسخة احتياطية'),
              content: Form(
                key: formKey,
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'اسم النسخة الاحتياطية (اختياري)',
                  ),
                  onSaved: (value) => backupName = value,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    formKey.currentState!.save();
                    Navigator.pop(context, true);
                  },
                  child: const Text('إنشاء'),
                ),
              ],
            ),
      );

      if (confirmed == true) {
        await _backupService.createFullBackup(description: backupName);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء النسخة الاحتياطية بنجاح')),
          );
          _loadBackups();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(e.toString())));
      }
    }
  }

  Future<void> _restoreBackup(String id) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('استعادة النسخة الاحتياطية'),
              content: const Text(
                'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('استعادة'),
                ),
              ],
            ),
      );

      if (confirmed == true) {
        await _backupService.restoreBackup(id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم استعادة النسخة الاحتياطية بنجاح')),
          );
          _loadBackups();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(e.toString())));
      }
    }
  }

  Future<void> _deleteBackup(String id) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('حذف النسخة الاحتياطية'),
              content: const Text('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: const Text('حذف'),
                ),
              ],
            ),
      );

      if (confirmed == true) {
        await _backupService.deleteBackup(id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف النسخة الاحتياطية بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadBackups();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              onPressed: () => _deleteBackup(id),
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }

  Future<void> _scheduleBackup() async {
    try {
      final formKey = GlobalKey<FormState>();
      String cronExpression = '0 0 * * *'; // يومياً في منتصف الليل
      String? backupName;

      final confirmed = await showDialog<bool>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('جدولة النسخ الاحتياطي'),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تعبير Cron',
                        hintText: 'مثال: 0 0 * * * (يومياً في منتصف الليل)',
                      ),
                      initialValue: cronExpression,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال تعبير Cron';
                        }
                        return null;
                      },
                      onSaved: (value) => cronExpression = value!,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'اسم النسخة الاحتياطية (اختياري)',
                      ),
                      onSaved: (value) => backupName = value,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      formKey.currentState!.save();
                      Navigator.pop(context, true);
                    }
                  },
                  child: const Text('جدولة'),
                ),
              ],
            ),
      );

      if (confirmed == true) {
        await _backupService.scheduleBackup(
          cronExpression: cronExpression,
          backupName: backupName,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم جدولة النسخ الاحتياطي بنجاح')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(e.toString())));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.schedule),
            onPressed: _scheduleBackup,
          ),
          IconButton(icon: const Icon(Icons.backup), onPressed: _createBackup),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(child: Text(_error!))
              : _backups.isEmpty
              ? const Center(child: Text('لا توجد نسخ احتياطية'))
              : ListView.builder(
                itemCount: _backups.length,
                itemBuilder: (context, index) {
                  final backup = _backups[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: ListTile(
                      title: Text(backup.description),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd HH:mm').format(backup.timestamp)}',
                          ),
                          Text('الحجم: ${backup.size} بايت'),
                          Text('المعرف: ${backup.id}'),
                          if (backup.localPath != null)
                            Text('المسار المحلي: ${backup.localPath}'),
                          if (backup.cloudUrl != null)
                            Text('المسار السحابي: ${backup.cloudUrl}'),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.restore),
                            onPressed: () => _restoreBackup(backup.id),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _deleteBackup(backup.id),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
    );
  }
}
