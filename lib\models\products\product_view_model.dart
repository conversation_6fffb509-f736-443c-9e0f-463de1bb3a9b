import 'package:flutter/material.dart';

class ProductViewModel {
  final String id;
  final String productId;
  final String? userId;
  final String? sessionId;
  final int? viewDurationSeconds;
  final String? referrerSource;
  final String? deviceType;
  final String? ipAddress;
  final DateTime createdAt;

  ProductViewModel({
    required this.id,
    required this.productId,
    this.userId,
    this.sessionId,
    this.viewDurationSeconds,
    this.referrerSource,
    this.deviceType,
    this.ipAddress,
    required this.createdAt,
  });

  factory ProductViewModel.fromJson(Map<String, dynamic> json) {
    return ProductViewModel(
      id: json['id'] as String,
      productId: json['product_id'] as String,
      userId: json['user_id'] as String?,
      sessionId: json['session_id'] as String?,
      viewDurationSeconds: json['view_duration_seconds'] as int?,
      referrerSource: json['referrer_source'] as String?,
      deviceType: json['device_type'] as String?,
      ipAddress: json['ip_address'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'user_id': userId,
      'session_id': sessionId,
      'view_duration_seconds': viewDurationSeconds,
      'referrer_source': referrerSource,
      'device_type': deviceType,
      'ip_address': ipAddress,
      'created_at': createdAt.toIso8601String(),
    };
  }

  ProductViewModel copyWith({
    String? id,
    String? productId,
    String? userId,
    String? sessionId,
    int? viewDurationSeconds,
    String? referrerSource,
    String? deviceType,
    String? ipAddress,
    DateTime? createdAt,
  }) {
    return ProductViewModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      viewDurationSeconds: viewDurationSeconds ?? this.viewDurationSeconds,
      referrerSource: referrerSource ?? this.referrerSource,
      deviceType: deviceType ?? this.deviceType,
      ipAddress: ipAddress ?? this.ipAddress,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}