import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/user_settings_service.dart';
import 'package:motorcycle_parts_shop/core/utils/ui_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/custom_app_bar.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/models/user/user_privacy_settings_model.dart';
import 'package:provider/provider.dart';

/// شاشة إعدادات الخصوصية
class PrivacySettingsScreen extends StatefulWidget {
  const PrivacySettingsScreen({super.key});

  @override
  State<PrivacySettingsScreen> createState() => _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends State<PrivacySettingsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final userSettingsService = Provider.of<UserSettingsService>(context);
    final privacySettings = userSettingsService.privacySettings;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'إعدادات الخصوصية',
        showBackButton: true,
      ),
      body:
          _isLoading
              ? const LoadingIndicator()
              : privacySettings == null
              ? const Center(child: Text('لم يتم العثور على إعدادات الخصوصية'))
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 16),

                    _buildSectionTitle('مشاركة البيانات'),
                    _buildSwitchTile(
                      title: 'مشاركة بيانات الملف الشخصي',
                      subtitle:
                          'السماح للمستخدمين الآخرين برؤية معلومات ملفك الشخصي',
                      value: privacySettings.shareProfileData,
                      onChanged:
                          (value) => _updateShareProfileData(
                            userSettingsService,
                            value,
                          ),
                    ),
                    _buildSwitchTile(
                      title: 'مشاركة سجل الطلبات',
                      subtitle: 'السماح للمتجر بعرض طلباتك السابقة كمراجع',
                      value: privacySettings.shareOrderHistory,
                      onChanged:
                          (value) => _updateShareOrderHistory(
                            userSettingsService,
                            value,
                          ),
                    ),

                    const SizedBox(height: 16),

                    _buildSectionTitle('الإعلانات والتحليلات'),
                    _buildSwitchTile(
                      title: 'السماح بالإعلانات المستهدفة',
                      subtitle: 'السماح بعرض إعلانات مخصصة بناءً على اهتماماتك',
                      value: privacySettings.allowTargetedAds,
                      onChanged:
                          (value) =>
                              _updateTargetedAds(userSettingsService, value),
                    ),
                    _buildSwitchTile(
                      title: 'السماح بتحليل البيانات',
                      subtitle: 'السماح بجمع بيانات الاستخدام لتحسين التطبيق',
                      value: privacySettings.allowDataAnalytics,
                      onChanged:
                          (value) =>
                              _updateDataAnalytics(userSettingsService, value),
                    ),
                    const SizedBox(height: 16),

                    _buildSectionTitle('الحالة والنشاط'),
                    _buildSwitchTile(
                      title: 'إظهار حالة الاتصال',
                      subtitle:
                          'السماح للآخرين بمعرفة ما إذا كنت متصلاً حالياً',
                      value: privacySettings.showOnlineStatus,
                      onChanged:
                          (value) =>
                              _updateOnlineStatus(userSettingsService, value),
                    ),
                    _buildSwitchTile(
                      title: 'إظهار آخر ظهور',
                      subtitle: 'السماح للآخرين بمعرفة آخر مرة كنت نشطاً فيها',
                      value: privacySettings.showLastSeen,
                      onChanged:
                          (value) =>
                              _updateLastSeen(userSettingsService, value),
                    ),
                    _buildSwitchTile(
                      title: 'إظهار حالة النشاط',
                      subtitle:
                          'السماح للآخرين بمعرفة ما إذا كنت تتصفح أو تكتب',
                      value: privacySettings.showActivityStatus,
                      onChanged:
                          (value) =>
                              _updateActivityStatus(userSettingsService, value),
                    ),
                    const SizedBox(height: 32),

                    _buildResetButton(context, userSettingsService),
                  ],
                ),
              ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [
            Text(
              'معلومات الخصوصية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'تحكم في كيفية مشاركة بياناتك واستخدامها في التطبيق. '
              'يمكنك تغيير هذه الإعدادات في أي وقت.',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle, style: const TextStyle(fontSize: 12)),
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildResetButton(BuildContext context, UserSettingsService service) {
    return Center(
      child: ElevatedButton.icon(
        icon: const Icon(Icons.refresh),
        label: const Text('إعادة تعيين إعدادات الخصوصية'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        onPressed: () => _resetSettings(context, service),
      ),
    );
  }

  Future<void> _updateShareProfileData(
    UserSettingsService service,
    bool enable,
  ) async {
    setState(() => _isLoading = true);
    try {
      await service.toggleShareProfileData(enable);
      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable
              ? 'تم تفعيل مشاركة بيانات الملف الشخصي'
              : 'تم إيقاف مشاركة بيانات الملف الشخصي',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات مشاركة البيانات',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateShareOrderHistory(
    UserSettingsService service,
    bool enable,
  ) async {
    setState(() => _isLoading = true);
    try {
      await service.toggleShareOrderHistory(enable);
      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable
              ? 'تم تفعيل مشاركة سجل الطلبات'
              : 'تم إيقاف مشاركة سجل الطلبات',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات مشاركة سجل الطلبات',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // تم حذف دالة تحديث مشاركة التقييمات حسب المتطلبات

  Future<void> _updateTargetedAds(
    UserSettingsService service,
    bool enable,
  ) async {
    setState(() => _isLoading = true);
    try {
      await service.toggleTargetedAds(enable);
      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable
              ? 'تم تفعيل الإعلانات المستهدفة'
              : 'تم إيقاف الإعلانات المستهدفة',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات الإعلانات',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateDataAnalytics(
    UserSettingsService service,
    bool enable,
  ) async {
    setState(() => _isLoading = true);
    try {
      final privacySettings = service.privacySettings;
      if (privacySettings == null) return;

      final updatedSettings = privacySettings.copyWith(
        allowDataAnalytics: enable,
        updatedAt: DateTime.now(),
      );

      await service.updatePrivacySettings(updatedSettings);

      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable ? 'تم تفعيل تحليل البيانات' : 'تم إيقاف تحليل البيانات',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات تحليل البيانات',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateOnlineStatus(
    UserSettingsService service,
    bool enable,
  ) async {
    setState(() => _isLoading = true);
    try {
      final privacySettings = service.privacySettings;
      if (privacySettings == null) return;

      final updatedSettings = privacySettings.copyWith(
        showOnlineStatus: enable,
        updatedAt: DateTime.now(),
      );

      await service.updatePrivacySettings(updatedSettings);

      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable
              ? 'تم تفعيل إظهار حالة الاتصال'
              : 'تم إيقاف إظهار حالة الاتصال',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات حالة الاتصال',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateLastSeen(UserSettingsService service, bool enable) async {
    setState(() => _isLoading = true);
    try {
      final privacySettings = service.privacySettings;
      if (privacySettings == null) return;

      final updatedSettings = privacySettings.copyWith(
        showLastSeen: enable,
        updatedAt: DateTime.now(),
      );

      await service.updatePrivacySettings(updatedSettings);

      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable ? 'تم تفعيل إظهار آخر ظهور' : 'تم إيقاف إظهار آخر ظهور',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات آخر ظهور',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateActivityStatus(
    UserSettingsService service,
    bool enable,
  ) async {
    setState(() => _isLoading = true);
    try {
      final privacySettings = service.privacySettings;
      if (privacySettings == null) return;

      final updatedSettings = privacySettings.copyWith(
        showActivityStatus: enable,
        updatedAt: DateTime.now(),
      );

      await service.updatePrivacySettings(updatedSettings);

      if (mounted) {
        UIHelper.showSnackBar(
          context,
          enable ? 'تم تفعيل إظهار حالة النشاط' : 'تم إيقاف إظهار حالة النشاط',
        );
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في تغيير إعدادات حالة النشاط',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _resetSettings(
    BuildContext context,
    UserSettingsService service,
  ) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعادة تعيين إعدادات الخصوصية'),
            content: const Text(
              'هل أنت متأكد من إعادة تعيين جميع إعدادات الخصوصية إلى الوضع الافتراضي؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
    );

    if (confirm != true) return;

    setState(() => _isLoading = true);
    try {
      final userId = service.privacySettings?.userId;
      if (userId == null) return;

      final defaultSettings = UserPrivacySettingsModel.createDefault(userId);
      await service.updatePrivacySettings(defaultSettings);

      if (mounted) {
        UIHelper.showSnackBar(context, 'تم إعادة تعيين إعدادات الخصوصية بنجاح');
      }
    } catch (e) {
      if (mounted) {
        UIHelper.showErrorDialog(
          context,
          'فشل في إعادة تعيين إعدادات الخصوصية',
          e.toString(),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
