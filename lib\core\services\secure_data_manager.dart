import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

/// مدير البيانات الآمنة مع تشفير متقدم
class SecureDataManager {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
    ),
  );

  static Encrypter? _encrypter;
  static IV? _iv;
  static bool _isInitialized = false;

  /// تهيئة مدير البيانات الآمنة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _initializeEncryption();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة مدير البيانات الآمنة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير البيانات الآمنة: $e');
      throw SecureStorageException('فشل في تهيئة التشفير: $e');
    }
  }

  /// تهيئة التشفير
  static Future<void> _initializeEncryption() async {
    // محاولة قراءة المفتاح الموجود
    String? keyString = await _secureStorage.read(key: '_encryption_key');
    String? ivString = await _secureStorage.read(key: '_encryption_iv');

    Key key;
    IV iv;

    if (keyString != null && ivString != null) {
      // استخدام المفتاح الموجود
      key = Key.fromBase64(keyString);
      iv = IV.fromBase64(ivString);
    } else {
      // إنشاء مفتاح جديد
      key = Key.fromSecureRandom(32);
      iv = IV.fromSecureRandom(16);

      // حفظ المفتاح بشكل آمن
      await _secureStorage.write(key: '_encryption_key', value: key.base64);
      await _secureStorage.write(key: '_encryption_iv', value: iv.base64);
    }

    _encrypter = Encrypter(AES(key));
    _iv = iv;
  }

  /// تشفير وحفظ البيانات
  static Future<void> storeSecureData(String key, String value) async {
    await _ensureInitialized();

    try {
      // تشفير البيانات
      final encrypted = _encrypter!.encrypt(value, iv: _iv!);
      
      // إضافة hash للتحقق من سلامة البيانات
      final hash = _generateHash(value);
      final dataWithHash = jsonEncode({
        'data': encrypted.base64,
        'hash': hash,
        'timestamp': DateTime.now().toIso8601String(),
      });

      // حفظ البيانات المشفرة
      await _secureStorage.write(key: key, value: dataWithHash);
      
      debugPrint('✅ تم حفظ البيانات الآمنة للمفتاح: $key');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات الآمنة: $e');
      throw SecureStorageException('فشل في حفظ البيانات الآمنة: $e');
    }
  }

  /// قراءة وفك تشفير البيانات
  static Future<String?> getSecureData(String key) async {
    await _ensureInitialized();

    try {
      final encryptedData = await _secureStorage.read(key: key);
      if (encryptedData == null) return null;

      // فك تشفير البيانات
      final dataMap = jsonDecode(encryptedData) as Map<String, dynamic>;
      final encryptedValue = Encrypted.fromBase64(dataMap['data']);
      final storedHash = dataMap['hash'] as String;
      
      final decryptedValue = _encrypter!.decrypt(encryptedValue, iv: _iv!);
      
      // التحقق من سلامة البيانات
      final calculatedHash = _generateHash(decryptedValue);
      if (calculatedHash != storedHash) {
        debugPrint('⚠️ تم اكتشاف تلاعب في البيانات للمفتاح: $key');
        await deleteSecureData(key); // حذف البيانات المتلاعب بها
        return null;
      }

      return decryptedValue;
    } catch (e) {
      debugPrint('❌ خطأ في قراءة البيانات الآمنة: $e');
      return null;
    }
  }

  /// حفظ كائن JSON بشكل آمن
  static Future<void> storeSecureJson(String key, Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    await storeSecureData(key, jsonString);
  }

  /// قراءة كائن JSON بشكل آمن
  static Future<Map<String, dynamic>?> getSecureJson(String key) async {
    final jsonString = await getSecureData(key);
    if (jsonString == null) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('❌ خطأ في تحليل JSON: $e');
      return null;
    }
  }

  /// حذف البيانات الآمنة
  static Future<void> deleteSecureData(String key) async {
    try {
      await _secureStorage.delete(key: key);
      debugPrint('✅ تم حذف البيانات الآمنة للمفتاح: $key');
    } catch (e) {
      debugPrint('❌ خطأ في حذف البيانات الآمنة: $e');
      throw SecureStorageException('فشل في حذف البيانات الآمنة: $e');
    }
  }

  /// مسح جميع البيانات الآمنة
  static Future<void> clearAllSecureData() async {
    try {
      await _secureStorage.deleteAll();
      _isInitialized = false;
      _encrypter = null;
      _iv = null;
      debugPrint('✅ تم مسح جميع البيانات الآمنة');
    } catch (e) {
      debugPrint('❌ خطأ في مسح البيانات الآمنة: $e');
      throw SecureStorageException('فشل في مسح البيانات الآمنة: $e');
    }
  }

  /// التحقق من وجود مفتاح
  static Future<bool> containsKey(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      return value != null;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من وجود المفتاح: $e');
      return false;
    }
  }

  /// الحصول على جميع المفاتيح
  static Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _secureStorage.readAll();
      return allData.keys.where((key) => !key.startsWith('_')).toSet();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على المفاتيح: $e');
      return <String>{};
    }
  }

  /// حفظ بيانات المصادقة
  static Future<void> storeAuthData({
    required String accessToken,
    required String refreshToken,
    String? userId,
    DateTime? expiresAt,
  }) async {
    final authData = {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      if (userId != null) 'user_id': userId,
      if (expiresAt != null) 'expires_at': expiresAt.toIso8601String(),
      'stored_at': DateTime.now().toIso8601String(),
    };

    await storeSecureJson('auth_data', authData);
  }

  /// قراءة بيانات المصادقة
  static Future<Map<String, dynamic>?> getAuthData() async {
    return await getSecureJson('auth_data');
  }

  /// حذف بيانات المصادقة
  static Future<void> clearAuthData() async {
    await deleteSecureData('auth_data');
  }

  /// حفظ إعدادات المستخدم الحساسة
  static Future<void> storeUserSettings(Map<String, dynamic> settings) async {
    await storeSecureJson('user_settings', settings);
  }

  /// قراءة إعدادات المستخدم الحساسة
  static Future<Map<String, dynamic>?> getUserSettings() async {
    return await getSecureJson('user_settings');
  }

  /// إنشاء hash للتحقق من سلامة البيانات
  static String _generateHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التأكد من التهيئة
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// تصدير البيانات للنسخ الاحتياطي (مشفرة)
  static Future<Map<String, String>> exportSecureData() async {
    try {
      final allData = await _secureStorage.readAll();
      // إزالة مفاتيح التشفير من التصدير
      allData.removeWhere((key, value) => key.startsWith('_'));
      return allData;
    } catch (e) {
      debugPrint('❌ خطأ في تصدير البيانات: $e');
      throw SecureStorageException('فشل في تصدير البيانات: $e');
    }
  }

  /// استيراد البيانات من النسخة الاحتياطية
  static Future<void> importSecureData(Map<String, String> data) async {
    try {
      for (final entry in data.entries) {
        if (!entry.key.startsWith('_')) {
          await _secureStorage.write(key: entry.key, value: entry.value);
        }
      }
      debugPrint('✅ تم استيراد ${data.length} عنصر من البيانات الآمنة');
    } catch (e) {
      debugPrint('❌ خطأ في استيراد البيانات: $e');
      throw SecureStorageException('فشل في استيراد البيانات: $e');
    }
  }

  /// فحص سلامة البيانات المخزنة
  static Future<Map<String, bool>> checkDataIntegrity() async {
    final results = <String, bool>{};
    final keys = await getAllKeys();

    for (final key in keys) {
      try {
        final data = await getSecureData(key);
        results[key] = data != null;
      } catch (e) {
        results[key] = false;
      }
    }

    return results;
  }
}

/// استثناء التخزين الآمن
class SecureStorageException implements Exception {
  final String message;
  SecureStorageException(this.message);

  @override
  String toString() => 'SecureStorageException: $message';
}
