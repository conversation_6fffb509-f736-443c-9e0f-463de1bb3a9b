{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\motorcycle_parts_shop\\android\\app\\.cxx\\RelWithDebInfo\\2z5u152l\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\motorcycle_parts_shop\\android\\app\\.cxx\\RelWithDebInfo\\2z5u152l\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}