import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// تم إزالة ai_services
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:provider/provider.dart';

/// شاشة تصفية المنتجات
/// تتيح هذه الشاشة للمستخدم تصفية المنتجات حسب معايير مختلفة مثل الشركة المصنعة والفئة والسعر
///
/// نصائح للتطوير:

/// 3. يمكن إضافة إمكانية الفرز حسب معايير مختلفة (السعر، الاسم، التاريخ)
class ProductFilterScreen extends StatefulWidget {
  final String? initialCategory;
  final String? initialCompany;

  const ProductFilterScreen({
    super.key,
    this.initialCategory,
    this.initialCompany,
  });

  @override
  State<ProductFilterScreen> createState() => _ProductFilterScreenState();
}

class _ProductFilterScreenState extends State<ProductFilterScreen> {
  // حالة التصفية
  String? _selectedCategory;
  String? _selectedCompany;

  // قوائم الخيارات
  List<String> _companies = [];
  List<String> _categories = [];

  // قيم الحد الأدنى والأقصى للسعر
  double _minPrice = 0;
  double _maxPrice = 10000;

  // حالة التحميل
  bool _isLoading = true;

  // وحدة تحكم التصفية
  late final ProductFilterController _filterController;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.initialCategory;
    _selectedCompany = widget.initialCompany;
    _filterController = ProductFilterController(
      initialCategory: _selectedCategory,
      initialCompany: _selectedCompany,
      minPrice: _minPrice,
      maxPrice: _maxPrice,
    );
    _loadFilterOptions();
  }

  /// تحميل خيارات التصفية من قاعدة البيانات
  Future<void> _loadFilterOptions() async {
    setState(() => _isLoading = true);

    try {
      final supabaseService = Provider.of<AuthSupabaseService>(
        context,
        listen: false,
      );
      final productService = ProductService(supabaseService.client);

      // جلب قائمة الشركات المصنعة
      final companiesList = await productService.getAvailableCompanies();
      _companies =
          companiesList.map((company) => company['name'] as String).toList();

      // جلب قائمة الفئات
      final categories = await supabaseService.getCategories();
      _categories = categories.map((cat) => cat.name).toList();

      // تحديد حدود السعر من المنتجات المتاحة
      final products = await supabaseService.getAllProducts();
      if (products.isNotEmpty) {
        // Filter out null prices and ensure the list is not empty before reducing
        final validPrices =
            products
                .map((p) => p.price)
                // ignore: unnecessary_null_comparison
                .where((price) => price != null)
                .cast<double>()
                .toList();
        if (validPrices.isNotEmpty) {
          _minPrice = validPrices.reduce((a, b) => a < b ? a : b);
          _maxPrice = validPrices.reduce((a, b) => a > b ? a : b);
        } else {
          // Default values if no valid prices found
          _minPrice = 0;
          _maxPrice = 10000;
        }
        setState(() {});
      } else {
        _minPrice = 0;
        _maxPrice = 10000;
        setState(() {});
      }

      // تحديث وحدة تحكم التصفية
      _filterController.updateOptions(
        companies: _companies,
        categories: _categories,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
      );
    } catch (e) {
      // معالجة أفضل للأخطاء
      String errorMessage =
          e.toString().contains('Network')
              ? 'يرجى التحقق من اتصال الإنترنت'
              : 'حدث خطأ: ${e.toString()}';
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(errorMessage)));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// تطبيق التصفية وإرجاع النتائج
  void _applyFilter() {
    // إضافة تأثير اهتزاز خفيف عند تطبيق التصفية
    HapticFeedback.mediumImpact();

    // إظهار رسالة تأكيد قبل العودة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تطبيق التصفية بنجاح'),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
        duration: const Duration(milliseconds: 800),
      ),
    );

    // انتظار لحظة قبل العودة للشاشة السابقة
    Future.delayed(const Duration(milliseconds: 500), () {
      Navigator.of(context).pop(_filterController.toMap());
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _filterController,
      child: Scaffold(
        appBar: AppBar(
          title: Text('تصفية المنتجات'),
          actions: [
            TextButton(
              onPressed: () => _filterController.resetFilters(),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.accentColor,
              ),
              child: Text('إعادة تعيين'),
            ),
          ],
        ),
        body:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Consumer<ProductFilterController>(
                    builder:
                        (context, controller, _) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildCompanyFilter(controller),
                            const Divider(height: 32, thickness: 1),
                            _buildCategoryFilter(controller),
                            const Divider(height: 32, thickness: 1),
                            _buildPriceFilter(controller),
                            const Divider(height: 32, thickness: 1),
                            Card(
                              elevation: 0,
                              color: AppTheme.secondaryLightColor.withOpacity(
                                0.2,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: CheckboxListTile(
                                title: Text('عرض المنتجات المتاحة فقط'),
                                value: controller.onlyAvailable,
                                onChanged:
                                    (value) =>
                                        controller.onlyAvailable =
                                            value ?? true,
                                activeColor: AppTheme.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: _applyFilter,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryColor,
                                  foregroundColor: Colors.white,
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Text(
                                  'تطبيق التصفية',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                  ),
                ),
      ),
    );
  }

  Widget _buildCompanyFilter(ProductFilterController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تصفية حسب الشركة المصنعة',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        controller.companies.isEmpty
            ? Text(
              'لا توجد شركات متاحة حاليًا',
              style: const TextStyle(
                fontStyle: FontStyle.italic,
                color: AppTheme.textSecondaryColor,
              ),
            )
            : SizedBox(
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: controller.companies.length + 1,
                itemBuilder: (context, index) {
                  if (index == 0) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text('الكل'),
                        selected: controller.selectedCompany == null,
                        onSelected: (_) => controller.selectedCompany = null,
                        selectedColor: AppTheme.primaryLightColor,
                        checkmarkColor: Colors.white,
                        backgroundColor: Colors.grey.shade200,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        labelStyle: TextStyle(
                          color:
                              controller.selectedCompany == null
                                  ? Colors.white
                                  : AppTheme.textPrimaryColor,
                          fontWeight:
                              controller.selectedCompany == null
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                        ),
                      ),
                    );
                  }
                  final company = controller.companies[index - 1];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(company),
                      selected: company == controller.selectedCompany,
                      onSelected: (_) => controller.selectedCompany = company,
                      selectedColor: AppTheme.primaryLightColor,
                      checkmarkColor: Colors.white,
                      backgroundColor: Colors.grey.shade200,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      labelStyle: TextStyle(
                        color:
                            company == controller.selectedCompany
                                ? Colors.white
                                : AppTheme.textPrimaryColor,
                        fontWeight:
                            company == controller.selectedCompany
                                ? FontWeight.bold
                                : FontWeight.normal,
                      ),
                    ),
                  );
                },
              ),
            ),
      ],
    );
  }

  Widget _buildCategoryFilter(ProductFilterController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تصفية حسب الفئة',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        controller.categories.isEmpty
            ? Text(
              'لا توجد فئات متاحة حاليًا',
              style: const TextStyle(
                fontStyle: FontStyle.italic,
                color: AppTheme.textSecondaryColor,
              ),
            )
            : Wrap(
              spacing: 8,
              runSpacing: 12,
              children: [
                FilterChip(
                  label: Text('الكل'),
                  selected: controller.selectedCategory == null,
                  onSelected: (_) => controller.selectedCategory = null,
                  selectedColor: AppTheme.primaryLightColor,
                  checkmarkColor: Colors.white,
                  backgroundColor: Colors.grey.shade200,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  labelStyle: TextStyle(
                    color:
                        controller.selectedCategory == null
                            ? Colors.white
                            : AppTheme.textPrimaryColor,
                    fontWeight:
                        controller.selectedCategory == null
                            ? FontWeight.bold
                            : FontWeight.normal,
                  ),
                ),
                ...controller.categories.map(
                  (category) => FilterChip(
                    label: Text(category),
                    selected: category == controller.selectedCategory,
                    onSelected: (_) => controller.selectedCategory = category,
                    selectedColor: AppTheme.primaryLightColor,
                    checkmarkColor: Colors.white,
                    backgroundColor: Colors.grey.shade200,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    labelStyle: TextStyle(
                      color:
                          category == controller.selectedCategory
                              ? Colors.white
                              : AppTheme.textPrimaryColor,
                      fontWeight:
                          category == controller.selectedCategory
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
      ],
    );
  }

  Widget _buildPriceFilter(ProductFilterController controller) {
    final currencySymbol = 'جنيه';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تصفية حسب السعر',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryLightColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                'نطاق السعر: ${controller.priceRange.start.toInt()} - ${controller.priceRange.end.toInt()} $currencySymbol',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${controller.priceRange.start.toInt()} $currencySymbol',
                    style: const TextStyle(color: AppTheme.textSecondaryColor),
                  ),
                  Text(
                    '${controller.priceRange.end.toInt()} $currencySymbol',
                    style: const TextStyle(color: AppTheme.textSecondaryColor),
                  ),
                ],
              ),
              RangeSlider(
                values: controller.priceRange,
                min: controller.minPrice,
                max: controller.maxPrice,
                divisions:
                    ((controller.maxPrice - controller.minPrice) / 100).ceil(),
                labels: RangeLabels(
                  '${controller.priceRange.start.toInt()} $currencySymbol',
                  '${controller.priceRange.end.toInt()} $currencySymbol',
                ),
                activeColor: AppTheme.primaryColor,
                inactiveColor: AppTheme.primaryLightColor.withOpacity(0.3),
                onChanged: (values) => controller.priceRange = values,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// وحدة تحكم تصفية المنتجات
class ProductFilterController extends ChangeNotifier {
  String? _selectedCategory;
  String? _selectedCompany;
  RangeValues _priceRange;
  bool _onlyAvailable = true;
  List<String> _companies = [];
  List<String> _categories = [];
  double _minPrice;
  double _maxPrice;

  ProductFilterController({
    String? initialCategory,
    String? initialCompany,
    required double minPrice,
    required double maxPrice,
  }) : _selectedCategory = initialCategory,
       _selectedCompany = initialCompany,
       _minPrice = minPrice,
       _maxPrice = maxPrice,
       _priceRange = RangeValues(minPrice, maxPrice);

  // الحصول على القيم
  String? get selectedCategory => _selectedCategory;
  String? get selectedCompany => _selectedCompany;
  RangeValues get priceRange => _priceRange;
  bool get onlyAvailable => _onlyAvailable;
  List<String> get companies => _companies;
  List<String> get categories => _categories;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;

  // تعيين القيم
  set selectedCategory(String? value) {
    _selectedCategory = value;
    notifyListeners();
  }

  set selectedCompany(String? value) {
    _selectedCompany = value;
    notifyListeners();
  }

  set priceRange(RangeValues value) {
    _priceRange = value;
    notifyListeners();
  }

  set onlyAvailable(bool value) {
    _onlyAvailable = value;
    notifyListeners();
  }

  // تحديث الخيارات
  void updateOptions({
    required List<String> companies,
    required List<String> categories,
    required double minPrice,
    required double maxPrice,
  }) {
    _companies = companies;
    _categories = categories;
    _minPrice = minPrice;
    _maxPrice = maxPrice;
    _priceRange = RangeValues(minPrice, maxPrice);
    notifyListeners();
  }

  // إعادة تعيين التصفية
  void resetFilters() {
    _selectedCategory = null;
    _selectedCompany = null;
    _priceRange = RangeValues(_minPrice, _maxPrice);
    _onlyAvailable = true;
    notifyListeners();
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'category': _selectedCategory,
      'company': _selectedCompany,
      'min_price': _priceRange.start,
      'max_price': _priceRange.end,
      'only_available': _onlyAvailable,
    };
  }
}

/// مدير تصفية المنتجات
class ProductFilterManager {
  static List<ProductModel> applyFilters(
    List<ProductModel> products,
    Map<String, dynamic> filterCriteria,
  ) {
    final filteredProducts = List<ProductModel>.from(products);

    final company = filterCriteria['company'] as String?;
    if (company != null && company.isNotEmpty) {
      filteredProducts.removeWhere((product) => product.companyId != company);
    }

    final category = filterCriteria['category'] as String?;
    if (category != null && category.isNotEmpty) {
      filteredProducts.removeWhere(
        (product) => product.categoryName != category,
      );
    }

    final minPrice = filterCriteria['min_price'] as double?;
    final maxPrice = filterCriteria['max_price'] as double?;
    if (minPrice != null && maxPrice != null) {
      filteredProducts.removeWhere((product) {
        final price = product.discountPrice ?? product.price;
        return price < minPrice || price > maxPrice;
      });
    }

    final onlyAvailable = filterCriteria['only_available'] as bool?;
    if (onlyAvailable == true) {
      filteredProducts.removeWhere((product) => !product.isAvailable);
    }

    return filteredProducts;
  }

  static List<String> getUniqueCompanies(List<ProductModel> products) {
    return products.map((product) => product.companyId).toSet().toList();
  }
}
