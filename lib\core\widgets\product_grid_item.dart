import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';

import '../../screens/product/product_details_screen.dart';

/// عنصر المنتج في الشبكة
class ProductGridItem extends StatelessWidget {
  final ProductModel product;
  final bool showNewBadge;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final bool isFavorite;

  const ProductGridItem({
    super.key,
    required this.product,
    this.showNewBadge = false,
    this.onTap,
    this.onFavoriteToggle,
    this.isFavorite = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: InkWell(
        onTap: onTap ?? () => _navigateToProductDetails(context),
        borderRadius: BorderRadius.circular(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProductImage(context),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProductName(),
                    const SizedBox(height: 4.0),
                    _buildProductPrice(),
                    const SizedBox(height: 4.0),
                    // تم حذف _buildProductRating حسب المتطلبات
                    if (showNewBadge && product.isNew) ...[
                      const SizedBox(height: 4.0),
                      _buildNewBadge(),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صورة المنتج
  Widget _buildProductImage(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 120.0,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(12.0),
            ),
            color: Colors.grey[100],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(12.0),
            ),
            child: _buildImage(),
          ),
        ),
        // زر المفضلة
        Positioned(top: 8.0, right: 8.0, child: _buildFavoriteButton(context)),
        // علامة "جديد" إذا كان المنتج جديد
        if (showNewBadge && product.isNew)
          Positioned(top: 8.0, left: 8.0, child: _buildNewBadgeOverlay()),
      ],
    );
  }

  /// بناء الصورة
  Widget _buildImage() {
    if (product.imageUrls.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: product.imageUrls.first,
        fit: BoxFit.cover,
        placeholder:
            (context, url) => Container(
              color: Colors.grey[200],
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 2.0),
              ),
            ),
        errorWidget:
            (context, url, error) => Container(
              color: Colors.grey[200],
              child: const Icon(
                Icons.image_not_supported,
                color: Colors.grey,
                size: 40.0,
              ),
            ),
      );
    } else {
      return Container(
        color: Colors.grey[200],
        child: const Icon(Icons.image, color: Colors.grey, size: 40.0),
      );
    }
  }

  /// بناء زر المفضلة
  Widget _buildFavoriteButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(
          isFavorite ? Icons.favorite : Icons.favorite_border,
          color: isFavorite ? Colors.red : Colors.grey,
          size: 20.0,
        ),
        onPressed: onFavoriteToggle,
        padding: const EdgeInsets.all(4.0),
        constraints: const BoxConstraints(minWidth: 32.0, minHeight: 32.0),
      ),
    );
  }

  /// بناء علامة "جديد" المتراكبة
  Widget _buildNewBadgeOverlay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: const Text(
        'جديد',
        style: TextStyle(
          color: Colors.white,
          fontSize: 10.0,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// بناء اسم المنتج
  Widget _buildProductName() {
    return Text(
      product.name,
      style: const TextStyle(fontSize: 14.0, fontWeight: FontWeight.w600),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// بناء سعر المنتج
  Widget _buildProductPrice() {
    return Row(
      children: [
        Text(
          '${product.price.toStringAsFixed(0)} ج.م',
          style: const TextStyle(
            fontSize: 14.0,
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        if (product.discountPrice != null && product.discountPrice! > 0) ...[
          const SizedBox(width: 4.0),
          Text(
            '${product.discountPrice!.toStringAsFixed(0)} ج.م',
            style: const TextStyle(
              fontSize: 12.0,
              decoration: TextDecoration.lineThrough,
              color: Colors.grey,
            ),
          ),
        ],
      ],
    );
  }

  // تم حذف دالة _buildProductRating حسب المتطلبات

  /// بناء علامة "جديد"
  Widget _buildNewBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(color: Colors.green, width: 1.0),
      ),
      child: Text(
        'جديد',
        style: const TextStyle(
          color: Colors.green,
          fontSize: 10.0,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// الانتقال لتفاصيل المنتج
  void _navigateToProductDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductDetailsScreen(product: product),
      ),
    );
  }
}
