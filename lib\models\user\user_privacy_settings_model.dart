/// نموذج إعدادات الخصوصية للمستخدم
/// يتوافق مع جدول user_privacy_settings في قاعدة البيانات
class UserPrivacySettingsModel {
  final String id;
  final String userId;
  final bool shareProfileData;
  final bool shareOrderHistory;
  // تم حذف shareReviews حسب المتطلبات
  final bool allowTargetedAds;
  final bool allowDataAnalytics;
  final bool showOnlineStatus;
  final bool showLastSeen;
  final bool showActivityStatus;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserPrivacySettingsModel({
    required this.id,
    required this.userId,
    this.shareProfileData = false,
    this.shareOrderHistory = false,
    // تم حذف shareReviews حسب المتطلبات
    this.allowTargetedAds = false,
    this.allowDataAnalytics = true,
    this.showOnlineStatus = true,
    this.showLastSeen = true,
    this.showActivityStatus = true,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نموذج من بيانات JSON
  factory UserPrivacySettingsModel.fromJson(Map<String, dynamic> json) {
    return UserPrivacySettingsModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      shareProfileData: json['share_profile_data'] as bool? ?? false,
      shareOrderHistory: json['share_order_history'] as bool? ?? false,
      // تم حذف shareReviews حسب المتطلبات
      allowTargetedAds: json['allow_targeted_ads'] as bool? ?? false,
      allowDataAnalytics: json['allow_data_analytics'] as bool? ?? true,
      showOnlineStatus: json['show_online_status'] as bool? ?? true,
      showLastSeen: json['show_last_seen'] as bool? ?? true,
      showActivityStatus: json['show_activity_status'] as bool? ?? true,
      createdAt: DateTime.tryParse(json['created_at'] as String? ?? '') ??
          DateTime.now(),
      updatedAt: DateTime.tryParse(json['updated_at'] as String? ?? '') ??
          DateTime.now(),
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'share_profile_data': shareProfileData,
      'share_order_history': shareOrderHistory,
      // تم حذف share_reviews حسب المتطلبات
      'allow_targeted_ads': allowTargetedAds,
      'allow_data_analytics': allowDataAnalytics,
      'show_online_status': showOnlineStatus,
      'show_last_seen': showLastSeen,
      'show_activity_status': showActivityStatus,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  UserPrivacySettingsModel copyWith({
    String? id,
    String? userId,
    bool? shareProfileData,
    bool? shareOrderHistory,
    // تم حذف shareReviews حسب المتطلبات
    bool? allowTargetedAds,
    bool? allowDataAnalytics,
    bool? showOnlineStatus,
    bool? showLastSeen,
    bool? showActivityStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserPrivacySettingsModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      shareProfileData: shareProfileData ?? this.shareProfileData,
      shareOrderHistory: shareOrderHistory ?? this.shareOrderHistory,
      // تم حذف shareReviews حسب المتطلبات
      allowTargetedAds: allowTargetedAds ?? this.allowTargetedAds,
      allowDataAnalytics: allowDataAnalytics ?? this.allowDataAnalytics,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
      showLastSeen: showLastSeen ?? this.showLastSeen,
      showActivityStatus: showActivityStatus ?? this.showActivityStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// إنشاء إعدادات خصوصية افتراضية لمستخدم جديد
  static UserPrivacySettingsModel createDefault(String userId) {
    final now = DateTime.now();
    return UserPrivacySettingsModel(
      id: userId, // استخدام نفس معرف المستخدم كمعرف للإعدادات
      userId: userId,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// التحقق مما إذا كانت جميع إعدادات الخصوصية معطلة
  bool get isAllPrivacyEnabled {
    return !shareProfileData &&
        !shareOrderHistory &&
        // تم حذف shareReviews حسب المتطلبات
        !allowTargetedAds &&
        !allowDataAnalytics &&
        !showOnlineStatus &&
        !showLastSeen &&
        !showActivityStatus;
  }
}