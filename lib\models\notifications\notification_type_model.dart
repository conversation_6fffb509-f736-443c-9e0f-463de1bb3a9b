import 'package:flutter/material.dart';

class NotificationTypeModel {
  final String id;
  final String typeKey;
  final String displayName;
  final String? description;
  final bool defaultEnabled;
  final bool isActive;
  final String category;
  final String? icon;
  final String? color;
  final int priority;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationTypeModel({
    required this.id,
    required this.typeKey,
    required this.displayName,
    this.description,
    required this.defaultEnabled,
    required this.isActive,
    required this.category,
    this.icon,
    this.color,
    required this.priority,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationTypeModel.fromJson(Map<String, dynamic> json) {
    return NotificationTypeModel(
      id: json['id'] as String,
      typeKey: json['type_key'] as String,
      displayName: json['display_name'] as String,
      description: json['description'] as String?,
      defaultEnabled: json['default_enabled'] as bool? ?? true,
      isActive: json['is_active'] as bool? ?? true,
      category: json['category'] as String? ?? 'general',
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      priority: json['priority'] as int? ?? 1,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type_key': typeKey,
      'display_name': displayName,
      'description': description,
      'default_enabled': defaultEnabled,
      'is_active': isActive,
      'category': category,
      'icon': icon,
      'color': color,
      'priority': priority,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  NotificationTypeModel copyWith({
    String? id,
    String? typeKey,
    String? displayName,
    String? description,
    bool? defaultEnabled,
    bool? isActive,
    String? category,
    String? icon,
    String? color,
    int? priority,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationTypeModel(
      id: id ?? this.id,
      typeKey: typeKey ?? this.typeKey,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      defaultEnabled: defaultEnabled ?? this.defaultEnabled,
      isActive: isActive ?? this.isActive,
      category: category ?? this.category,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}