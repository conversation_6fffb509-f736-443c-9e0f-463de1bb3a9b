import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/order_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/core/widgets/loading_indicator.dart';
import 'package:motorcycle_parts_shop/models/orders/order_model.dart';
import 'package:provider/provider.dart';

/// شاشة تتبع الطلب - تعرض حالة الطلب ومراحل التوصيل
class OrderTrackingScreen extends StatefulWidget {
  final String? orderId;
  final OrderModel? order;

  const OrderTrackingScreen({super.key, this.orderId, this.order});

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen>
    with SingleTickerProviderStateMixin {
  late OrderService _orderService;
  OrderModel? _order;
  List<Map<String, dynamic>> _trackingSteps = [];
  bool _isLoading = true;
  String? _error;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _orderService = Provider.of<OrderService>(context, listen: false);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _loadOrderData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الطلب ومراحل التتبع
  Future<void> _loadOrderData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // إذا تم تمرير الطلب مباشرة، استخدمه
      if (widget.order != null) {
        _order = widget.order;
      } else if (widget.orderId != null) {
        // جلب تفاصيل الطلب من الخدمة
        _order = await _orderService.getOrderDetails(widget.orderId!);
      }

      if (_order == null) {
        throw Exception('لم يتم العثور على الطلب');
      }

      // جلب مراحل التتبع من قاعدة البيانات
      _trackingSteps = await _orderService.getOrderTrackingStatus(_order!.id);

      // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
      if (_trackingSteps.isNotEmpty) {
        _trackingSteps =
            _trackingSteps.map((step) {
              return {
                'status': step['status'] ?? '',
                'title': step['title'] ?? '',
                'description': step['description'] ?? '',
                'icon': _getIconFromString(step['icon'] ?? ''),
                'completed':
                    true, // جميع المراحل المحفوظة في قاعدة البيانات مكتملة
                'date': step['created_at'] ?? DateTime.now(),
              };
            }).toList();
      } else {
        // إذا لم تكن هناك مراحل في قاعدة البيانات، عرض رسالة
        _trackingSteps = [];
      }

      _animationController.forward();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحويل اسم الأيقونة من النص إلى IconData
  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'receipt_long':
        return Icons.receipt_long;
      case 'inventory_2':
        return Icons.inventory_2;
      case 'local_shipping':
        return Icons.local_shipping;
      case 'check_circle':
        return Icons.check_circle;
      case 'cancel':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('تتبع الطلب'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOrderData,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_order == null) {
      return _buildNotFoundWidget();
    }

    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);

        return FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(padding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildOrderSummaryCard(),
                SizedBox(height: ResponsiveHelper.isMobile(context) ? 16 : 20),
                _buildTrackingStepsCard(),
                SizedBox(height: ResponsiveHelper.isMobile(context) ? 16 : 20),
                _buildOrderDetailsCard(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل بيانات الطلب',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'خطأ غير معروف',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadOrderData,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotFoundWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على الطلب',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'تحقق من رقم الطلب وحاول مرة أخرى',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.shopping_bag,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص الطلب',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('رقم الطلب', _order!.id),
            _buildInfoRow('التاريخ', _formatDate(_order!.createdAt)),
            _buildInfoRow('الحالة', _getStatusText(_order!.status)),
            _buildInfoRow(
              'المبلغ الإجمالي',
              '${_order!.totalAmount.toStringAsFixed(2)} جنيه',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingStepsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'مراحل التتبع',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ..._trackingSteps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isLast = index == _trackingSteps.length - 1;
              return _buildTrackingStep(step, isLast);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingStep(Map<String, dynamic> step, bool isLast) {
    final isCompleted = step['completed'] ?? false;
    final isCurrent =
        !isCompleted &&
        _trackingSteps.indexOf(step) > 0 &&
        _trackingSteps[_trackingSteps.indexOf(step) - 1]['completed'];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    isCompleted
                        ? AppTheme.primaryColor
                        : isCurrent
                        ? AppTheme.primaryColor.withOpacity(0.3)
                        : Colors.grey[300],
              ),
              child: Icon(
                step['icon'] ?? Icons.circle,
                color:
                    isCompleted
                        ? Colors.white
                        : isCurrent
                        ? AppTheme.primaryColor
                        : Colors.grey[600],
                size: 20,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 60,
                color: isCompleted ? AppTheme.primaryColor : Colors.grey[300],
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step['title'] ?? '',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color:
                        isCompleted ? AppTheme.primaryColor : Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  step['description'] ?? '',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
                if (step['date'] != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    _formatDate(step['date']),
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderDetailsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل إضافية',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // تم حذف عرض طريقة الدفع حسب المتطلبات
            if (_order!.notes?.isNotEmpty == true)
              _buildInfoRow('ملاحظات', _order!.notes!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return 'في الانتظار';
      case AppConstants.orderStatusProcessing:
        return 'قيد التحضير';
      case AppConstants.orderStatusShipped:
        return 'تم الشحن';
      case AppConstants.orderStatusDelivered:
        return 'تم التوصيل';
      case AppConstants.orderStatusCancelled:
        return 'ملغي';
      default:
        return status;
    }
  }
}
