import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../constants/app_constants.dart';
import '../utils/common_operations.dart';

/// خدمة قائمة الرغبات الموحدة - تستخدم قاعدة البيانات فقط
class WishlistService extends ChangeNotifier {
  static final WishlistService _instance = WishlistService._internal();
  final SupabaseClient _supabase = Supabase.instance.client;

  List<ProductModel> _wishlistProducts = [];
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // نمط Singleton
  factory WishlistService() {
    return _instance;
  }

  WishlistService._internal();

  // Getters
  List<ProductModel> get wishlistProducts => _wishlistProducts;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get wishlistCount => _wishlistProducts.length;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    _isLoading = true;
    notifyListeners();

    try {
      await loadUserWishlist();
      _isInitialized = true;
    } catch (e) {
      _error = 'فشل تهيئة خدمة قائمة الرغبات: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحميل قائمة رغبات المستخدم
  Future<void> loadUserWishlist() async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) {
      _wishlistProducts = [];
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _supabase
          .from(AppConstants.wishlistsTable)
          .select('''
            product_id,
            ${AppConstants.productsTable} (
              id, sku, name, description, brand, price, discount_price,
              category_id, company_id, image_urls, stock_quantity,
              is_available, created_at
            )
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      _wishlistProducts =
          response
              .where((item) => item[AppConstants.productsTable] != null)
              .map((item) => ProductModel.fromJson(item[AppConstants.productsTable]))
              .toList();

      debugPrint('تم تحميل ${_wishlistProducts.length} منتج من قائمة الرغبات');
    } catch (e) {
      _error = 'فشل تحميل قائمة الرغبات: $e';
      debugPrint(_error);
      _wishlistProducts = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// التحقق من وجود المنتج في قائمة الرغبات
  bool isInWishlist(String productId) {
    return _wishlistProducts.any((product) => product.id == productId);
  }

  /// إضافة منتج إلى قائمة الرغبات
  Future<bool> addToWishlist(String productId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) {
      _error = 'يجب تسجيل الدخول أولاً';
      notifyListeners();
      return false;
    }

    if (isInWishlist(productId)) {
      return true; // المنتج موجود بالفعل
    }

    try {
      await CommonOperations.executeWithErrorHandling(
        () async {
          await _supabase.from(AppConstants.wishlistsTable).insert({
            'user_id': userId,
            'product_id': productId,
          });

          // إعادة تحميل القائمة لتحديث البيانات
          await loadUserWishlist();

          debugPrint('تمت إضافة المنتج $productId إلى قائمة الرغبات');
          return true;
        },
        operationName: 'addToWishlist',
        maxRetries: 2,
        retryDelay: const Duration(seconds: 1),
      );

      return true;
    } catch (e) {
      _error = 'فشل إضافة المنتج إلى قائمة الرغبات: $e';
      notifyListeners();
      return false;
    }
  }

  /// إزالة منتج من قائمة الرغبات
  Future<bool> removeFromWishlist(String productId) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) {
      _error = 'يجب تسجيل الدخول أولاً';
      notifyListeners();
      return false;
    }

    try {
      await _supabase.from(AppConstants.wishlistsTable).delete().match({
        'user_id': userId,
        'product_id': productId,
      });

      // إزالة المنتج من القائمة المحلية
      _wishlistProducts.removeWhere((product) => product.id == productId);

      debugPrint('تمت إزالة المنتج $productId من قائمة الرغبات');
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'فشل إزالة المنتج من قائمة الرغبات: $e';
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  /// تبديل حالة المنتج في قائمة الرغبات
  Future<bool> toggleWishlist(String productId) async {
    if (isInWishlist(productId)) {
      return await removeFromWishlist(productId);
    } else {
      return await addToWishlist(productId);
    }
  }

  /// مسح قائمة الرغبات بالكامل
  Future<bool> clearWishlist() async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) {
      _error = 'يجب تسجيل الدخول أولاً';
      notifyListeners();
      return false;
    }

    try {
      await _supabase.from(AppConstants.wishlistsTable).delete().eq('user_id', userId);

      _wishlistProducts.clear();
      debugPrint('تم مسح قائمة الرغبات بالكامل');
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'فشل مسح قائمة الرغبات: $e';
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  /// الحصول على منتج من قائمة الرغبات بواسطة المعرف
  ProductModel? getWishlistProduct(String productId) {
    try {
      return _wishlistProducts.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// تحديث منتج في قائمة الرغبات
  void updateWishlistProduct(ProductModel updatedProduct) {
    final index = _wishlistProducts.indexWhere(
      (product) => product.id == updatedProduct.id,
    );

    if (index != -1) {
      _wishlistProducts[index] = updatedProduct;
      notifyListeners();
    }
  }

  /// مسح الأخطاء
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// إعادة تحميل قائمة الرغبات
  Future<void> refresh() async {
    await loadUserWishlist();
  }
}
