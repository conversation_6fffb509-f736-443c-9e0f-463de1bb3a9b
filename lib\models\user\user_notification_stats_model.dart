import 'package:flutter/material.dart';

class UserNotificationStatsModel {
  final String id;
  final String userId;
  final int totalNotifications;
  final int unreadNotifications;
  final int totalRead;
  final DateTime? lastNotificationRead;
  final DateTime? lastNotificationAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserNotificationStatsModel({
    required this.id,
    required this.userId,
    required this.totalNotifications,
    required this.unreadNotifications,
    this.totalRead = 0,
    this.lastNotificationRead,
    this.lastNotificationAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserNotificationStatsModel.fromJson(Map<String, dynamic> json) {
    return UserNotificationStatsModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      totalNotifications: json['total_notifications'] as int? ?? 0,
      unreadNotifications: json['unread_notifications'] as int? ?? 0,
      totalRead: json['total_read'] as int? ?? 0,
      lastNotificationRead: json['last_notification_read'] != null ? DateTime.parse(json['last_notification_read'] as String) : null,
      lastNotificationAt: json['last_notification_at'] != null ? DateTime.parse(json['last_notification_at'] as String) : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'total_notifications': totalNotifications,
      'unread_notifications': unreadNotifications,
      'total_read': totalRead,
      'last_notification_read': lastNotificationRead?.toIso8601String(),
      'last_notification_at': lastNotificationAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserNotificationStatsModel copyWith({
    String? id,
    String? userId,
    int? totalNotifications,
    int? unreadNotifications,
    int? totalRead,
    DateTime? lastNotificationRead,
    DateTime? lastNotificationAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserNotificationStatsModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      totalNotifications: totalNotifications ?? this.totalNotifications,
      unreadNotifications: unreadNotifications ?? this.unreadNotifications,
      totalRead: totalRead ?? this.totalRead,
      lastNotificationRead: lastNotificationRead ?? this.lastNotificationRead,
      lastNotificationAt: lastNotificationAt ?? this.lastNotificationAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}