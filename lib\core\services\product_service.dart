import 'dart:async';

import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/advanced_search_service.dart';
import 'package:motorcycle_parts_shop/core/utils/service_locator.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../models/category_model.dart';
import '../../models/company_model.dart';
import '../../models/products/product_model.dart';
import '../utils/common_operations.dart';

/// خدمة المنتجات لجلب وإدارة بيانات المنتجات مع دعم التحميل الكسول والتخزين المؤقت المحسن
class ProductService {
  final SupabaseClient _supabase;
  final _cache = ServiceLocator.storage;
  bool _isInitialized = false;

  // إحصائيات التخزين المؤقت
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _estimatedCacheSize = 0;

  // مدة التخزين المؤقت للمنتجات
  static const Duration _productCacheDuration = Duration(minutes: 5);
  // مدة التخزين المؤقت للفئات والشركات (بيانات أقل تغييراً)
  static const Duration _metadataCacheDuration = Duration(hours: 1);
  // مدة التخزين المؤقت لنتائج البحث
  static const Duration _searchCacheDuration = Duration(minutes: 2);

  static const int defaultPageSize = 10;

  // حقول المنتج المطلوبة في الاستعلامات
  static const String _productFields =
      'id, sku, name, description, brand, price, discount_price, original_price, category_id, company_id, created_at, view_count, sales_count, is_featured, is_best_selling, is_available, is_on_sale, stock_quantity, min_stock_level, image_urls, specifications, is_new, new_until, meta_title, meta_description, tags, weight, dimensions';

  // بادئة مفاتيح التخزين المؤقت
  static const String _cacheKeyPrefix = 'product_service_';

  ProductService(this._supabase) {
    _initialize();
  }

  bool get isInitialized => _isInitialized;

  /// تهيئة الخدمة مع التحقق من الاتصال بـ Supabase
  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      // التأكد من تهيئة خدمة التخزين الموحدة
      await _cache.initialize();

      await _checkConnection();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة المنتجات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة المنتجات: $e');
      // السماح بالعمل دون اتصال مع تسجيل الخطأ
      _isInitialized = true;
    }
  }

  /// إبطال التخزين المؤقت بالكامل
  Future<void> invalidateCache() async {
    await _cache.clear();
    debugPrint('🧹 تم إبطال التخزين المؤقت لخدمة المنتجات بالكامل');
  }

  /// إبطال التخزين المؤقت لمفتاح محدد
  Future<void> invalidateCacheKey(String key) async {
    final fullKey = _cacheKeyPrefix + key;
    _cache.removeCachedData(fullKey);
    debugPrint('🗑️ تم إبطال التخزين المؤقت للمفتاح: $key');
  }

  /// التحقق من الاتصال بقاعدة البيانات
  Future<void> _checkConnection() async {
    try {
      await _supabase
          .from('products')
          .select('id')
          .limit(1)
          .timeout(const Duration(seconds: 10));
    } catch (e) {
      debugPrint('⚠️ خطأ في الاتصال بخادم Supabase: $e');
      // السماح بالعمل دون اتصال مع تسجيل الخطأ
    }
  }

  /// الحصول على بيانات مخزنة مؤقتاً مع دعم التحديث التلقائي
  Future<T> _getCachedData<T>(
    String key,
    Future<T> Function() fetcher, {
    Duration? expiry,
    bool forceRefresh = false,
  }) async {
    final fullKey = _cacheKeyPrefix + key;

    return await _cache.getCachedData<T>(
      fullKey,
      fetcher,
      expiry: expiry,
      forceRefresh: forceRefresh,
    );
  }

  // هذه الدوال تم تعليقها لأنها غير مستخدمة حالياً، ولكن قد تكون مفيدة في المستقبل

  /*
  /// تخزين بيانات في الذاكرة المؤقتة يدوياً
  void _setCachedData<T>(String key, T data, {Duration? expiry}) {
    final fullKey = _cacheKeyPrefix + key;
    _storage.setCachedData<T>(fullKey, data, expiry: expiry);
  }

  /// إزالة بيانات من الذاكرة المؤقتة
  void _removeCachedData(String key) {
    final fullKey = _cacheKeyPrefix + key;
    _storage.removeCachedData(fullKey);
  }

  /// إبطال مجموعة من مفاتيح التخزين المؤقت بناءً على نمط
  void _invalidateCachePattern(String pattern) {
    final keys = _storage.getKeys();
    final keysToInvalidate =
        keys
            .where(
              (key) => key.startsWith(_cacheKeyPrefix) && key.contains(pattern),
            )
            .toList();

    for (final key in keysToInvalidate) {
      _storage.remove(key);
    }

    if (keysToInvalidate.isNotEmpty) {
      debugPrint(
        '🧹 تم إبطال ${keysToInvalidate.length} مفتاح يطابق النمط: $pattern',
      );
    }
  }
  */

  /// الحصول على بيانات من التخزين المؤقت
  Future<T?> _getFromCache<T>(String key) async {
    final fullKey = _cacheKeyPrefix + key;

    // استخدام getCachedData مع دالة تُرجع قيمة افتراضية null
    try {
      final cachedData = await _cache.getCachedData<T>(
        fullKey,
        () async => throw Exception('Cache miss'),
        forceRefresh: false,
      );

      _cacheHits++;
      return cachedData;
    } catch (e) {
      _cacheMisses++;
      return null;
    }
  }

  /// إضافة بيانات إلى التخزين المؤقت
  Future<void> _addToCache<T>(String key, T data) async {
    final fullKey = _cacheKeyPrefix + key;
    _cache.setCachedData(fullKey, data);

    // تقدير حجم البيانات المخزنة
    _estimateDataSize(data);
  }

  /// تقدير حجم البيانات المخزنة
  void _estimateDataSize(dynamic data) {
    if (data is List) {
      _estimatedCacheSize += data.length * 100; // تقدير تقريبي
    } else if (data is Map) {
      _estimatedCacheSize += data.length * 50; // تقدير تقريبي
    } else {
      _estimatedCacheSize += 10; // تقدير تقريبي للقيم البسيطة
    }
  }

  /// إزالة بيانات من التخزين المؤقت
  void _removeFromCache(String key) {
    final fullKey = _cacheKeyPrefix + key;
    _cache.removeCachedData(fullKey);
  }

  /// جلب عدد المنتجات الإجمالي
  Future<int> getProductsCount({String? category}) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'products_count_$category';
      final cachedCount = await _getFromCache<int>(cacheKey);
      if (cachedCount != null) {
        return cachedCount;
      }

      var queryBuilder = _supabase.from(AppConstants.productsTable).select('*');

      if (category != null && category.isNotEmpty) {
        queryBuilder = queryBuilder.eq('category_id', category);
      }

      final List<dynamic> response = await queryBuilder;
      final int count = response.length;
      _addToCache(cacheKey, count);
      return count;
    } catch (e) {
      debugPrint('خطأ في جلب عدد المنتجات: $e');
      throw Exception('فشل جلب عدد المنتجات: $e');
    }
  }

  /// جلب المنتجات مع دعم ترقيم الصفحات
  Future<List<ProductModel>> getProducts({
    required int page,
    int pageSize = defaultPageSize,
    String? category,
    String sortBy = 'created_at',
    bool ascending = false,
    bool forceRefresh = false,
  }) async {
    if (!_isInitialized) await _initialize();

    return await CommonOperations.executeWithErrorHandling(
      () async {
        final cacheKey =
            'products_${page}_${pageSize}_${category}_${sortBy}_$ascending';

        return _getCachedData<List<ProductModel>>(
          cacheKey,
          () async {
            final int startRange = page * pageSize;
            final int endRange = startRange + pageSize - 1;

            var query = _supabase
                .from(AppConstants.productsTable)
                .select(_productFields);

            if (category != null && category.isNotEmpty) {
              query = query.eq('category_id', category);
            }

            final response = await query
                .order(sortBy, ascending: ascending)
                .range(startRange, endRange)
                .timeout(const Duration(seconds: 10));

            final products =
                response.map((data) => ProductModel.fromJson(data)).toList();

            // التحقق من صحة البيانات
            final validProducts = <ProductModel>[];
            for (final product in products) {
              final validation = product.validateProduct();
              if (validation.isValid) {
                validProducts.add(product);
              } else {
                debugPrint(
                  '⚠️ منتج غير صالح: ${product.id} - ${validation.errorMessage}',
                );
              }
            }

            return validProducts;
          },
          expiry: _productCacheDuration,
          forceRefresh: forceRefresh,
        );
      },
      operationName: 'getProducts',
      maxRetries: 3,
      retryDelay: const Duration(seconds: 2),
    );
  }

  /// جلب المنتجات الشائعة
  Future<List<ProductModel>> getPopularProducts({int limit = 10}) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'popular_products_$limit';
      final cachedProducts = await _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final response = await _supabase
          .from('products')
          .select(_productFields)
          .order('view_count', ascending: false)
          .limit(limit)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الشائعة: $e');
      throw Exception('فشل جلب المنتجات الشائعة: $e');
    }
  }

  /// جلب المنتجات المميزة
  Future<List<ProductModel>> getFeaturedProducts({
    required int page,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'featured_${page}_$pageSize';
      final cachedProducts = await _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from(AppConstants.productsTable)
          .select(_productFields)
          .eq('is_featured', true)
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المميزة: $e');
      throw Exception('فشل جلب المنتجات المميزة: $e');
    }
  }

  /// جلب المنتجات الأكثر مبيعًا
  Future<List<ProductModel>> getBestSellingProducts({
    required int page,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'bestselling_${page}_$pageSize';
      final cachedProducts = await _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from(AppConstants.productsTable)
          .select(_productFields)
          .order('sales_count', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الأكثر مبيعًا: $e');
      throw Exception('فشل جلب المنتجات الأكثر مبيعًا: $e');
    }
  }

  /// البحث عن المنتجات (مستبدل بـ AdvancedSearchService)
  Future<List<ProductModel>> searchProducts({
    required String searchQuery,
    required int page,
    int pageSize = defaultPageSize,
    bool forceRefresh = false,
  }) async {
    // استخدام AdvancedSearchService بدلاً من هذه الوظيفة
    final advancedSearch = AdvancedSearchService();
    return await advancedSearch.searchProducts(
      searchQuery: searchQuery,
      limit: pageSize,
      offset: page * pageSize,
    );
  }

  /// البحث عن المنتجات مع كاش
  Future<List<ProductModel>> searchProductsWithCache({
    required String searchQuery,
    int page = 0,
    int pageSize = defaultPageSize,
    bool forceRefresh = false,
  }) async {
    if (!_isInitialized) await _initialize();

    final cacheKey = 'search_${searchQuery}_$page';
    return await _getCachedData<List<ProductModel>>(
      cacheKey,
      () async {
        final int startRange = page * pageSize;
        final int endRange = startRange + pageSize - 1;
        final response = await _supabase
            .from(AppConstants.productsTable)
            .select(_productFields)
            .or(
              'name.ilike.%$searchQuery%,description.ilike.%$searchQuery%,sku.ilike.%$searchQuery%',
            )
            .order('created_at', ascending: false)
            .range(startRange, endRange);
        return (response as List)
            .map((item) => ProductModel.fromJson(item))
            .toList();
      },
      expiry: _searchCacheDuration,
      forceRefresh: forceRefresh,
    );
  }

  /// جلب الفئات
  Future<List<CategoryModel>> getCategories({bool forceRefresh = false}) async {
    if (!_isInitialized) await _initialize();

    try {
      return await _getCachedData<List<CategoryModel>>(
        'categories',
        () async {
          final response = await _supabase
              .from(AppConstants.categoriesTable)
              .select(
                'id, name, description, image_url, icon_data, parent_id, sort_order, is_active, products_count',
              )
              .order('name')
              .timeout(const Duration(seconds: 10));

          return response.map((data) => CategoryModel.fromJson(data)).toList();
        },
        expiry: _metadataCacheDuration, // فترة تخزين مؤقت أطول للبيانات الوصفية
        forceRefresh: forceRefresh,
      );
    } catch (e) {
      debugPrint('❌ خطأ في جلب الفئات: $e');

      // في حالة الخطأ، نحاول استرداد الفئات من التخزين المؤقت حتى لو كانت منتهية الصلاحية
      try {
        final fullKey = '${_cacheKeyPrefix}categories';
        try {
          final cachedData = await _cache.getCachedData<List<CategoryModel>>(
            fullKey,
            () async => throw Exception('Cache miss'),
            forceRefresh: false,
          );
          debugPrint(
            '⚠️ استخدام نسخة مخزنة مؤقتاً من الفئات بسبب خطأ في الاتصال',
          );
          return cachedData;
        } catch (_) {
          // No cached data available
          return [];
        }
      } catch (_) {
        // تجاهل أي خطأ في استرداد البيانات المخزنة مؤقتاً
      }

      throw Exception('فشل جلب الفئات: $e');
    }
  }

  /// جلب الشركات المصنعة
  Future<List<CompanyModel>> getCompanies() async {
    if (!_isInitialized) await _initialize();

    try {
      final cachedCompanies = await _getFromCache<List<CompanyModel>>(
        'companies',
      );
      if (cachedCompanies != null) {
        return cachedCompanies;
      }

      final response = await _supabase
          .from(AppConstants.companiesTable)
          .select('id, name, logo, description, is_active, products_count')
          .order('name')
          .timeout(const Duration(seconds: 10));

      final companies =
          response.map((data) => CompanyModel.fromJson(data)).toList();
      _addToCache('companies', companies);
      return companies;
    } catch (e) {
      debugPrint('خطأ في جلب الشركات: $e');
      throw Exception('فشل جلب الشركات: $e');
    }
  }

  /// جلب منتج بواسطة المعرف
  Future<ProductModel?> getProductById(
    String id, {
    bool forceRefresh = false,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'product_$id';

      return await _getCachedData<ProductModel?>(
        cacheKey,
        () async {
          final response = await _supabase
              .from(AppConstants.productsTable)
              .select(_productFields)
              .eq('id', id)
              .maybeSingle()
              .timeout(const Duration(seconds: 10));

          if (response == null) {
            return null;
          }

          final product = ProductModel.fromJson(response);
          return product;
        },
        expiry: _productCacheDuration,
        forceRefresh: forceRefresh,
      );
    } catch (e) {
      debugPrint('❌ خطأ في جلب المنتج: $e');
      // في حالة الخطأ، نحاول استرداد المنتج من التخزين المؤقت حتى لو كان منتهي الصلاحية
      try {
        final fullKey = '${_cacheKeyPrefix}product_$id';
        try {
          final cachedProduct = await _cache.getCachedData<ProductModel>(
            fullKey,
            () async => throw Exception('Cache miss'),
            forceRefresh: false,
          );
          debugPrint(
            '⚠️ استخدام نسخة مخزنة مؤقتاً من المنتج بسبب خطأ في الاتصال',
          );
          return cachedProduct;
        } catch (_) {
          // No cached data available
          return null;
        }
      } catch (_) {
        // تجاهل أي خطأ في استرداد البيانات المخزنة مؤقتاً
      }

      throw Exception('فشل جلب المنتج: $e');
    }
  }

  /// تحديث عدد مشاهدات المنتج
  Future<void> incrementProductViewCount(String productId) async {
    if (!_isInitialized) await _initialize();

    try {
      // تحديث عدد المشاهدات في قاعدة البيانات
      await _supabase
          .from(AppConstants.productsTable)
          .update({
            'view_count': _supabase.rpc(
              'increment',
              params: {
                'row_id': productId,
                'table_name': AppConstants.productsTable,
                'column_name': 'view_count',
                'increment_amount': 1,
              },
            ),
          })
          .eq('id', productId)
          .timeout(const Duration(seconds: 10));

      // تحديث المنتج في الذاكرة المؤقتة
      final cacheKey = 'product_$productId';
      final cachedProduct = await _getFromCache<ProductModel>(cacheKey);
      if (cachedProduct != null) {
        final updatedProduct = cachedProduct.copyWith(
          viewCount: cachedProduct.viewCount + 1,
        );
        await _addToCache(cacheKey, updatedProduct);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث عدد مشاهدات المنتج: $e');
      // لا نرمي استثناء هنا لتجنب تعطيل تجربة المستخدم
    }
  }

  /// تسجيل مشاهدة المنتج مع بيانات إضافية
  Future<void> logProductView({
    required String productId,
    String? userId,
    String? sessionId,
    int? viewDurationSeconds,
    String? referrerSource,
    String? deviceType,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      // تسجيل مشاهدة المنتج في جدول product_views
      await _supabase
          .from('product_views')
          .insert({
            'product_id': productId,
            'user_id': userId,
            'session_id': sessionId,
            'view_duration_seconds': viewDurationSeconds,
            'referrer_source': referrerSource,
            'device_type': deviceType,
          })
          .timeout(const Duration(seconds: 10));

      // تحديث عدد المشاهدات في جدول المنتجات
      await incrementProductViewCount(productId);
    } catch (e) {
      debugPrint('خطأ في تسجيل مشاهدة المنتج: $e');
      // لا نرمي استثناء هنا لتجنب تعطيل تجربة المستخدم
    }
  }

  /// تنظيف الذاكرة المؤقتة
  void clearCache() {
    _cache.clear();
    _estimatedCacheSize = 0;
    debugPrint('تم تنظيف الذاكرة المؤقتة بالكامل');
  }

  /// تنظيف الذاكرة المؤقتة لمنتج محدد
  void clearProductCache(String productId) {
    final cacheKey = 'product_$productId';
    _removeFromCache(cacheKey);
    debugPrint('تم تنظيف الذاكرة المؤقتة للمنتج: $productId');
  }

  /// تنظيف الذاكرة المؤقتة لفئة محددة
  Future<void> clearCategoryCache(String categoryId) async {
    final keysToRemove = <String>[];
    final allKeys = _cache.getKeys();
    for (final key in allKeys) {
      if (key.contains(categoryId)) {
        keysToRemove.add(key);
      }
    }
    for (final key in keysToRemove) {
      _removeFromCache(key);
    }
    debugPrint('تم تنظيف الذاكرة المؤقتة للفئة: $categoryId');
  }

  /// الحصول على إحصائيات الذاكرة المؤقتة
  Future<Map<String, dynamic>> getCacheStats() async {
    final allKeys = _cache.getKeys();
    return {
      'cacheSize': allKeys.length,
      'estimatedSizeBytes': _estimatedCacheSize,
      'cacheHits': _cacheHits,
      'cacheMisses': _cacheMisses,
      'hitRatio':
          _cacheHits + _cacheMisses > 0
              ? (_cacheHits / (_cacheHits + _cacheMisses))
              : 0.0,
    };
  }

  /// تبديل حالة المفضلة للمنتج
  Future<bool> toggleFavoriteStatus(String productId, String userId) async {
    if (!_isInitialized) await _initialize();

    try {
      // التحقق من وجود المنتج في المفضلة
      final response =
          await _supabase
              .from(AppConstants.favoritesTable)
              .select()
              .eq('user_id', userId)
              .eq('product_id', productId)
              .maybeSingle();

      if (response == null) {
        // إضافة المنتج إلى المفضلة
        await _supabase.from(AppConstants.favoritesTable).insert({
          'user_id': userId,
          'product_id': productId,
          'created_at': DateTime.now().toIso8601String(),
        });
        return true; // تمت الإضافة بنجاح
      } else {
        // إزالة المنتج من المفضلة
        await _supabase
            .from(AppConstants.favoritesTable)
            .delete()
            .eq('user_id', userId)
            .eq('product_id', productId);
        return false; // تمت الإزالة بنجاح
      }
    } catch (e) {
      debugPrint('خطأ في تبديل حالة المفضلة: $e');
      throw Exception('فشل تبديل حالة المفضلة: $e');
    }
  }

  /// الحصول على المنتجات الجديدة
  Future<List<ProductModel>> getNewProducts({
    required int page,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'new_products_${page}_$pageSize';
      final cachedProducts = await _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final now = DateTime.now();
      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from(AppConstants.productsTable)
          .select(_productFields)
          .eq('is_new', true)
          .gte('new_until', now.toIso8601String())
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الجديدة: $e');
      return []; // إرجاع قائمة فارغة في حالة الخطأ
    }
  }

  /// الحصول على الحد الأقصى لسعر المنتجات
  Future<double> getMaxProductPrice() async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'max_product_price';
      final cachedPrice = await _getFromCache<double>(cacheKey);
      if (cachedPrice != null) {
        return cachedPrice;
      }

      final response =
          await _supabase
              .from('products')
              .select('price')
              .order('price', ascending: false)
              .limit(1)
              .single();

      final maxPrice = (response['price'] as num).toDouble();
      _addToCache(cacheKey, maxPrice);
      return maxPrice;
    } catch (e) {
      debugPrint('خطأ في جلب الحد الأقصى لسعر المنتجات: $e');
      return 10000.0; // قيمة افتراضية في حالة الخطأ
    }
  }

  /// الحصول على الشركات المتاحة
  Future<List<Map<String, dynamic>>> getAvailableCompanies() async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'available_companies';
      final cachedCompanies = await _getFromCache<List<Map<String, dynamic>>>(
        cacheKey,
      );
      if (cachedCompanies != null) {
        return cachedCompanies;
      }

      final response = await _supabase
          .from('companies')
          .select('id, name, logo_url')
          .order('name')
          .timeout(const Duration(seconds: 10));

      final companies = List<Map<String, dynamic>>.from(response);
      _addToCache(cacheKey, companies);
      return companies;
    } catch (e) {
      debugPrint('خطأ في جلب الشركات المتاحة: $e');
      return [];
    }
  }

  /// البحث المتقدم عن المنتجات (مستبدل بـ AdvancedSearchService)
  @deprecated
  Future<List<ProductModel>> searchProductsExtended({
    required String query,
    String? category,
    double? minPrice,
    double? maxPrice,
    String? brand,
    String? companyId,
    bool inStock = false,
    bool onSale = false,
    int page = 0,
    int pageSize = defaultPageSize,
  }) async {
    // استخدام AdvancedSearchService بدلاً من هذه الوظيفة
    final advancedSearch = AdvancedSearchService();
    return await advancedSearch.searchProducts(
      searchQuery: query,
      categories: category != null ? [category] : null,
      minPrice: minPrice,
      maxPrice: maxPrice,
      brands: brand != null ? [brand] : null,
      inStock: inStock,
      limit: pageSize,
      offset: page * pageSize,
    );
  }

  /// الحصول على المنتجات مرتبة حسب معيار معين
  Future<List<ProductModel>> getSortedProducts({
    required String sortBy,
    bool ascending = false,
    int page = 0,
    int pageSize = defaultPageSize,
    String? category,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey =
          'sorted_${sortBy}_${ascending}_${page}_${pageSize}_$category';
      final cachedProducts = await _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      var queryBuilder = _supabase.from('products').select(_productFields);

      if (category != null && category.isNotEmpty) {
        queryBuilder = queryBuilder.eq('category_id', category);
      }

      final response = await queryBuilder
          .order(sortBy, ascending: ascending)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المرتبة: $e');
      return [];
    }
  }

  /// الحصول على المنتجات حسب الشركة المصنعة
  Future<List<ProductModel>> getProductsByCompany({
    required String companyId,
    int page = 0,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'company_products_${companyId}_${page}_$pageSize';
      final cachedProducts = await _getFromCache<List<ProductModel>>(cacheKey);
      if (cachedProducts != null) {
        return cachedProducts;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      final response = await _supabase
          .from('products')
          .select(_productFields)
          .eq('company_id', companyId)
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      final products =
          response.map((data) => ProductModel.fromJson(data)).toList();
      _addToCache(cacheKey, products);
      return products;
    } catch (e) {
      debugPrint('خطأ في جلب منتجات الشركة: $e');
      return [];
    }
  }

  /// الحصول على تفاصيل منتج معين
  Future<ProductModel?> getProductDetails(String productId) async {
    if (!_isInitialized) await _initialize();

    try {
      final cacheKey = 'product_details_$productId';
      final cachedProduct = _getFromCache<ProductModel>(cacheKey);
      if (cachedProduct != null) {
        return cachedProduct;
      }

      final response =
          await _supabase
              .from('products')
              .select(
                '$_productFields, category:categories(*), company:companies(*)',
              )
              .eq('id', productId)
              .maybeSingle();

      if (response == null) {
        return null;
      }

      final product = ProductModel.fromJson(response);

      // زيادة عداد المشاهدات
      await _supabase
          .from('products')
          .update({'view_count': product.viewCount + 1})
          .eq('id', productId);

      // إبطال التخزين المؤقت للمنتجات ذات الصلة
      await invalidateCacheKey('product_details_$productId');

      // إعادة تخزين المنتج المحدث
      final updatedProduct = product.copyWith(viewCount: product.viewCount + 1);
      _addToCache(cacheKey, updatedProduct);
      return updatedProduct;
    } catch (e) {
      debugPrint('خطأ في جلب تفاصيل المنتج: $e');
      return null;
    }
  }

  /// تحديث بيانات منتج في قاعدة البيانات
  Future<ProductModel> updateProduct(ProductModel product) async {
    if (!_isInitialized) await _initialize();

    try {
      // التحقق من صحة بيانات المنتج قبل التحديث
      final validation = product.validateProduct();
      if (!validation.isValid) {
        throw Exception(
          'بيانات المنتج غير صالحة: ${validation.errors.join(', ')}',
        );
      }

      // تحديث المنتج في قاعدة البيانات
      await _supabase
          .from('products')
          .update(product.toJson())
          .eq('id', product.id);

      // إبطال جميع مفاتيح التخزين المؤقت المتعلقة بالمنتج
      await invalidateCacheKey('product_details_${product.id}');

      // إبطال قوائم المنتجات في التخزين المؤقت
      final allKeys = _cache.getKeys();
      final keysToInvalidate =
          allKeys
              .where(
                (key) =>
                    key.startsWith('products_') ||
                    key.startsWith('featured_') ||
                    key.startsWith('bestselling_') ||
                    key.startsWith('new_products_') ||
                    key.startsWith('popular_products_'),
              )
              .toList();

      for (final key in keysToInvalidate) {
        await invalidateCacheKey(key);
      }

      // الحصول على البيانات المحدثة من قاعدة البيانات
      final updatedProduct = await getProductDetails(product.id);
      if (updatedProduct == null) {
        throw Exception('فشل في الحصول على بيانات المنتج المحدثة');
      }

      return updatedProduct;
    } catch (e) {
      debugPrint('خطأ في تحديث المنتج: $e');
      throw Exception('فشل في تحديث المنتج: $e');
    }
  }

  /// حذف منتج من قاعدة البيانات (للمشرفين فقط)
  Future<bool> deleteProduct(String productId, {String? adminUserId}) async {
    if (!_isInitialized) await _initialize();

    try {
      // التحقق من صحة معرف المنتج
      if (productId.isEmpty) {
        throw Exception('معرف المنتج مطلوب');
      }

      // التحقق من وجود المنتج
      final existingProduct = await getProductDetails(productId);
      if (existingProduct == null) {
        throw Exception('المنتج غير موجود');
      }

      // حذف المنتج من قاعدة البيانات
      await _supabase.from('products').delete().eq('id', productId);

      // إبطال جميع مفاتيح التخزين المؤقت المتعلقة بالمنتج
      await invalidateCacheKey('product_details_$productId');

      // إبطال قوائم المنتجات في التخزين المؤقت
      final allKeys2 = _cache.getKeys();
      final keysToInvalidate =
          allKeys2
              .where(
                (key) =>
                    key.startsWith('products_') ||
                    key.startsWith('featured_') ||
                    key.startsWith('bestselling_') ||
                    key.startsWith('new_products_') ||
                    key.startsWith('popular_products_'),
              )
              .toList();

      for (final key in keysToInvalidate) {
        await invalidateCacheKey(key);
      }

      // تسجيل العملية
      debugPrint('✅ تم حذف المنتج بنجاح: $productId');
      if (adminUserId != null) {
        debugPrint('👤 تم الحذف بواسطة المشرف: $adminUserId');
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف المنتج: $e');
      throw Exception('فشل في حذف المنتج: $e');
    }
  }

  /// التخلص من الموارد عند إنهاء الخدمة
  Future<void> dispose() async {
    // إبطال التخزين المؤقت لخدمة المنتجات فقط
    await invalidateCache();

    debugPrint('♻️ تم التخلص من موارد خدمة المنتجات');
  }
}
