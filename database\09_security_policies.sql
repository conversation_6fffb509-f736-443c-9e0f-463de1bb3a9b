
-
-- ===================================================================
-- دوال مساعدة للأمان
-- ===================================================================

-- دالة التحقق من صلاحيات المشرف - محسنة مع معالجة أفضل للأخطاء
CREATE OR REPLACE FUNCTION is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_is_admin BOOLEAN := false;
    v_user_exists BOOLEAN := false;
BEGIN
    -- التحقق من وجود معرف المستخدم
    IF user_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- التحقق من وجود جدول profiles
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') THEN
        RETURN false;
    END IF;
    
    -- التحقق من وجود المستخدم في جدول auth.users
    BEGIN
        SELECT EXISTS (SELECT 1 FROM auth.users WHERE id = user_id) INTO v_user_exists;
    EXCEPTION WHEN OTHERS THEN
        -- في حالة حدوث خطأ في الوصول إلى جدول auth.users
        RAISE WARNING 'خطأ في التحقق من وجود المستخدم في جدول auth.users: %', SQLERRM;
        v_user_exists := false;
    END;
    
    -- إذا لم يكن المستخدم موجودًا، نعيد false
    IF NOT v_user_exists THEN
        RETURN false;
    END IF;
    
    -- التحقق من نوع الملف الشخصي
    BEGIN
        SELECT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = user_id
            AND profile_type = 'admin'
            AND is_active = true
        ) INTO v_is_admin;
    EXCEPTION WHEN OTHERS THEN
        -- في حالة حدوث خطأ في الوصول إلى جدول profiles
        RAISE WARNING 'خطأ في التحقق من صلاحيات المشرف: %', SQLERRM;
        v_is_admin := false;
        
        -- تسجيل محاولة الوصول الفاشلة في سجل الأمان
        BEGIN
            INSERT INTO security_audit_log (
                user_id,
                action,
                table_name,
                record_id,
                old_values,
                new_values,
                ip_address,
                user_agent
            ) VALUES (
                user_id,
                'admin_check_failed',
                'profiles',
                user_id,
                NULL,
                jsonb_build_object('error', SQLERRM),
                inet_client_addr(),
                current_setting('request.headers', true)::json->>'user-agent'
            );
        EXCEPTION WHEN OTHERS THEN
            -- تجاهل أي أخطاء في تسجيل الحدث
            NULL;
        END;
    END;
    
    -- تسجيل محاولة الوصول الناجحة للمشرف في سجل الأمان
    IF v_is_admin THEN
        BEGIN
            INSERT INTO security_audit_log (
                user_id,
                action,
                table_name,
                record_id,
                ip_address,
                user_agent
            ) VALUES (
                user_id,
                'admin_access',
                'profiles',
                user_id,
                inet_client_addr(),
                current_setting('request.headers', true)::json->>'user-agent'
            );
        EXCEPTION WHEN OTHERS THEN
            -- تجاهل أي أخطاء في تسجيل الحدث
            NULL;
        END;
    END IF;
    
    RETURN v_is_admin;
END;
$$;
-- ===================================================================

-- دالة التحقق من ملكية المورد - محسنة مع معالجة أفضل للأخطاء
CREATE OR REPLACE FUNCTION owns_resource(
    resource_user_id UUID, 
    user_id UUID DEFAULT auth.uid(),
    resource_type TEXT DEFAULT NULL,
    resource_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_owns BOOLEAN := false;
    v_is_admin BOOLEAN := false;
BEGIN
    -- التحقق من وجود معرفات المستخدمين
    IF resource_user_id IS NULL OR user_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- التحقق مما إذا كان المستخدم هو مالك المورد
    v_owns := (resource_user_id = user_id);
    
    -- التحقق مما إذا كان المستخدم مشرفًا (المشرفون يمكنهم الوصول إلى جميع الموارد)
    BEGIN
        SELECT is_admin(user_id) INTO v_is_admin;
    EXCEPTION WHEN OTHERS THEN
        v_is_admin := false;
    END;
    
    -- تسجيل محاولة الوصول في سجل الأمان إذا كانت المعلومات متوفرة
    IF resource_type IS NOT NULL AND resource_id IS NOT NULL THEN
        BEGIN
            INSERT INTO security_audit_log (
                user_id,
                action,
                table_name,
                record_id,
                old_values,
                ip_address,
                user_agent
            ) VALUES (
                user_id,
                CASE 
                    WHEN v_owns THEN 'resource_access_owner'
                    WHEN v_is_admin THEN 'resource_access_admin'
                    ELSE 'resource_access_denied'
                END,
                resource_type,
                resource_id,
                jsonb_build_object(
                    'resource_user_id', resource_user_id,
                    'is_owner', v_owns,
                    'is_admin', v_is_admin
                ),
                inet_client_addr(),
                current_setting('request.headers', true)::json->>'user-agent'
            );
        EXCEPTION WHEN OTHERS THEN
            -- تجاهل أي أخطاء في تسجيل الحدث
            NULL;
        END;
    END IF;
    
    -- المستخدم يملك المورد إذا كان هو المالك أو كان مشرفًا
    RETURN v_owns OR v_is_admin;
END;
$$;



-- دالة موحدة للتحقق من صلاحيات تحليلات البحث
CREATE OR REPLACE FUNCTION can_access_search_analytics(
    p_action VARCHAR DEFAULT 'view',
    user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- التحقق من أن المستخدم مشرف نشط
    RETURN EXISTS (
        SELECT 1 FROM profiles
        WHERE id = user_id
        AND profile_type = 'admin'
        AND is_active = true
    );
END;
$$;
-- ===================================================================

-- دالة فحص عمر البيانات للتعديل
CREATE OR REPLACE FUNCTION is_search_analytics_modifiable(record_date TIMESTAMPTZ)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN record_date > NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- دالة فحص إمكانية الحذف
CREATE OR REPLACE FUNCTION is_search_analytics_deletable(record_date TIMESTAMPTZ)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN record_date < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- ===================================================================

-- إنشاء الدالة الجديدة بعد التعديل
CREATE OR REPLACE FUNCTION check_all_functions_security()
RETURNS TABLE (
    function_name TEXT,
    has_security_definer BOOLEAN,
    has_search_path BOOLEAN,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.proname::TEXT,
        p.prosecdef,
        ('search_path=public' = ANY(p.proconfig)),
        CASE
            WHEN p.prosecdef AND ('search_path=public' = ANY(p.proconfig)) THEN '✅ آمنة'
            WHEN p.prosecdef AND NOT ('search_path=public' = ANY(p.proconfig)) THEN '⚠️ تحتاج search_path'
            WHEN NOT p.prosecdef THEN '📝 عادية'
            ELSE '❓ غير محددة'
        END::TEXT
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND (p.proname LIKE '%search_analytics%' OR p.proname LIKE '%admin%' OR p.proname LIKE '%access%')
    ORDER BY p.proname;
END;
$$;

-- ===================================================================
-- تسجيل أحداث الأمان
-- ===================================================================

-- جدول سجل أحداث الأمان - محسن مع معلومات إضافية
CREATE TABLE IF NOT EXISTS security_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    table_name VARCHAR(50),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    request_path TEXT,
    request_method VARCHAR(10),
    status_code INTEGER,
    is_suspicious BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول محاولات الوصول المشبوهة
CREATE TABLE IF NOT EXISTS suspicious_access_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    attempt_type VARCHAR(50) NOT NULL,
    attempt_details JSONB,
    blocked BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_security_audit_log_user_id ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_action ON security_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_security_audit_log_created_at ON security_audit_log(created_at);

-- فهارس للجدول الجديد
CREATE INDEX IF NOT EXISTS idx_suspicious_access_ip ON suspicious_access_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_suspicious_access_user_id ON suspicious_access_attempts(user_id);
CREATE INDEX IF NOT EXISTS idx_suspicious_access_created_at ON suspicious_access_attempts(created_at);
CREATE INDEX IF NOT EXISTS idx_suspicious_access_type ON suspicious_access_attempts(attempt_type);

-- دالة للكشف عن محاولات الوصول المشبوهة
CREATE OR REPLACE FUNCTION detect_suspicious_access(
    p_user_id UUID,
    p_ip_address INET,
    p_user_agent TEXT,
    p_resource_type TEXT DEFAULT NULL,
    p_resource_id UUID DEFAULT NULL,
    p_action TEXT DEFAULT 'access'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_is_suspicious BOOLEAN := false;
    v_recent_attempts INTEGER := 0;
    v_different_ips INTEGER := 0;
    v_user_usual_ip INET;
    v_threshold_attempts CONSTANT INTEGER := 10;
    v_threshold_ips CONSTANT INTEGER := 3;
    v_time_window CONSTANT INTERVAL := INTERVAL '1 hour';
BEGIN
    -- التحقق من وجود معرف المستخدم
    IF p_user_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- 1. التحقق من عدد محاولات الوصول الأخيرة من نفس عنوان IP
    SELECT COUNT(*) INTO v_recent_attempts
    FROM security_audit_log
    WHERE user_id = p_user_id
    AND created_at > NOW() - v_time_window;
    
    -- 2. التحقق من عدد عناوين IP المختلفة المستخدمة مؤخرًا
    SELECT COUNT(DISTINCT ip_address) INTO v_different_ips
    FROM security_audit_log
    WHERE user_id = p_user_id
    AND created_at > NOW() - v_time_window;
    
    -- 3. الحصول على عنوان IP المعتاد للمستخدم
    SELECT ip_address INTO v_user_usual_ip
    FROM security_audit_log
    WHERE user_id = p_user_id
    GROUP BY ip_address
    ORDER BY COUNT(*) DESC
    LIMIT 1;
    
    -- تحديد ما إذا كانت المحاولة مشبوهة
    v_is_suspicious := (
        (v_recent_attempts > v_threshold_attempts) OR
        (v_different_ips > v_threshold_ips) OR
        (v_user_usual_ip IS NOT NULL AND v_user_usual_ip != p_ip_address)
    );
    
    -- تسجيل المحاولة المشبوهة
    IF v_is_suspicious THEN
        BEGIN
            INSERT INTO suspicious_access_attempts (
                user_id,
                ip_address,
                user_agent,
                attempt_type,
                attempt_details
            ) VALUES (
                p_user_id,
                p_ip_address,
                p_user_agent,
                p_action,
                jsonb_build_object(
                    'resource_type', p_resource_type,
                    'resource_id', p_resource_id,
                    'recent_attempts', v_recent_attempts,
                    'different_ips', v_different_ips,
                    'usual_ip', v_user_usual_ip
                )
            );
            
            -- تسجيل في سجل الأمان
            INSERT INTO security_audit_log (
                user_id,
                action,
                table_name,
                record_id,
                ip_address,
                user_agent,
                is_suspicious
            ) VALUES (
                p_user_id,
                'suspicious_' || p_action,
                p_resource_type,
                p_resource_id,
                p_ip_address,
                p_user_agent,
                true
            );
        EXCEPTION WHEN OTHERS THEN
            -- تجاهل أي أخطاء في تسجيل الحدث
            RAISE WARNING 'خطأ في تسجيل محاولة وصول مشبوهة: %', SQLERRM;
        END;
    END IF;
    
    RETURN v_is_suspicious;
END;
$$;

-- دالة للتحقق من محاولات تخمين كلمة المرور
CREATE OR REPLACE FUNCTION check_password_guessing(
    p_email TEXT,
    p_ip_address INET,
    p_user_agent TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_failed_attempts INTEGER := 0;
    v_threshold CONSTANT INTEGER := 5;
    v_time_window CONSTANT INTERVAL := INTERVAL '15 minutes';
    v_is_suspicious BOOLEAN := false;
    v_user_id UUID;
BEGIN
    -- محاولة العثور على معرف المستخدم
    BEGIN
        SELECT id INTO v_user_id
        FROM profiles
        WHERE email = p_email;
    EXCEPTION WHEN OTHERS THEN
        v_user_id := NULL;
    END;
    
    -- التحقق من عدد محاولات تسجيل الدخول الفاشلة
    SELECT COUNT(*) INTO v_failed_attempts
    FROM suspicious_access_attempts
    WHERE ip_address = p_ip_address
    AND attempt_type = 'failed_login'
    AND created_at > NOW() - v_time_window;
    
    -- تحديد ما إذا كانت المحاولة مشبوهة
    v_is_suspicious := (v_failed_attempts >= v_threshold);
    
    -- تسجيل المحاولة المشبوهة
    IF v_is_suspicious THEN
        BEGIN
            INSERT INTO suspicious_access_attempts (
                user_id,
                ip_address,
                user_agent,
                attempt_type,
                attempt_details,
                blocked
            ) VALUES (
                v_user_id,
                p_ip_address,
                p_user_agent,
                'password_guessing',
                jsonb_build_object(
                    'email', p_email,
                    'failed_attempts', v_failed_attempts,
                    'time_window_minutes', EXTRACT(EPOCH FROM v_time_window) / 60
                ),
                true
            );
        EXCEPTION WHEN OTHERS THEN
            -- تجاهل أي أخطاء في تسجيل الحدث
            RAISE WARNING 'خطأ في تسجيل محاولة تخمين كلمة المرور: %', SQLERRM;
        END;
    END IF;
    
    RETURN v_is_suspicious;
END;
$$;


