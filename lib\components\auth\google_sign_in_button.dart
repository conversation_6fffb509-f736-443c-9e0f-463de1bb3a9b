import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/screens/auth/complete_google_profile_screen.dart';
import 'package:provider/provider.dart';

class GoogleSignInButton extends StatelessWidget {
  final VoidCallback? onSuccess;
  final VoidCallback? onError;
  final String? customText;
  final bool showIcon;
  final bool isCompact;

  const GoogleSignInButton({
    super.key,
    this.onSuccess,
    this.onError,
    this.customText,
    this.showIcon = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthSupabaseService>(
      builder: (context, authService, child) {
        return Container(
          width: double.infinity,
          height: isCompact ? 45 : 55,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ElevatedButton(
            onPressed:
                (authService.isGoogleSigningIn || authService.isLoading)
                    ? null
                    : () => _handleGoogleSignIn(context, authService),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black87,
              elevation: 2,
              shadowColor: Colors.black26,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child:
                (authService.isGoogleSigningIn || authService.isLoading)
                    ? _buildLoadingWidget()
                    : _buildButtonContent(),
          ),
        );
      },
    );
  }

  Widget _buildButtonContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showIcon) ...[
          Image.asset(
            'assets/images/google_icon.png',
            height: isCompact ? 24 : 30,
            width: isCompact ? 24 : 30,
            errorBuilder: (context, error, stackTrace) {
              // في حالة عدم وجود الصورة، استخدم أيقونة
              return Icon(
                Icons.login,
                size: isCompact ? 20 : 24,
                color: AppTheme.primaryColor,
              );
            },
          ),
          SizedBox(width: isCompact ? 8 : 12),
        ],
        Text(
          customText ?? 'تسجيل الدخول مع Google',
          style: TextStyle(
            fontSize: isCompact ? 14 : 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      child: Row(
        key: const ValueKey('loading'),
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: isCompact ? 18 : 24,
            height: isCompact ? 18 : 24,
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
          ),
          SizedBox(width: isCompact ? 8 : 12),
          Text(
            'جاري تسجيل الدخول...',
            style: TextStyle(
              fontSize: isCompact ? 14 : 16,
              color: Colors.black54,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleGoogleSignIn(
    BuildContext context,
    AuthSupabaseService authService,
  ) async {
    try {
      final success = await authService.signInWithGoogle();

      if (success) {
        debugPrint('✅ تم بدء عملية Google OAuth بنجاح - سيتم إعادة التوجيه...');

        // في بيئة الويب، سيتم إعادة التوجيه تلقائياً إلى Google
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('جاري إعادة التوجيه إلى Google...'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 2),
            ),
          );
        }

        if (onSuccess != null) {
          onSuccess!();
        }
      } else if (success && authService.isAuthenticated) {
        // التحقق من إذا كان المستخدم جديد يحتاج لإكمال البيانات
        final currentUser = authService.currentUser;
        // يمكن تحديد إذا كان المستخدم جديد بناءً على تاريخ الإنشاء أو بيانات أخرى
        final isNewUser = currentUser?.name.isEmpty ?? true;

        if (isNewUser && context.mounted) {
          // توجيه المستخدم الجديد لشاشة إكمال البيانات
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder:
                  (context) => CompleteGoogleProfileScreen(
                    email: authService.googleUser?.email ?? '',
                    name:
                        authService.googleUser?.displayName ?? 'مستخدم Google',
                    userId: currentUser?.id ?? '',
                  ),
            ),
          );
        } else {
          // مستخدم موجود - الانتقال للشاشة الرئيسية
          if (onSuccess != null) {
            onSuccess!();
          } else if (context.mounted) {
            Navigator.of(context).pushReplacementNamed('/home');
          }

          // إظهار رسالة ترحيب للمستخدم الموجود
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'مرحباً بعودتك ${authService.googleUser?.displayName ?? ''}!',
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } else {
        // حدث خطأ أو تم إلغاء العملية
        if (authService.error != null &&
            (authService.error!.contains('network') ||
                authService.error!.contains('cancelled'))) {
          _showErrorSnackBar(
            context,
            'فشل الاتصال أو تم إلغاء العملية. يمكنك إعادة المحاولة أو التواصل مع الدعم.',
            onRetry: () => _handleGoogleSignIn(context, authService),
          );
          return;
        }
        if (onError != null) {
          onError!();
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authService.error!),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      // معالجة الأخطاء غير المتوقعة
      if (onError != null) {
        onError!();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}

/// زر مخصص لتسجيل الخروج من Google
class GoogleSignOutButton extends StatelessWidget {
  final VoidCallback? onSuccess;
  final String? customText;
  final bool isCompact;

  const GoogleSignOutButton({
    super.key,
    this.onSuccess,
    this.customText,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthSupabaseService>(
      builder: (context, authService, child) {
        if (!authService.isGoogleSignedIn) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          height: isCompact ? 45 : 55,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ElevatedButton(
            onPressed: () => _handleSignOut(context, authService),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade50,
              foregroundColor: Colors.red.shade700,
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.red.shade200, width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.logout, size: isCompact ? 20 : 24),
                SizedBox(width: isCompact ? 8 : 12),
                Text(
                  customText ?? 'تسجيل الخروج من Google',
                  style: TextStyle(
                    fontSize: isCompact ? 14 : 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleSignOut(
    BuildContext context,
    AuthSupabaseService authService,
  ) async {
    try {
      await authService.signOutFromGoogle();

      if (onSuccess != null) {
        onSuccess!();
      } else if (context.mounted) {
        // الانتقال إلى شاشة تسجيل الدخول
        Navigator.of(context).pushReplacementNamed('/login');
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الخروج من Google بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تسجيل الخروج: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}

// 1. عند نجاح تسجيل الدخول، إظهار رسالة ترحيب متحركة باسم المستخدم

// 2. عند الفشل، إظهار SnackBar مع أيقونة وزر إعادة المحاولة
void _showErrorSnackBar(
  BuildContext context,
  String message, {
  VoidCallback? onRetry,
}) {
  final snackBar = SnackBar(
    content: Row(
      children: [
        const Icon(Icons.error_outline, color: Colors.white),
        const SizedBox(width: 8),
        Expanded(child: Text(message)),
        if (onRetry != null)
          TextButton(
            onPressed: onRetry,
            child: const Text(
              'إعادة المحاولة',
              style: TextStyle(color: Colors.white),
            ),
          ),
      ],
    ),
    backgroundColor: Colors.red,
    behavior: SnackBarBehavior.floating,
    duration: const Duration(seconds: 4),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
    margin: const EdgeInsets.all(16),
  );
  ScaffoldMessenger.of(context).showSnackBar(snackBar);
}
