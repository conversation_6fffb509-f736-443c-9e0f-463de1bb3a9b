import 'package:flutter/foundation.dart';

/// خدمة تحسين الاعتماديات وإزالة غير المستخدم
class DependencyOptimizer {
  /// قائمة الحزم المستخدمة فعلياً
  static const List<String> _usedPackages = [
    // الحزم الأساسية
    'flutter',
    'flutter_localizations',
    'cupertino_icons',

    // قاعدة البيانات والشبكة
    'supabase_flutter',
    'supabase',
    'http',
    'postgrest',

    // إدارة الحالة
    'provider',
    'flutter_riverpod',

    // التخزين
    'shared_preferences',
    'flutter_secure_storage',
    'sqflite',
    'path_provider',
    'path',

    // الواجهة والرسوم المتحركة
    'cached_network_image',
    'shimmer',
    'flutter_staggered_animations',
    'flutter_staggered_grid_view',
    'carousel_slider',
    'lottie',
    'confetti',

    // الأدوات المساعدة
    'connectivity_plus',
    'device_info_plus',
    'package_info_plus',
    'geolocator',
    'url_launcher',
    'share_plus',
    'intl',
    'uuid',
    'logger',

    // الأمان والتشفير
    'crypto',
    'encrypt',

    // الإشعارات
    'flutter_local_notifications',

    // التحقق والتحليل
    'email_validator',
    'vibration',

    // الملفات والوسائط
    'image_picker',
    'file_picker',
    'image',
    'flutter_svg',
    'camera',

    // الطباعة والتصدير
    'printing',
    'pdf',
    'open_file',

    // الرسوم البيانية
    'fl_chart',
    'data_table_2',

    // التكامل مع الخدمات الخارجية
    'google_sign_in',
    'cloudinary',
    'google_generative_ai',
    'twilio_flutter',

    // الميزات المتقدمة
    'speech_to_text',
    'pin_code_fields',
    'quick_actions',
    'font_awesome_flutter',

    // البيئة والإعدادات
    'flutter_dotenv',
    'flutter_cache_manager',

    // التطوير
    'json_annotation',
    'json_serializable',
  ];

  /// قائمة الحزم غير المستخدمة (يمكن إزالتها)
  static const List<String> _unusedPackages = [
    // هذه الحزم قد تكون غير مستخدمة - يجب التحقق
    // 'some_unused_package',
  ];

  /// قائمة الحزم المكررة أو التي لها بدائل أفضل
  static const Map<String, String> _duplicatePackages = {
    'provider': 'flutter_riverpod', // يفضل استخدام Riverpod
    'supabase': 'supabase_flutter', // supabase_flutter يتضمن supabase
  };

  /// تحليل الاعتماديات
  static Map<String, dynamic> analyzeDependencies() {
    return {
      'total_packages': _usedPackages.length,
      'used_packages': _usedPackages,
      'unused_packages': _unusedPackages,
      'duplicate_packages': _duplicatePackages,
      'recommendations': _getRecommendations(),
    };
  }

  /// الحصول على التوصيات
  static List<String> _getRecommendations() {
    return [
      'يفضل استخدام flutter_riverpod بدلاً من provider لإدارة الحالة',
      'يمكن إزالة حزمة supabase والاعتماد على supabase_flutter فقط',
      'تحديث جميع الحزم لأحدث إصدار مستقر',
      'إزالة الحزم غير المستخدمة لتقليل حجم التطبيق',
      'استخدام lazy loading للحزم الثقيلة',
    ];
  }

  /// تحسين pubspec.yaml
  static String generateOptimizedPubspec() {
    return '''
name: motorcycle_parts_shop
description: "تطبيق متجر قطع غيار الدراجات النارية"
publish_to: 'none'

version: 1.2.0+2

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2

  # قاعدة البيانات والشبكة
  supabase_flutter: ^2.8.3
  http: ^1.2.2

  # إدارة الحالة (محسن)
  flutter_riverpod: ^2.6.1
  provider: ^6.1.2  # سيتم إزالته تدريجياً

  # التخزين والأمان
  shared_preferences: ^2.3.5
  flutter_secure_storage: ^9.0.0
  sqflite: ^2.4.1
  path_provider: ^2.1.5
  path: ^1.9.0
  crypto: ^3.0.3
  encrypt: ^5.0.3

  # الواجهة والرسوم المتحركة
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.1.1
  flutter_staggered_grid_view: ^0.7.0
  carousel_slider: ^5.1.1
  lottie: ^3.3.1
  confetti: ^0.8.0

  # الأدوات المساعدة
  connectivity_plus: ^6.0.0
  device_info_plus: ^11.4.0
  package_info_plus: ^8.2.0
  geolocator: ^14.0.1
  url_launcher: ^6.3.1
  share_plus: ^11.0.0
  intl: ^0.19.0
  uuid: ^4.5.1
  logger: ^2.5.0
  email_validator: ^3.0.0
  vibration: ^3.1.3

  # الملفات والوسائط
  image_picker: ^1.1.2
  file_picker: ^10.1.9
  image: ^4.1.7
  flutter_svg: ^2.0.17
  camera: ^0.11.1

  # الإشعارات
  flutter_local_notifications: ^19.2.1

  # الطباعة والتصدير
  printing: ^5.12.0
  pdf: ^3.10.8
  open_file: ^3.3.1

  # الرسوم البيانية
  fl_chart: ^1.0.0
  data_table_2: ^2.5.11

  # التكامل مع الخدمات الخارجية
  google_sign_in: ^6.2.1
  cloudinary: ^1.2.0
  google_generative_ai: ^0.4.6
  twilio_flutter: ^0.9.0

  # الميزات المتقدمة
  speech_to_text: ^7.0.0
  pin_code_fields: ^8.0.1
  quick_actions: ^1.1.0
  font_awesome_flutter: ^10.8.0

  # البيئة والإعدادات
  flutter_dotenv: ^5.2.1
  flutter_cache_manager: ^3.4.1

  # التطوير
  json_annotation: ^4.9.0
  json_serializable: ^6.9.5

dev_dependencies:
  flutter_lints: ^5.0.0
  build_runner: ^2.4.11
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - .env

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
''';
  }

  /// طباعة تقرير التحليل
  static void printAnalysisReport() {
    if (kDebugMode) {
      final analysis = analyzeDependencies();

      print('📦 تقرير تحليل الاعتماديات:');
      print('إجمالي الحزم: ${analysis['total_packages']}');
      print('الحزم غير المستخدمة: ${analysis['unused_packages'].length}');
      print('الحزم المكررة: ${analysis['duplicate_packages'].length}');

      print('\n💡 التوصيات:');
      for (final recommendation in analysis['recommendations']) {
        print('- $recommendation');
      }
    }
  }
}
