import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:motorcycle_parts_shop/core/analytics/unified_analytics.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/user_interaction_service.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

class ChatbotService extends ChangeNotifier {
  final AuthSupabaseService _supabaseService;
  final UserInteractionService _userInteractionService;
  final UnifiedAnalyticsService _analyticsService;
   final SpeechToText _speechToText = SpeechToText(); 
   late GenerativeModel _geminiModel; 
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isGeminiInitialized = false;

  /// تهيئة خدمة التعرف على الصوت
  Future<bool> initializeSpeechToText() async {
    try {
      _isInitialized = await _speechToText.initialize();

      // تسجيل نجاح أو فشل تهيئة خدمة التعرف على الصوت
      _analyticsService.logCustomEvent('speech_to_text_initialization', {
        'success': _isInitialized,
      });

      return _isInitialized;
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة التعرف على الصوت: $e');

      // تسجيل الخطأ
      _analyticsService.logCustomEvent('speech_to_text_error', {
        'error': e.toString(),
      });

      return false;
    }
  }

  // تخزين مؤقت للتوصيات الشخصية
  final Map<String, List<Map<String, dynamic>>>
  _personalizedRecommendationsCache = {};

  ChatbotService(
    this._supabaseService,
    this._userInteractionService,
    this._analyticsService,
  ) {
    _initializeGemini();
    // تنظيف الذاكرة المؤقتة كل 30 دقيقة
    Future.delayed(const Duration(minutes: 30), () {
      _clearRecommendationsCache();
    });
  }

  /// تنظيف الذاكرة المؤقتة للتوصيات
  void _clearRecommendationsCache() {
    _personalizedRecommendationsCache.clear();
    // إعادة جدولة التنظيف
    Future.delayed(const Duration(minutes: 30), () {
      _clearRecommendationsCache();
    });
  }

  /// تهيئة Gemini AI
  Future<void> _initializeGemini() async {
    try {
      final apiKey = dotenv.env['GEMINI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        debugPrint('Gemini API Key not found in environment variables');
        return;
      }

      _geminiModel = GenerativeModel(
        model: 'gemini-1.5-flash',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        ),
        systemInstruction: Content.system(_getSystemPrompt()),
      );

      _isGeminiInitialized = true;
      debugPrint('Gemini AI initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize Gemini AI: $e');
      _isGeminiInitialized = false;
    }
  }

  /// إنشاء النص التوجيهي للنظام
  String _getSystemPrompt() {
    return '''
أنت مساعد ذكي لمتجر قطع غيار الدراجات النارية. اسمك "مساعد المتجر الذكي".

مهامك:
1. الإجابة على أسئلة العملاء حول المنتجات والخدمات
2. مساعدة العملاء في العثور على قطع الغيار المناسبة
3. تقديم معلومات عن الطلبات والشحن
4. تقديم الدعم الفني والاستشارات

قواعد مهمة:
- أجب باللغة العربية دائماً
- كن مهذباً ومفيداً
- إذا لم تعرف الإجابة، اطلب من العميل التواصل مع خدمة العملاء
- استخدم المعلومات من قاعدة البيانات عند توفرها
- قدم اقتراحات مفيدة للعملاء

أسلوب الرد:
- استخدم عبارات ترحيبية ودودة
- قدم إجابات واضحة ومفصلة
- اقترح منتجات أو خدمات ذات صلة عند الإمكان
''';
  }

  // 1. تخصيص الردود باسم المستخدم
  String _getUserName() {
    return _supabaseService.currentUser?.name ?? "عميلنا الكريم";
  }

  // 2. اقتراحات ذكية بعد كل إجابة
  String _buildSuggestions() {
    final suggestions = [
      "ما هي طرق التوصيل المتاحة؟",
      "كيف أتتبع طلبي؟",
      "أرغب في التواصل مع الدعم الفني",
      "ما هي العروض الحالية؟",
      "منتجاتنا الأكثر مبيعاً",
    ];
    return '\n\n💡 اقتراحات:\n${suggestions.map((q) => '• $q').join('\n')}';
  }

  // 3. تتبع الطلبات مباشرة
  Future<String?> _handleOrderTracking() async {
    var orders = await _getUserOrdersData();
    if (orders.isNotEmpty) {
      final order = orders.first;
      return "آخر طلب لك رقم ${order['id']} حالته: ${order['status']} (بتاريخ ${order['created_at']})";
    } else {
      return "لا يوجد طلبات مسجلة على حسابك.";
    }
  }

  // 4. إضافة المنتجات للسلة بنقرة واحدة (رد نصي توضيحي)
  String _buildAddToCartSuggestion(String productName, String productId) {
    return '\nهل ترغب في إضافة المنتج "$productName" للسلة؟ اضغط هنا: [إضافة للسلة](app://add_to_cart/$productId)';
  }

  // 5. إشعارات فورية بالعروض
  Future<String> _getCurrentOffers() async {
    try {
      final response = await _supabaseService.client
          .from('offers')
          .select('title, description')
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .limit(1);
      if (response.isNotEmpty) {
        final offer = response.first;
        return '🎉 عرض خاص: ${offer['title']} - ${offer['description']}';
      }
      return '';
    } catch (e) {
      return '';
    }
  }

  // 6. تحليل نية المستخدم (Intent Detection)
  String _detectIntent(String message) {
    final lower = message.toLowerCase();
    if (lower.contains('تتبع طلبي')) return 'order_tracking';
    if (lower.contains('شراء') || lower.contains('أرغب في شراء')) return 'buy';
    if (lower.contains('شكوى') || lower.contains('مشكلة')) return 'complaint';
    if (lower.contains('إضافة للسلة')) return 'add_to_cart';
    if (lower.contains('عرض') || lower.contains('خصم')) return 'offer';
    if (lower.contains('تحدث مع موظف')) return 'human_support';
    return 'general';
  }

  // 7. توصيات مخصصة بناءً على سجل الشراء
  Future<String> _getPersonalizedRecommendationsText() async {
    try {
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) return '';
      final response = await _supabaseService.client
          .from('orders')
          .select('order_items')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(5);
      final productIds = <String>{};
      for (final order in response) {
        if (order['order_items'] is List) {
          for (final item in order['order_items']) {
            productIds.add(item['product_id']);
          }
        }
      }
      if (productIds.isEmpty) return '';
      final recs = await _supabaseService.client
          .from('products')
          .select('name')
          .inFilter('id', productIds.toList())
          .limit(3);
      if (recs.isNotEmpty) {
        return '\nمنتجات قد تعجبك: ${recs.map((p) => p['name']).join(', ')}';
      }
      return '';
    } catch (e) {
      return '';
    }
  }

  // تم حذف نظام تقييم الخدمة حسب المتطلبات

  // 9. إدارة الجلسة واستئناف المحادثة (يتم عبر getChatHistory/saveChatHistory)
  // 10. دعم الصور والملفات (يتم عبر واجهة المستخدم، ويمكن إضافة رسالة توضيحية)
  String _buildImageSupportMessage() {
    return '\nيمكنك إرسال صورة قطعة الغيار وسأساعدك في التعرف عليها!';
  }

  /// بدء الاستماع للصوت
  Future<bool> startListening() async {
    if (!_isInitialized) {
      await initializeSpeechToText();
    }

    if (_isInitialized && !_isListening) {
      try {
        _isListening = await _speechToText.listen(
          onResult: (result) {
            if (result.finalResult) {
              // تسجيل نتيجة التعرف على الصوت
              _analyticsService.logCustomEvent('speech_recognition_result', {
                'text': result.recognizedWords,
                'confidence': result.confidence,
              });

              _userInteractionService.updateVoiceInput(result.recognizedWords);
            }
          },
          localeId: 'ar_SA', // اللغة العربية - السعودية
        );

        // تسجيل بدء الاستماع
        _analyticsService.trackUserEvent('voice_input_started');

        notifyListeners();
        return _isListening;
      } catch (e) {
        debugPrint('خطأ في بدء الاستماع: $e');

        // تسجيل الخطأ
        _analyticsService.logCustomEvent('speech_recognition_error', {
          'error': e.toString(),
          'action': 'start_listening',
        });

        _isListening = false;
        notifyListeners();
        return false;
      }
    }
    return false;
  }

  /// إيقاف الاستماع للصوت
  Future<void> stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      _isListening = false;

      // تسجيل إيقاف الاستماع
      _analyticsService.trackUserEvent('voice_input_stopped');

      notifyListeners();
    }
  }

  // 11. تسريع الإجابات بالتخزين المؤقت (cache)
  final Map<String, String> _faqCache = {};

  // 12. زر "تحدث مع موظف حقيقي"
  String _buildHumanSupportButton() {
    return '\n🔴 لم تجد ما تبحث عنه؟ [تحدث مع موظف حقيقي](app://support_chat)';
  }

  // تعديل getChatbotResponse ليشمل كل الميزات أعلاه
  Future<String> getChatbotResponse(String message) async {
    try {
      // تخصيص الرد باسم المستخدم
      final userName = _getUserName();
      final intent = _detectIntent(message);

      // تسجيل تفاعل المستخدم مع الشات بوت
      _analyticsService.logCustomEvent('chatbot_interaction', {
        'message': message,
        'intent': intent,
        'user_name': userName,
      });

      // تسريع الإجابات للأسئلة الشائعة
      if (_faqCache.containsKey(message)) {
        // تسجيل استخدام الكاش
        _analyticsService.logCustomEvent('chatbot_cache_hit', {
          'message': message,
          'intent': intent,
        });
        return _faqCache[message]!;
      }

      // تتبع الطلبات مباشرة
      if (intent == 'order_tracking') {
        final tracking = await _handleOrderTracking();
        // تم حذف طلب التقييم حسب المتطلبات
        final suggestions = _buildSuggestions();
        final support = _buildHumanSupportButton();

        // تسجيل طلب تتبع الطلبات
        _analyticsService.trackUserEvent(
          'order_tracking_request',
          properties: {'user_name': userName},
        );

        return 'مرحباً $userName!\n$tracking$support$suggestions';
      }

      // إشعارات العروض
      String offers = '';
      if (intent == 'offer' || intent == 'general') {
        offers = await _getCurrentOffers();

        if (offers.isNotEmpty) {
          // تسجيل عرض العروض للمستخدم
          _analyticsService.logCustomEvent('offers_shown', {'intent': intent});
        }
      }

      // الرد الافتراضي (مع الذكاء الاصطناعي)
      if (!_isGeminiInitialized) {
        await _initializeGemini();
        if (!_isGeminiInitialized) {
          // تسجيل فشل تهيئة الذكاء الاصطناعي
          _analyticsService.logCustomEvent('gemini_initialization_failed', {});
          return 'عذراً، خدمة المساعد الذكي غير متاحة حالياً. يرجى المحاولة لاحقاً.';
        }
      }

      final contextData = await _getContextData(message);
      final enhancedMessage = await _buildEnhancedMessage(message, contextData);
      final content = [Content.text(enhancedMessage)];

      // تسجيل وقت بدء طلب الذكاء الاصطناعي
      final startTime = DateTime.now();
      final response = await _geminiModel.generateContent(content);
      // حساب وقت الاستجابة
      final responseTime = DateTime.now().difference(startTime).inMilliseconds;

      // تسجيل استجابة الذكاء الاصطناعي
      _analyticsService.logCustomEvent('gemini_response', {
        'response_time_ms': responseTime,
        'success': response.text != null,
      });

      String reply =
          response.text ??
          'عذراً، لم أتمكن من فهم طلبك. هل يمكنك إعادة صياغته؟';

      // إضافة اقتراحات ذكية وتوصيات مخصصة
      final suggestions = _buildSuggestions();
      final recommendations = await _getPersonalizedRecommendationsText();
      // تم حذف طلب التقييم حسب المتطلبات
      final imageSupport = _buildImageSupportMessage();
      final support = _buildHumanSupportButton();

      // إضافة زر إضافة للسلة إذا كان الرد عن منتج
      if (intent == 'buy' &&
          contextData['products'] != null &&
          (contextData['products'] as List).isNotEmpty) {
        final product = (contextData['products'] as List).first;
        reply += _buildAddToCartSuggestion(product['name'], product['id']);

        // تسجيل اقتراح منتج للمستخدم
        _analyticsService.logProductView(
          product['id'],
          properties: {'product_name': product['name']},
        );
      }

      // إضافة إشعار العروض إذا وجد
      if (offers.isNotEmpty) {
        reply += '\n$offers';
      }

      // إضافة التوصيات
      if (recommendations.isNotEmpty) {
        reply += '\n$recommendations';

        // تسجيل عرض توصيات شخصية
        _analyticsService.logCustomEvent('personalized_recommendations_shown', {
          'user_name': userName,
        });
      }

      // إضافة دعم الصور
      reply += imageSupport;

      // إضافة زر التواصل مع موظف حقيقي
      reply += support;

      // إضافة الاقتراحات الذكية
      reply += suggestions;

      // تم حذف إضافة تقييم الخدمة حسب المتطلبات

      // تخصيص الرد باسم المستخدم
      reply = 'مرحباً $userName!\n$reply';

      // تخزين الإجابة في الكاش للأسئلة الشائعة
      if (intent == 'general' || intent == 'order_tracking') {
        _faqCache[message] = reply;
      }

      // تسجيل اكتمال الرد
      _analyticsService.logCustomEvent('chatbot_response_complete', {
        'intent': intent,
        'response_length': reply.length,
      });

      return reply;
    } catch (e) {
      // تسجيل الخطأ
      _analyticsService.logCustomEvent('chatbot_error', {
        'error': e.toString(),
        'message': message,
      });

      debugPrint('Chatbot error: $e');
      return 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة لاحقاً.';
    }
  }

  /// جمع البيانات السياقية من قاعدة البيانات
  Future<Map<String, dynamic>> _getContextData(String message) async {
    final contextData = <String, dynamic>{};

    try {
      // تحليل الرسالة لتحديد نوع الاستفسار
      final messageType = _analyzeMessageType(message);

      switch (messageType) {
        case 'product_inquiry':
          contextData['products'] = await _getProductsData(message);
          contextData['categories'] = await _getCategoriesData();
          break;
        case 'order_inquiry':
          if (_supabaseService.isAuthenticated) {
            contextData['orders'] = await _getUserOrdersData();
          }
          break;
        case 'general_inquiry':
          contextData['featured_products'] = await _getFeaturedProducts();
          contextData['categories'] = await _getCategoriesData();
          break;
      }

      // إضافة معلومات عامة عن المتجر
      contextData['store_info'] = await _getStoreInfo();
    } catch (e) {
      debugPrint('Error getting context data: $e');
    }

    return contextData;
  }

  /// تحليل نوع الرسالة
  String _analyzeMessageType(String message) {
    final lowerMessage = message.toLowerCase();

    // كلمات مفتاحية للمنتجات
    final productKeywords = ['منتج', 'قطعة', 'غيار', 'سعر', 'متوفر', 'مواصفات'];

    // كلمات مفتاحية للطلبات
    final orderKeywords = ['طلب', 'شحن', 'توصيل', 'فاتورة'];

    if (productKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'product_inquiry';
    } else if (orderKeywords.any((keyword) => lowerMessage.contains(keyword))) {
      return 'order_inquiry';
    } else {
      return 'general_inquiry';
    }
  }

  /// بناء الرسالة المحسنة مع السياق والتوصيات الشخصية
  Future<String> _buildEnhancedMessage(
    String originalMessage,
    Map<String, dynamic> contextData,
  ) async {
    final buffer = StringBuffer();

    buffer.writeln(
      'أنت مساعد ذكي لمتجر قطع غيار الدراجات النارية. '
      'مهمتك هي مساعدة العملاء في العثور على المنتجات المناسبة والإجابة على استفساراتهم. '
      'يجب أن تكون إجاباتك مختصرة ومفيدة ودقيقة. '
      'استخدم اللغة العربية الفصحى البسيطة في إجاباتك. '
      'قدم توصيات شخصية بناءً على اهتمامات العميل وتفضيلاته.',
    );

    buffer.writeln('\nرسالة العميل: $originalMessage');
    buffer.writeln('\nمعلومات السياق:');

    // معلومات المستخدم وتفضيلاته
    final user = _supabaseService.currentUser;
    bool isLoggedIn = user != null;

    if (isLoggedIn) {
      buffer.writeln('\nمعلومات المستخدم:');
      buffer.writeln('- البريد الإلكتروني: ${user.email}');
      buffer.writeln(
        '- الاسم: ${user.rawUserMetaData?['full_name'] ?? user.name}',
      );

      // إضافة تفضيلات المستخدم
      final userPreferences = await _getUserPreferences(user.id);
      if (userPreferences.isNotEmpty) {
        buffer.writeln('\nتفضيلات المستخدم:');
        userPreferences.forEach((key, value) {
          buffer.writeln('- $key: $value');
        });
      }
    }

    // إضافة معلومات المنتجات
    if (contextData['products'] != null) {
      final products = contextData['products'] as List;
      if (products.isNotEmpty) {
        buffer.writeln('\nالمنتجات ذات الصلة:');
        for (final product in products.take(5)) {
          buffer.writeln('- اسم المنتج: ${product['name']}');
          if (product['description'] != null) {
            buffer.writeln('  الوصف: ${product['description']}');
          }
          buffer.writeln('  السعر: ${product['price']} ريال');
          buffer.writeln('  الكمية المتاحة: ${product['stock_quantity'] ?? 0}');
          buffer.writeln(
            '  العلامة التجارية: ${product['brand'] ?? 'غير محدد'}',
          );
          buffer.writeln(
            '  معرف المنتج: ${product['id']}',
          ); // إضافة معرف المنتج للإشارة إليه في الرد
          buffer.writeln('');
        }

        // إضافة توصيات شخصية إذا كان المستخدم مسجل الدخول
        if (isLoggedIn) {
          final personalizedProducts = await _getPersonalizedRecommendations(
            user.id,
          );
          if (personalizedProducts.isNotEmpty) {
            buffer.writeln('\nتوصيات شخصية للعميل:');
            for (final product in personalizedProducts.take(3)) {
              buffer.writeln('- اسم المنتج: ${product['name']}');
              if (product['description'] != null) {
                buffer.writeln('  الوصف: ${product['description']}');
              }
              buffer.writeln('  السعر: ${product['price']} ريال');
              buffer.writeln(
                '  العلامة التجارية: ${product['brand'] ?? 'غير محدد'}',
              );
              buffer.writeln('  معرف المنتج: ${product['id']}');
              buffer.writeln('');
            }
          }
        }
      }
    }

    // إضافة معلومات الفئات
    if (contextData['categories'] != null) {
      final categories = contextData['categories'] as List;
      if (categories.isNotEmpty) {
        buffer.writeln('\nالفئات المتاحة:');
        for (final category in categories) {
          buffer.writeln('- ${category['name']}');
        }
      }
    }

    // إضافة معلومات الطلبات
    if (contextData['orders'] != null) {
      final orders = contextData['orders'] as List;
      if (orders.isNotEmpty) {
        buffer.writeln('\nطلبات العميل الأخيرة:');
        for (final order in orders.take(3)) {
          buffer.writeln('- طلب رقم: ${order['id']}');
          buffer.writeln('  الحالة: ${order['status']}');
          buffer.writeln('  التاريخ: ${order['created_at']}');
        }
      }
    }

    // إضافة معلومات المتجر
    if (contextData['store_info'] != null) {
      final storeInfo = contextData['store_info'] as Map<String, dynamic>;
      buffer.writeln('\nمعلومات المتجر:');
      buffer.writeln(
        '- اسم المتجر: ${storeInfo['name'] ?? 'متجر قطع غيار الدراجات النارية'}',
      );
      buffer.writeln(
        '- ساعات العمل: ${storeInfo['hours'] ?? 'من 9 صباحاً إلى 9 مساءً'}',
      );
      buffer.writeln('- رقم الهاتف: ${storeInfo['phone'] ?? '01234567890'}');
    }

    // إضافة توجيهات للمساعد الذكي حول كيفية تقديم التوصيات
    buffer.writeln('\nتوجيهات لتقديم التوصيات:');
    buffer.writeln('1. قدم توصيات محددة بناءً على استفسار العميل واهتماماته.');
    buffer.writeln('2. اذكر معرفات المنتجات المحددة في توصياتك.');
    buffer.writeln(
      '3. اشرح سبب توصيتك بكل منتج (مثلاً: لأنه يناسب احتياجاتك، أو لأنه من نفس العلامة التجارية التي تفضلها).',
    );
    buffer.writeln(
      '4. إذا كان العميل مسجل الدخول، استخدم تفضيلاته وتاريخ مشترياته لتخصيص التوصيات.',
    );
    buffer.writeln(
      '5. إذا كان العميل يبحث عن منتج غير متوفر، اقترح بدائل مناسبة.',
    );

    buffer.writeln(
      '\nيرجى الإجابة على استفسار العميل بناءً على المعلومات المتاحة أعلاه. قدم توصيات شخصية ومفيدة.',
    );

    return buffer.toString();
  }

  /// الحصول على تفضيلات المستخدم
  Future<Map<String, String>> _getUserPreferences(String userId) async {
    try {
      final Map<String, String> preferences = {};

      // الحصول على العلامات التجارية المفضلة
      final brandInteractions = await _supabaseService.client
          .from('user_interactions')
          .select('products(brand)')
          .eq('user_id', userId)
          .inFilter('interaction_type', [
            'view',
            'add_to_wishlist',
            'add_to_cart',
          ])
          .order('created_at', ascending: false)
          .limit(20);

      final brandCounts = <String, int>{};
      for (final interaction in brandInteractions) {
        if (interaction['products'] != null &&
            interaction['products']['brand'] != null) {
          final brand = interaction['products']['brand'];
          brandCounts[brand] = (brandCounts[brand] ?? 0) + 1;
        }
      }

      // الحصول على الفئات المفضلة
      final categoryInteractions = await _supabaseService.client
          .from('user_interactions')
          .select('products(category_id, categories(name))')
          .eq('user_id', userId)
          .inFilter('interaction_type', [
            'view',
            'add_to_wishlist',
            'add_to_cart',
          ])
          .order('created_at', ascending: false)
          .limit(20);

      final categoryCounts = <String, int>{};
      for (final interaction in categoryInteractions) {
        if (interaction['products'] != null &&
            interaction['products']['categories'] != null &&
            interaction['products']['categories']['name'] != null) {
          final categoryName = interaction['products']['categories']['name'];
          categoryCounts[categoryName] =
              (categoryCounts[categoryName] ?? 0) + 1;
        }
      }

      // إضافة العلامات التجارية المفضلة
      if (brandCounts.isNotEmpty) {
        final brandsList = brandCounts.entries.toList();
        brandsList.sort(
          (a, b) => b.value.compareTo(a.value),
        ); // Sort in descending order
        final favoritesBrands = brandsList.take(3).map((e) => e.key).join('، ');
        preferences['العلامات التجارية المفضلة'] = favoritesBrands;
      }

      // إضافة الفئات المفضلة
      if (categoryCounts.isNotEmpty) {
        final categoriesList = categoryCounts.entries.toList();
        categoriesList.sort(
          (a, b) => b.value.compareTo(a.value),
        ); // Sort in descending order
        final favoritesCategories = categoriesList
            .take(3)
            .map((e) => e.key)
            .join('، ');
        preferences['الفئات المفضلة'] = favoritesCategories;
      }

      // إضافة نطاق السعر المفضل
      final priceInteractions = await _supabaseService.client
          .from('user_interactions')
          .select('products(price)')
          .eq('user_id', userId)
          .inFilter('interaction_type', [
            'view',
            'add_to_wishlist',
            'add_to_cart',
            'purchase',
          ])
          .order('created_at', ascending: false)
          .limit(20);

      final prices = <double>[];
      for (final interaction in priceInteractions) {
        if (interaction['products'] != null &&
            interaction['products']['price'] != null) {
          prices.add(double.parse(interaction['products']['price'].toString()));
        }
      }

      if (prices.isNotEmpty) {
        final avgPrice = prices.reduce((a, b) => a + b) / prices.length;
        preferences['متوسط السعر المفضل'] =
            '${avgPrice.toStringAsFixed(2)} ريال';
      }

      return preferences;
    } catch (e) {
      debugPrint('Error getting user preferences: $e');
      return {};
    }
  }

  /// جلب بيانات المنتجات ذات الصلة مع توصيات شخصية
  Future<List<Map<String, dynamic>>> _getProductsData(String message) async {
    try {
      final userId = _supabaseService.currentUser?.id;

      // البحث في المنتجات بناءً على الكلمات المفتاحية
      final searchTerms = _extractSearchTerms(message);

      // تسجيل البحث في تفاعلات المستخدم إذا كان مسجل الدخول
      if (userId != null && searchTerms.isNotEmpty) {
        await _userInteractionService.recordSearch(
          userId: userId,
          searchQuery: searchTerms.join(' '),
        );
      }

      if (searchTerms.isEmpty) {
        // إذا لم توجد كلمات بحث، أرجع توصيات شخصية أو المنتجات المميزة
        if (userId != null) {
          final personalizedProducts = await _getPersonalizedRecommendations(
            userId,
          );
          if (personalizedProducts.isNotEmpty) {
            return personalizedProducts;
          }
        }
        return await _getFeaturedProducts();
      }

      // البحث عن المنتجات المطابقة للكلمات المفتاحية
      final query = _supabaseService.client
          .from('products')
          .select('''
            id, name, description, price, stock_quantity, brand,
            category_id, is_featured
          ''')
          .or(
            searchTerms
                .map(
                  (term) =>
                      'name.ilike.%$term%,description.ilike.%$term%,brand.ilike.%$term%',
                )
                .join(','),
          )
          .eq('is_active', true)
          .limit(10);

      final response = await query;
      final products = List<Map<String, dynamic>>.from(response);

      // إذا كانت النتائج قليلة، أضف توصيات شخصية
      if (products.length < 5 && userId != null) {
        final personalizedProducts = await _getPersonalizedRecommendations(
          userId,
        );

        // إضافة المنتجات الشخصية التي لم تظهر في نتائج البحث
        final existingIds = products.map((p) => p['id']).toSet();
        for (final product in personalizedProducts) {
          if (!existingIds.contains(product['id']) && products.length < 10) {
            products.add(product);
            existingIds.add(product['id']);
          }
        }
      }

      return products;
    } catch (e) {
      debugPrint('Error getting products data: $e');
      return [];
    }
  }

  /// استخراج كلمات البحث من الرسالة
  List<String> _extractSearchTerms(String message) {
    // إزالة كلمات الوصل والحروف الشائعة
    final stopWords = [
      'في',
      'من',
      'إلى',
      'على',
      'عن',
      'مع',
      'هل',
      'ما',
      'كيف',
      'أين',
    ];

    final words =
        message
            .split(' ')
            .where((word) => word.length > 2 && !stopWords.contains(word))
            .toList();

    return words;
  }

  /// جلب بيانات الفئات
  Future<List<Map<String, dynamic>>> _getCategoriesData() async {
    try {
      final response = await _supabaseService.client
          .from('categories')
          .select('id, name, description')
          .eq('is_active', true)
          .order('name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting categories data: $e');
      return [];
    }
  }

  /// جلب المنتجات المميزة
  Future<List<Map<String, dynamic>>> _getFeaturedProducts() async {
    try {
      final response = await _supabaseService.client
          .from('products')
          .select('''
            id, name, description, price, stock_quantity, brand,
            category_id
          ''')
          .eq('is_featured', true)
          .eq('is_active', true)
          .limit(5);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting featured products: $e');
      return [];
    }
  }

  /// الحصول على توصيات شخصية للمستخدم
  Future<List<Map<String, dynamic>>> _getPersonalizedRecommendations(
    String userId,
  ) async {
    try {
      // التحقق من وجود توصيات في الذاكرة المؤقتة
      if (_personalizedRecommendationsCache.containsKey(userId)) {
        return _personalizedRecommendationsCache[userId]!;
      }

      // الحصول على المنتجات الأكثر مشاهدة للمستخدم
      final mostViewedProductIds = await _userInteractionService
          .getMostViewedProducts(userId: userId, limit: 5);

      // الحصول على الفئات المفضلة للمستخدم
      final favoriteCategories = await _getUserFavoriteCategories(userId);

      // إذا لم تكن هناك منتجات مشاهدة أو فئات مفضلة، أرجع قائمة فارغة
      if (mostViewedProductIds.isEmpty && favoriteCategories.isEmpty) {
        return [];
      }

      // بناء استعلام للحصول على منتجات مشابهة
      var query = _supabaseService.client
          .from('products')
          .select('''
            id, name, description, price, stock_quantity, brand,
            category_id
          ''')
          .eq('is_active', true);

      // إضافة شرط الفئات المفضلة إذا وجدت
      if (favoriteCategories.isNotEmpty) {
        query = query.inFilter('category_id', favoriteCategories);
      }

      // استبعاد المنتجات التي تمت مشاهدتها بالفعل
      if (mostViewedProductIds.isNotEmpty) {
        query = query.not('id', 'in', '(${mostViewedProductIds.join(',')})');
      }

      // الحصول على المنتجات
      final response = await query.limit(5);
      final recommendations = List<Map<String, dynamic>>.from(response);

      // تخزين التوصيات في الذاكرة المؤقتة
      _personalizedRecommendationsCache[userId] = recommendations;

      return recommendations;
    } catch (e) {
      debugPrint('Error getting personalized recommendations: $e');
      return [];
    }
  }

  /// الحصول على الفئات المفضلة للمستخدم
  Future<List<String>> _getUserFavoriteCategories(String userId) async {
    try {
      // الحصول على الفئات من المنتجات التي تمت مشاهدتها أو إضافتها للمفضلة
      final response = await _supabaseService.client
          .from('user_interactions')
          .select('products(category_id)')
          .eq('user_id', userId)
          .inFilter('interaction_type', [
            'view',
            'add_to_wishlist',
            'add_to_cart',
          ])
          .order('created_at', ascending: false)
          .limit(20);

      // استخراج معرفات الفئات الفريدة
      final categoryIds = <String>{};
      for (final interaction in response) {
        if (interaction['products'] != null &&
            interaction['products']['category_id'] != null) {
          categoryIds.add(interaction['products']['category_id']);
        }
      }

      return categoryIds.toList();
    } catch (e) {
      debugPrint('Error getting user favorite categories: $e');
      return [];
    }
  }

  /// جلب طلبات المستخدم
  Future<List<Map<String, dynamic>>> _getUserOrdersData() async {
    try {
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) return [];

      final response = await _supabaseService.client
          .from('orders')
          .select('id, status, total_amount, created_at')
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .limit(5);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting user orders: $e');
      return [];
    }
  }

  /// جلب معلومات المتجر
  Future<Map<String, dynamic>> _getStoreInfo() async {
    try {
      final response = await _supabaseService.client
          .from('admin_settings')
          .select('setting_key, setting_value')
          .inFilter('setting_key', [
            'store_name',
            'store_hours',
            'store_phone',
          ]);

      final settings = <String, dynamic>{};
      for (final setting in response) {
        final key = setting['setting_key'].toString().replaceAll('store_', '');
        settings[key] = setting['setting_value'];
      }

      return settings;
    } catch (e) {
      debugPrint('Error getting store info: $e');
      return {
        'name': 'متجر قطع غيار الدراجات النارية',
        'hours': 'من 9 صباحاً إلى 9 مساءً',
        'phone': '01234567890',
      };
    }
  }

  Future<void> saveChatHistory(List<Map<String, dynamic>> messages) async {
    try {
      // التحقق من تسجيل دخول المستخدم
      if (!_supabaseService.isAuthenticated) {
        debugPrint('لا يمكن حفظ المحادثة: المستخدم غير مسجل الدخول');
        return;
      }

      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        debugPrint('لا يمكن حفظ المحادثة: لا يوجد اتصال بالإنترنت');
        return;
      }

      // الحصول على آخر محادثة محفوظة
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) {
        debugPrint('لا يمكن حفظ المحادثة: معرف المستخدم غير موجود');
        return;
      }

      final existingHistory =
          await _supabaseService.client
              .from('chat_history')
              .select()
              .eq('user_id', userId)
              .order('created_at', ascending: false)
              .limit(1)
              .maybeSingle();

      // تحديد الرسائل الجديدة فقط للحفظ
      final lastSavedMessageCount =
          existingHistory != null
              ? (existingHistory['messages'] as List).length
              : 0;

      if (messages.length > lastSavedMessageCount) {
        // حفظ المحادثة الكاملة المحدثة
        await _supabaseService.client.from('chat_history').upsert({
          'user_id': userId, // userId تم التحقق منه مسبقًا
          'messages': messages,
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      debugPrint('Failed to save chat history: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getChatHistory() async {
    try {
      // التحقق من تسجيل دخول المستخدم
      if (!_supabaseService.isAuthenticated) {
        return [];
      }

      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return [];
      }

      final userId = _supabaseService.currentUser?.id;
      if (userId == null) {
        return [];
      }

      final history =
          await _supabaseService.client
              .from('chat_history')
              .select()
              .eq('user_id', userId)
              .order('created_at', ascending: false)
              .limit(1)
              .maybeSingle();

      if (history != null && history['messages'] != null) {
        return List<Map<String, dynamic>>.from(history['messages']);
      }
      return [];
    } catch (e) {
      debugPrint('Failed to get chat history: $e');
      return [];
    }
  }

  Future<String> handleProductInquiry(String productName) async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return 'يبدو أنك غير متصل بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
      }

      final response = await _supabaseService.client.rpc(
        'get_product_info',
        params: {'product_name': productName},
      );
      return response as String;
    } catch (e) {
      debugPrint('Product inquiry error: $e');
      return 'عذرًا، لا يمكن العثور على معلومات المنتج حاليًا. يرجى المحاولة لاحقًا.';
    }
  }

  Future<String> handleSalesInquiry() async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        return 'يبدو أنك غير متصل بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';
      }

      final response = await _supabaseService.client.rpc('get_sales_info');
      return response as String;
    } catch (e) {
      debugPrint('Sales inquiry error: $e');
      return 'عذرًا، لا يمكن العثور على معلومات المبيعات حاليًا. يرجى المحاولة لاحقًا.';
    }
  }

  Future<void> sendOrderNotification(String orderId) async {
    try {
      // التحقق من الاتصال بالإنترنت
      if (!await _checkConnectivity()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      await _supabaseService.client.rpc(
        'send_order_notification',
        params: {'order_id': orderId},
      );
    } catch (e) {
      debugPrint('Failed to send order notification: $e');
      throw Exception('فشل في إرسال إشعار حالة الطلب');
    }
  }

  // Speech recognition functions
  Future<void> initSpeech() async {
    try {
      _isInitialized = await _speechToText.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to initialize speech recognition: $e');
      _isInitialized = false;
      notifyListeners();
    }
  }

  Future<String> listenToVoiceCommand() async {
    if (!_isInitialized) {
      await initSpeech(); // Ensure initialization before listening
      if (!_isInitialized) return 'فشل تهيئة التعرف الصوتي';
    }

    String recognizedText = '';
    _isListening = true;
    notifyListeners();

    try {
      await _speechToText.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
        },
        localeId: 'ar_SA', // تعيين اللغة العربية للتعرف الصوتي
      );

      // انتظار انتهاء التعرف الصوتي
      await Future.delayed(const Duration(seconds: 5));
      _stopVoiceRecognition();

      return recognizedText;
    } catch (e) {
      debugPrint('Voice recognition error: $e');
      _stopVoiceRecognition();
      return '';
    }
  }

  void _stopVoiceRecognition() {
    if (_isListening) {
      _speechToText.stop();
      _isListening = false;
      notifyListeners();
    }
  }

  // التحقق من الاتصال بالإنترنت
  Future<bool> _checkConnectivity() async {
    try {
      // محاولة بسيطة للتحقق من الاتصال بالإنترنت عن طريق استعلام بسيط
      await _supabaseService.client
          .from('_dummy_table_for_connectivity_check')
          .select()
          .limit(1)
          .maybeSingle()
          .timeout(const Duration(seconds: 5));
      return true;
    } catch (e) {
      return false;
    }
  }

  // Optional: Getter to check if speech is initialized
  bool get isSpeechInitialized => _isInitialized;

  // Getter to check if speech is currently listening
  bool get isListening => _isListening;
}
