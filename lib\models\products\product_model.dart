import 'dart:convert';

import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/company_model.dart';
import 'package:motorcycle_parts_shop/models/validation_result.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ProductModel {
  /// لا يمكن إنشاء منتج فارغ - يجب الحصول على البيانات من قاعدة البيانات
  static Future<ProductModel> getById(String id) async {
    if (id.isEmpty) {
      throw Exception(
        'يجب توفير معرف المنتج للحصول على بياناته من قاعدة البيانات',
      );
    }

    final supabase = Supabase.instance.client;
    try {
      final response =
          await supabase.from('products').select().eq('id', id).single();

      return ProductModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في الحصول على بيانات المنتج من قاعدة البيانات: $e');
    }
  }

  final String id; // معرف المنتج الفريد
  final String name; // اسم المنتج
  final String? description; // وصف المنتج (اختياري)
  final double price; // سعر المنتج الأصلي
  final double? discountPrice; // سعر المنتج بعد الخصم (اختياري)
  final double? originalPrice; // السعر الأصلي للمنتج قبل أي خصومات (اختياري)
  final String categoryId; // معرف الفئة التي ينتمي إليها المنتج
  final String? categoryName; // اسم الفئة (للعرض فقط، اختياري)
  final String companyId; // معرف الشركة المصنعة للمنتج
  final List<String> imageUrls; // روابط صور المنتج
  final String? imageUrl; // رابط الصورة الرئيسية للمنتج
  final double? discountPercentage;
  String? get mainImage => imageUrl;
  final Map<String, dynamic> specifications; // مواصفات المنتج
  final int stockQuantity; // الكمية المتاحة في المخزون
  // تم حذف حقول التقييم والمراجعات
  final int viewCount; // عدد مرات مشاهدة المنتج
  final int salesCount; // عدد المبيعات
  final bool isFeatured; // هل المنتج مميز؟
  final bool isBestSelling; // هل المنتج من الأكثر مبيعًا؟
  final bool isNew; // هل المنتج جديد؟
  final DateTime? newUntil; // تاريخ انتهاء كون المنتج "جديد"
  final DateTime createdAt; // تاريخ إضافة المنتج
  final DateTime updatedAt; // تاريخ آخر تحديث للمنتج
  final String sku; // كود المخزون الفريد
  final String brand; // العلامة التجارية
  final bool isAvailable; // حالة التوفر
  final int minStockLevel; // الحد الأدنى للمخزون
  final double? weight; // الوزن
  final Map<String, dynamic>? dimensions; // الأبعاد
  final String? metaTitle; // عنوان SEO
  final String? metaDescription; // وصف SEO
  final List<String> tags; // علامات البحث
  final bool isOnSale; // هل المنتج معروض للبيع بخصم؟
  bool? isFavorite; // هل المنتج في المفضلة؟
  final CategoryModel? category; // كائن الفئة المرتبط (اختياري)
  final CompanyModel? company; // كائن الشركة المصنعة المرتبط (اختياري)

  /// المُنشئ الرئيسي لنموذج المنتج
  ProductModel({
    required this.id,
    required this.sku,
    required this.brand,
    required this.isAvailable,
    required this.name,
    this.description,
    required this.price,
    this.discountPrice,
    this.originalPrice,
    required this.categoryId,
    this.categoryName,
    required this.companyId,
    required this.imageUrls,
    this.imageUrl,
    required this.specifications,
    required this.stockQuantity,
    required this.viewCount,
    this.salesCount = 0,
    required this.isFeatured,
    required this.isBestSelling,
    required this.isNew,
    this.newUntil,
    required this.createdAt,
    required this.updatedAt,
    this.minStockLevel = 5,
    this.weight,
    this.dimensions,
    this.metaTitle,
    this.metaDescription,
    this.tags = const [],
    this.category,
    this.company,
    this.isOnSale = false,
    this.isFavorite,
    this.discountPercentage,
  });

  /// تحويل من JSON إلى كائن ProductModel
  ///
  /// يتضمن معالجة متقدمة للأخطاء وتحويل البيانات بشكل آمن
  /// مع تسجيل الأخطاء وإعادة قيم افتراضية مناسبة
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    try {
      // معالجة كائن الفئة إذا كان موجودًا
      CategoryModel? categoryObj;
      if (json['category'] != null) {
        try {
          if (json['category'] is Map<String, dynamic>) {
            categoryObj = CategoryModel.fromJson(
              json['category'] as Map<String, dynamic>,
            );
          } else {
            print(
              'تحذير: حقل category ليس من النوع المتوقع: ${json['category'].runtimeType}',
            );
          }
        } catch (e) {
          print('خطأ في تحويل بيانات الفئة: $e');
        }
      }

      // معالجة كائن الشركة إذا كان موجودًا
      CompanyModel? companyObj;
      if (json['company'] != null) {
        try {
          if (json['company'] is Map<String, dynamic>) {
            companyObj = CompanyModel.fromJson(
              json['company'] as Map<String, dynamic>,
            );
          } else {
            print(
              'تحذير: حقل company ليس من النوع المتوقع: ${json['company'].runtimeType}',
            );
          }
        } catch (e) {
          print('خطأ في تحويل بيانات الشركة: $e');
        }
      }

      // معالجة قائمة الصور بشكل آمن
      List<String> imagesList = [];
      final imagesData = json['image_urls'];
      if (imagesData != null) {
        try {
          if (imagesData is List) {
            imagesList = List<String>.from(
              imagesData.map((img) => img?.toString() ?? ''),
            );
          } else if (imagesData is String) {
            try {
              final List<dynamic> parsedImages = jsonDecode(imagesData);
              imagesList =
                  parsedImages.map((img) => img?.toString() ?? '').toList();
            } catch (e) {
              print('خطأ في تحليل سلسلة الصور JSON: $e');
              imagesList = [imagesData.toString()];
            }
          } else {
            print(
              'تحذير: حقل image_urls من نوع غير متوقع: ${imagesData.runtimeType}',
            );
          }
        } catch (e) {
          print('خطأ في معالجة صور المنتج: $e');
        }
      }

      // معالجة المواصفات بشكل آمن
      Map<String, dynamic> specs = {};
      if (json['specifications'] != null) {
        try {
          if (json['specifications'] is Map) {
            specs = Map<String, dynamic>.from(json['specifications'] as Map);
          } else if (json['specifications'] is String) {
            try {
              specs = jsonDecode(json['specifications'] as String);
            } catch (e) {
              print('خطأ في تحليل سلسلة المواصفات JSON: $e');
              specs = {'error': 'فشل في تحليل المواصفات: $e'};
            }
          } else {
            print(
              'تحذير: حقل specifications من نوع غير متوقع: ${json['specifications'].runtimeType}',
            );
          }
        } catch (e) {
          print('خطأ في معالجة مواصفات المنتج: $e');
        }
      }

      // معالجة الأبعاد بشكل آمن
      Map<String, dynamic>? dims;
      if (json['dimensions'] != null) {
        try {
          if (json['dimensions'] is Map) {
            dims = Map<String, dynamic>.from(json['dimensions'] as Map);
          } else if (json['dimensions'] is String) {
            try {
              dims = jsonDecode(json['dimensions'] as String);
            } catch (e) {
              print('خطأ في تحليل سلسلة الأبعاد JSON: $e');
              dims = {'error': 'فشل في تحليل الأبعاد: $e'};
            }
          } else {
            print(
              'تحذير: حقل dimensions من نوع غير متوقع: ${json['dimensions'].runtimeType}',
            );
          }
        } catch (e) {
          print('خطأ في معالجة أبعاد المنتج: $e');
        }
      }

      // معالجة العلامات بشكل آمن
      List<String> tagsList = const [];
      try {
        if (json['tags'] != null) {
          if (json['tags'] is List) {
            tagsList = (json['tags'] as List).map((e) => e.toString()).toList();
          } else if (json['tags'] is String) {
            try {
              final List<dynamic> parsedTags = jsonDecode(
                json['tags'] as String,
              );
              tagsList = parsedTags.map((tag) => tag.toString()).toList();
            } catch (e) {
              print('خطأ في تحليل سلسلة العلامات JSON: $e');
            }
          }
        }
      } catch (e) {
        print('خطأ في معالجة علامات المنتج: $e');
      }

      // معالجة التواريخ بشكل آمن
      DateTime? newUntilDate;
      try {
        if (json['new_until'] != null) {
          newUntilDate = DateTime.tryParse(json['new_until'].toString());
          if (newUntilDate == null) {
            print(
              'تحذير: تنسيق تاريخ new_until غير صالح: ${json['new_until']}',
            );
          }
        }
      } catch (e) {
        print('خطأ في معالجة تاريخ new_until: $e');
      }

      DateTime createdAtDate;
      try {
        createdAtDate =
            DateTime.tryParse(json['created_at']?.toString() ?? '') ??
            DateTime.now();
      } catch (e) {
        print('خطأ في معالجة تاريخ created_at: $e');
        createdAtDate = DateTime.now();
      }

      DateTime updatedAtDate;
      try {
        updatedAtDate =
            DateTime.tryParse(json['updated_at']?.toString() ?? '') ??
            DateTime.now();
      } catch (e) {
        print('خطأ في معالجة تاريخ updated_at: $e');
        updatedAtDate = DateTime.now();
      }

      // إنشاء كائن المنتج مع معالجة آمنة لجميع الحقول
      return ProductModel(
        id: json['id']?.toString() ?? '',
        sku: json['sku']?.toString() ?? '',
        brand: json['brand']?.toString() ?? '',
        isAvailable: _parseBoolNonNull(json['is_available'], false),
        name: json['name']?.toString() ?? '',
        description: json['description']?.toString(),
        price: _parseDoubleNonNull(json['price'], 0.0),
        discountPrice: _parseDouble(json['discount_price'], null),
        originalPrice: _parseDouble(json['original_price'], null),
        categoryId: json['category_id']?.toString() ?? '',
        categoryName: json['category_name']?.toString(),
        companyId: json['company_id']?.toString() ?? '',
        imageUrls: imagesList,
        imageUrl: json['image_url']?.toString(),
        specifications: specs,
        stockQuantity: _parseInt(json['stock_quantity'], 0),
        viewCount: _parseInt(json['view_count'], 0),
        salesCount: _parseInt(json['sales_count'], 0),
        isFeatured: _parseBoolNonNull(json['is_featured'], false),
        isBestSelling: _parseBoolNonNull(json['is_best_selling'], false),
        isNew: _parseBoolNonNull(json['is_new'], false),
        newUntil: newUntilDate,
        createdAt: createdAtDate,
        updatedAt: updatedAtDate,
        minStockLevel: _parseInt(json['min_stock_level'], 5),
        weight: _parseDouble(json['weight'], null),
        dimensions: dims,
        metaTitle: json['meta_title']?.toString(),
        metaDescription: json['meta_description']?.toString(),
        tags: tagsList,
        isOnSale: _parseBoolNonNull(json['is_on_sale'], false),
        isFavorite: _parseBool(json['is_favorite'], null),
        discountPercentage: _parseDouble(json['discount_percentage'], null),
        category: categoryObj,
        company: companyObj,
      );
    } catch (e, stackTrace) {
      // تسجيل الخطأ الرئيسي مع تفاصيل كاملة
      print('خطأ خطير في تحويل بيانات المنتج: $e');
      print('تفاصيل الخطأ: $stackTrace');

      // إرجاع منتج فارغ أو إعادة رمي الاستثناء حسب الحاجة
      if (json['id'] != null) {
        // محاولة إنشاء منتج بالحد الأدنى من البيانات
        return ProductModel(
          id: json['id'].toString(),
          sku: json['sku']?.toString() ?? 'unknown-sku',
          brand: json['brand']?.toString() ?? '',
          isAvailable: false,
          name: json['name']?.toString() ?? 'منتج غير معروف',
          description: 'حدث خطأ في تحميل بيانات المنتج',
          price: 0.0,
          categoryId: json['category_id']?.toString() ?? '',
          companyId: json['company_id']?.toString() ?? '',
          imageUrls: [],
          specifications: {},
          stockQuantity: 0,
          viewCount: 0,
          salesCount: 0,
          isFeatured: false,
          isBestSelling: false,
          isNew: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else {
        // إذا لم يكن هناك معرف، أعد رمي الاستثناء
        throw FormatException('بيانات المنتج غير صالحة أو مفقودة: $e');
      }
    }
  }

  /// تحويل قيمة إلى عدد صحيح بشكل آمن
  static int _parseInt(dynamic value, int defaultValue) {
    if (value == null) return defaultValue;
    if (value is int) return value;
    if (value is num) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        try {
          return double.parse(value).toInt();
        } catch (e) {
          return defaultValue;
        }
      }
    }
    return defaultValue;
  }

  /// تحويل قيمة إلى قيمة منطقية بشكل آمن
  static bool? _parseBool(dynamic value, bool? defaultValue) {
    if (value == null) return defaultValue;
    if (value is bool) return value;
    if (value is String) {
      if (value.toLowerCase() == 'true') return true;
      if (value.toLowerCase() == 'false') return false;
      if (value == '1') return true;
      if (value == '0') return false;
    }
    if (value is num) {
      if (value == 1) return true;
      if (value == 0) return false;
    }
    return defaultValue;
  }

  /// تحويل قيمة إلى قيمة منطقية بشكل آمن (غير قابلة للإرجاع كقيمة فارغة)
  static bool _parseBoolNonNull(dynamic value, bool defaultValue) {
    return _parseBool(value, defaultValue) ?? defaultValue;
  }

  // دوال الوصول المتوافقة مع الواجهة القديمة
  bool get isOnSaleCompat => isOnSale;

  // دالة للحصول على روابط الصور (للتوافق مع الواجهة القديمة)
  List<String> get imageUrlList {
    if (imageUrls.isNotEmpty) {
      return imageUrls;
    } else if (imageUrl != null) {
      return [imageUrl!];
    } else {
      return [];
    }
  }

  bool get isBestSellingCompat => isBestSelling;

  set isFavoriteCompat(bool value) {
    isFavorite = value;
  }

  // تحويل كائن ProductModel إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sku': sku,
      'name': name,
      'description': description,
      'brand': brand,
      'price': price,
      'discount_price': discountPrice,
      'original_price': originalPrice,
      'category_id': categoryId,
      'category_name': categoryName,
      'company_id': companyId,
      'image_urls': imageUrls,
      'image_url': imageUrl,
      'specifications': specifications,
      'stock_quantity': stockQuantity,
      'view_count': viewCount,
      'sales_count': salesCount,
      'is_featured': isFeatured,
      'is_best_selling': isBestSelling,
      'is_available': isAvailable,
      'is_on_sale': isOnSale,
      'is_new': isNew,
      'new_until': newUntil?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'min_stock_level': minStockLevel,
      'weight': weight,
      'dimensions': dimensions,
      'meta_title': metaTitle,
      'meta_description': metaDescription,
      'tags': tags,
      'discount_percentage': discountPercentage,
    };
  }

  // إنشاء نسخة معدلة من الكائن مع إمكانية تغيير بعض القيم
  ProductModel copyWith({
    String? id,
    String? sku,
    String? brand,
    bool? isAvailable,
    String? name,
    String? description,
    double? price,
    double? discountPrice,
    double? originalPrice,
    String? categoryId,
    String? categoryName,
    String? companyId,
    List<String>? imageUrls,
    String? imageUrl,
    Map<String, dynamic>? specifications,
    int? stockQuantity,
    int? viewCount,
    int? salesCount,
    bool? isFeatured,
    bool? isBestSelling,
    bool? isNew,
    DateTime? newUntil,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? minStockLevel,
    double? weight,
    Map<String, dynamic>? dimensions,
    String? metaTitle,
    String? metaDescription,
    List<String>? tags,
    CategoryModel? category,
    CompanyModel? company,
    bool? isOnSale,
    bool? isFavorite,
    double? discountPercentage,
  }) {
    return ProductModel(
      id: id ?? this.id,
      sku: sku ?? this.sku,
      brand: brand ?? this.brand,
      isAvailable: isAvailable ?? this.isAvailable,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      discountPrice: discountPrice ?? this.discountPrice,
      originalPrice: originalPrice ?? this.originalPrice,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      companyId: companyId ?? this.companyId,
      imageUrls: imageUrls ?? this.imageUrls,
      imageUrl: imageUrl ?? this.imageUrl,
      specifications: specifications ?? this.specifications,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      viewCount: viewCount ?? this.viewCount,
      salesCount: salesCount ?? this.salesCount,
      isFeatured: isFeatured ?? this.isFeatured,
      isBestSelling: isBestSelling ?? this.isBestSelling,
      isNew: isNew ?? this.isNew,
      newUntil: newUntil ?? this.newUntil,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      minStockLevel: minStockLevel ?? this.minStockLevel,
      weight: weight ?? this.weight,
      dimensions: dimensions ?? this.dimensions,
      metaTitle: metaTitle ?? this.metaTitle,
      metaDescription: metaDescription ?? this.metaDescription,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      company: company ?? this.company,
      isOnSale: isOnSale ?? this.isOnSale,
      isFavorite: isFavorite ?? this.isFavorite,
      discountPercentage: discountPercentage ?? this.discountPercentage,
    );
  }

  // دالة مساعدة لتحويل الأرقام
  static double? _parseDouble(dynamic value, double? defaultValue) {
    if (value == null) return defaultValue;
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return defaultValue;
  }

  /// تحويل قيمة إلى رقم عشري بشكل آمن (غير قابل للإرجاع كقيمة فارغة)
  static double _parseDoubleNonNull(dynamic value, double defaultValue) {
    return _parseDouble(value, defaultValue) ?? defaultValue;
  }
}

extension LoadingProductModel on ProductModel {
  static ProductModel loading() {
    return ProductModel(
      id: 'loading',
      sku: 'loading',
      brand: 'loading',
      isAvailable: false,
      name: 'جاري التحميل...',
      description: 'جاري تحميل تفاصيل المنتج',
      price: 0.0,
      discountPrice: null,
      categoryId: 'loading',
      categoryName: null,
      companyId: 'loading',
      imageUrls: [],
      imageUrl: null,
      specifications: {},
      stockQuantity: 0,
      viewCount: 0,
      salesCount: 0,
      isFeatured: false,
      isBestSelling: false,
      isNew: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      minStockLevel: 5,
      tags: [],
      category: null,
      company: null,
      isOnSale: false,
    );
  }

  /// التحقق مما إذا كان المنتج فارغًا أو غير صالح
  bool get isEmpty => id.isEmpty || name.isEmpty;

  /// التحقق من صحة بيانات المنتج
  bool get isValid => !isEmpty && price > 0 && stockQuantity >= 0;

  /// التحقق الشامل من صحة بيانات المنتج
  ValidationResult validateProduct() {
    final errors = <String>[];
    final warnings = <String>[];

    // التحقق من الحقول الأساسية
    if (id.isEmpty) errors.add('معرف المنتج مطلوب');
    if (sku.isEmpty) errors.add('كود المنتج (SKU) مطلوب');
    if (name.isEmpty) errors.add('اسم المنتج مطلوب');
    if (name.length < 3) warnings.add('اسم المنتج قصير جداً');
    if (name.length > 200) errors.add('اسم المنتج طويل جداً');

    // التحقق من الوصف
    if (description?.isEmpty ?? true) warnings.add('وصف المنتج مفقود');
    if ((description?.length ?? 0) > 5000) warnings.add('وصف المنتج طويل جداً');

    // التحقق من السعر
    if (price < 0) errors.add('السعر يجب أن يكون موجب');
    if (price == 0) warnings.add('السعر صفر - تأكد من صحة السعر');
    if (price > 1000000) warnings.add('السعر مرتفع جداً');

    // التحقق من سعر الخصم
    if (discountPrice != null) {
      if (discountPrice! < 0) errors.add('سعر الخصم يجب أن يكون موجب');
      if (discountPrice! >= price) {
        errors.add('سعر الخصم يجب أن يكون أقل من السعر الأصلي');
      }
    }

    // التحقق من الكمية
    if (stockQuantity < 0) errors.add('الكمية يجب أن تكون موجبة أو صفر');
    if (stockQuantity == 0 && isAvailable) {
      warnings.add('المنتج متاح لكن الكمية صفر');
    }
    if (stockQuantity > 0 && !isAvailable) {
      warnings.add('المنتج غير متاح لكن الكمية موجودة');
    }

    // تم حذف التحقق من التقييم والمراجعات

    // التحقق من العلامة التجارية والفئة
    if (brand.isEmpty) warnings.add('العلامة التجارية مفقودة');
    if (categoryId.isEmpty) errors.add('معرف الفئة مطلوب');
    if (companyId.isEmpty) errors.add('معرف الشركة مطلوب');

    // التحقق من الصور
    if (imageUrls.isEmpty) warnings.add('لا توجد صور للمنتج');
    if (imageUrls.length > 10) warnings.add('عدد الصور كثير جداً');

    // التحقق من الوزن والأبعاد
    if (weight != null && weight! <= 0) warnings.add('الوزن يجب أن يكون موجب');
    if (dimensions != null) {
      final length = dimensions!['length'];
      final width = dimensions!['width'];
      final height = dimensions!['height'];
      if (length != null && length <= 0) warnings.add('الطول يجب أن يكون موجب');
      if (width != null && width <= 0) warnings.add('العرض يجب أن يكون موجب');
      if (height != null && height <= 0) {
        warnings.add('الارتفاع يجب أن يكون موجب');
      }
    }

    // التحقق من الحد الأدنى للمخزون
    if (minStockLevel < 0) errors.add('الحد الأدنى للمخزون يجب أن يكون موجب');
    if (stockQuantity > 0 && stockQuantity <= minStockLevel) {
      warnings.add('الكمية أقل من أو تساوي الحد الأدنى للمخزون');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// الحصول على رابط صورة محسن بحجم معين
  String getOptimizedImageUrl([int width = 300, int height = 300]) {
    if (imageUrls.isEmpty) return '';
    final mainImage = imageUrls.first;
    if (!mainImage.contains('?')) {
      return '$mainImage?width=$width&height=$height&quality=80';
    }
    return mainImage;
  }
}
