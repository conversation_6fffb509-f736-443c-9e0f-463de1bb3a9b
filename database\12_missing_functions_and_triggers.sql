-- ===================================================================
-- دوال مفقودة ومحفزات
-- ===================================================================

-- دالة تحديث إحصائيات المستخدم
CREATE OR REPLACE FUNCTION update_user_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- تحديث عدد الطلبات
        UPDATE profiles 
        SET order_count = COALESCE(order_count, 0) + 1
        WHERE id = NEW.user_id;
        
        -- تحديث إجمالي المشتريات
        UPDATE profiles 
        SET total_spent = COALESCE(total_spent, 0) + NEW.total_amount
        WHERE id = NEW.user_id;
        
        RETURN NEW;
    END IF;

    IF TG_OP = 'UPDATE' THEN
        -- تحديث إجمالي المشتريات عند تغيير المبلغ
        IF OLD.total_amount != NEW.total_amount THEN
            UPDATE profiles 
            SET total_spent = COALESCE(total_spent, 0) - OLD.total_amount + NEW.total_amount
            WHERE id = NEW.user_id;
        END IF;
        
        RETURN NEW;
    END IF;

    IF TG_OP = 'DELETE' THEN
        -- تخفيض عدد الطلبات
        UPDATE profiles 
        SET order_count = GREATEST(COALESCE(order_count, 0) - 1, 0)
        WHERE id = OLD.user_id;
        
        -- تخفيض إجمالي المشتريات
        UPDATE profiles 
        SET total_spent = GREATEST(COALESCE(total_spent, 0) - OLD.total_amount, 0)
        WHERE id = OLD.user_id;
        
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$;

-- دالة التحقق من صلاحيات الوصول للطلب
CREATE OR REPLACE FUNCTION can_access_order(order_id UUID, user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_order_user_id UUID;
    v_is_admin BOOLEAN := false;
BEGIN
    -- التحقق من وجود الطلب
    SELECT o.user_id INTO v_order_user_id
    FROM orders o
    WHERE o.id = order_id;
    
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    -- التحقق من أن المستخدم هو صاحب الطلب
    IF v_order_user_id = user_id THEN
        RETURN true;
    END IF;
    
    -- التحقق من صلاحيات المدير
    SELECT is_admin(user_id) INTO v_is_admin;
    
    IF v_is_admin THEN
        RETURN true;
    END IF;
    
    RETURN false;
END;
$$;

-- دالة تعيين سياق المستخدم
CREATE OR REPLACE FUNCTION set_user_context()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user_id UUID;
    v_profile_type VARCHAR(20);
    v_is_admin BOOLEAN := false;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        PERFORM set_config('app.current_user_type', 'anonymous', true);
        RETURN;
    END IF;
    
    -- الحصول على نوع الملف الشخصي
    SELECT profile_type INTO v_profile_type
    FROM profiles
    WHERE id = v_user_id;
    
    -- التحقق من صلاحيات المدير
    SELECT is_admin(v_user_id) INTO v_is_admin;
    
    -- تعيين سياق المستخدم
    PERFORM set_config('app.current_user_id', v_user_id::TEXT, true);
    PERFORM set_config('app.current_user_type', v_profile_type, true);
    PERFORM set_config('app.is_admin', v_is_admin::TEXT, true);
END;
$$;

-- دالة تعيين سياق المدير
CREATE OR REPLACE FUNCTION set_admin_context(user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    IF is_admin(user_id) THEN
        PERFORM set_config('app.current_user_type', 'admin', true);
        PERFORM set_config('app.admin_user_id', user_id::TEXT, true);
    ELSE
        RAISE EXCEPTION 'المستخدم ليس مديراً';
    END IF;
END;
$$;

-- ===================================================================
-- جداول ودوال دعم الشات بوت
-- ===================================================================

-- دالة جلب معلومات المنتج
CREATE OR REPLACE FUNCTION get_product_info(product_name TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    product_info JSONB;
BEGIN
    SELECT jsonb_build_object(
        'id', id,
        'name', name,
        'description', description,
        'price', price,
        'quantity', stock_quantity,
        'brand', brand,
        'category', (SELECT name FROM categories WHERE id = products.category_id),
        'is_available', CASE WHEN stock_quantity > 0 THEN true ELSE false END
    ) INTO product_info
    FROM products
    WHERE name ILIKE '%' || product_name || '%'
    AND is_available = true
    LIMIT 1;

    IF product_info IS NULL THEN
        RETURN 'لم يتم العثور على المنتج';
    END IF;

    RETURN product_info::TEXT;
END;
$$;

-- دالة جلب معلومات المبيعات
CREATE OR REPLACE FUNCTION get_sales_info()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    sales_info JSONB;
BEGIN
    SELECT jsonb_build_object(
        'total_orders', (SELECT COUNT(*) FROM orders),
        'total_revenue', (SELECT COALESCE(SUM(total_amount), 0) FROM orders),
        'active_offers', (
            SELECT jsonb_agg(jsonb_build_object(
                'title', title,
                'description', description,
                'discount_percentage', discount_percentage
            ))
            FROM offers
            WHERE is_active = true
            AND end_date > NOW()
        ),
        'top_products', (
            SELECT jsonb_agg(jsonb_build_object(
                'name', name,
                'price', price,
                'sales_count', COALESCE(sales_count, 0)
            ))
            FROM products
            WHERE is_available = true
            ORDER BY COALESCE(sales_count, 0) DESC, created_at DESC
            LIMIT 5
        )
    ) INTO sales_info;

    RETURN sales_info::TEXT;
END;
$$;

-- دالة إرسال إشعار حالة الطلب
CREATE OR REPLACE FUNCTION send_order_notification(order_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    order_info JSONB;
    user_id UUID;
BEGIN
    -- جلب معلومات الطلب والمستخدم
    SELECT 
        jsonb_build_object(
            'order_id', o.id,
            'status', o.status,
            'total_amount', o.total_amount,
            'created_at', o.created_at
        ),
        o.user_id
    INTO order_info, user_id
    FROM orders o
    WHERE o.id = order_id;

    -- إدراج الإشعار
    INSERT INTO notifications (
        user_id,
        title,
        body,
        type,
        data
    ) VALUES (
        user_id,
        'تحديث حالة الطلب',
        'تم تحديث حالة طلبك رقم ' || order_id,
        'order_update',
        order_info
    );
END;
$$;

------------------------------------------------------------------

CREATE OR REPLACE FUNCTION public.cleanup_search_analytics()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path = public
AS $function$
BEGIN
    -- Delete old, irrelevant search terms
    DELETE FROM public.search_analytics
    WHERE results_count = 0 
      AND search_count < 5 
      AND created_at < NOW() - INTERVAL '30 days';
    
    RETURN NULL;
END;
$function$;
---------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION public.update_search_analytics(p_search_term text)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path = public
AS $function$
BEGIN
    -- التحقق من صحة المدخلات
    IF p_search_term IS NULL OR TRIM(p_search_term) = '' THEN
        RETURN;
    END IF;

    -- إدراج أو تحديث كلمة البحث، زيادة عدد البحث، تحديث وقت آخر بحث
    INSERT INTO public.search_analytics (search_term, search_count, last_searched_at)
    VALUES (p_search_term, 1, NOW())
    ON CONFLICT (search_term) 
    DO UPDATE SET 
        search_count = public.search_analytics.search_count + 1,
        last_searched_at = NOW();
END;
$function$;

--------------------------------------------------------------------------

CREATE OR REPLACE FUNCTION public.update_search_analytics()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path = public
AS $function$
BEGIN
    -- Update most searched terms
    INSERT INTO public.search_analytics (search_term, search_count, last_searched_at)
    SELECT 
        keyword, 
        COUNT(*) as search_count, 
        MAX(created_at) as last_searched_at
    FROM public.search_logs
    WHERE created_at > NOW() - INTERVAL '24 hours'
    GROUP BY keyword
    ON CONFLICT (search_term) 
    DO UPDATE SET 
        search_count = public.search_analytics.search_count + EXCLUDED.search_count,
        last_searched_at = GREATEST(public.search_analytics.last_searched_at, EXCLUDED.last_searched_at);
    
    -- Update most searched products
    UPDATE public.products p
    SET search_count = p.search_count + sq.count
    FROM (
        SELECT product_id, COUNT(*) as count
        FROM public.product_views
        WHERE created_at > NOW() - INTERVAL '24 hours'
        GROUP BY product_id
    ) as sq
    WHERE p.id = sq.product_id;
    
    -- Log the update operation
    INSERT INTO public.system_logs (
        action, entity_type, description, ip_address, user_agent
    ) VALUES (
        'maintenance', 'system', 'Updated search analytics', '127.0.0.1', 'System Cron'
    );
END;
$function$;

------------------------------------------------------

CREATE OR REPLACE FUNCTION public.insert_search_log(
    p_keyword text,
    p_results_count integer DEFAULT 0,
    p_clicked_product_id uuid DEFAULT NULL,
    p_device_type character varying DEFAULT NULL,
    p_ip_address inet DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
    v_user_id uuid;
    v_session_id varchar(100);
    v_log_id uuid;
BEGIN
    -- التحقق من صحة الكلمة المفتاحية
    IF p_keyword IS NULL OR TRIM(p_keyword) = '' THEN
        RAISE EXCEPTION 'الكلمة المفتاحية لا يمكن أن تكون فارغة أو null';
    END IF;

    -- الحصول على معرف المستخدم (قد يكون null للمستخدمين المجهولين)
    BEGIN
        v_user_id := auth.uid();
    EXCEPTION WHEN OTHERS THEN
        v_user_id := NULL; -- تعيين null إذا فشلت auth.uid()
    END;

    -- توليد معرف جلسة إذا لم يُمرر من التطبيق
    v_session_id := COALESCE(
        current_setting('app.session_id', true), -- افتراض أن التطبيق يمرر session_id عبر إعدادات
        gen_random_uuid()::text
    );

    -- إدراج سجل البحث
    INSERT INTO public.search_logs (
        user_id, 
        session_id, 
        keyword, 
        results_count, 
        clicked_product_id, 
        device_type, 
        ip_address
    ) VALUES (
        v_user_id,
        v_session_id,
        p_keyword,
        p_results_count,
        p_clicked_product_id,
        p_device_type,
        p_ip_address
    ) RETURNING id INTO v_log_id;

    RETURN v_log_id;
END;
$function$;