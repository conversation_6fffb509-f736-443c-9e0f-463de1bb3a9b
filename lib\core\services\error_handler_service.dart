import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// نظام معالجة الأخطاء الموحد
class ErrorHandler {
  static final Map<Type, ErrorHandlerFunction> _handlers = {
    SocketException: _handleNetworkError,
    AuthException: _handleAuthError,
    PostgrestException: _handleDatabaseError,
    ArgumentError: _handleValidationError,
    InsufficientStockException: _handleStockError,
    TimeoutException: _handleTimeoutError,
  };

  /// تهيئة معالج الأخطاء
  static void initialize() {
    debugPrint('✅ تم تهيئة معالج الأخطاء');
  }

  /// معالجة الخطأ الرئيسية
  static void handleError(
    dynamic error, {
    BuildContext? context,
    String? userMessage,
    bool showSnackBar = true,
    String? contextInfo,
    StackTrace? stackTrace,
  }) {
    // تسجيل الخطأ أولاً
    _logError(error, contextInfo, stackTrace);

    // العثور على المعالج المناسب
    final handler = _handlers[error.runtimeType];

    if (handler != null) {
      handler(error, context, userMessage, showSnackBar);
    } else {
      _handleGenericError(error, context, userMessage, showSnackBar);
    }
  }

  /// معالجة أخطاء الشبكة
  static void _handleNetworkError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final message =
        userMessage ??
        'مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';

    if (showSnackBar && context != null) {
      _showErrorSnackBar(
        context,
        message,
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          onPressed: () => _retryLastOperation(),
        ),
      );
    }
  }

  /// معالجة أخطاء المصادقة
  static void _handleAuthError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final authError = error as AuthException;
    String message;

    switch (authError.message) {
      case 'Invalid login credentials':
        message = 'بيانات تسجيل الدخول غير صحيحة';
        break;
      case 'Email not confirmed':
        message = 'يرجى تأكيد البريد الإلكتروني أولاً';
        break;
      case 'User not found':
        message = 'المستخدم غير موجود';
        break;
      case 'Signup not allowed for this instance':
        message = 'التسجيل غير مسموح حالياً';
        break;
      default:
        message = userMessage ?? 'خطأ في المصادقة: ${authError.message}';
    }

    if (showSnackBar && context != null) {
      _showErrorSnackBar(context, message);
    }

    // إذا كان خطأ انتهاء الجلسة، توجيه للتسجيل
    if (authError.message.contains('JWT') ||
        authError.message.contains('expired')) {
      _redirectToLogin(context);
    }
  }

  /// معالجة أخطاء قاعدة البيانات
  static void _handleDatabaseError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final dbError = error as PostgrestException;
    String message;

    switch (dbError.code) {
      case '23505': // Unique violation
        message = 'البيانات موجودة مسبقاً';
        break;
      case '23503': // Foreign key violation
        message = 'لا يمكن حذف هذا العنصر لأنه مرتبط ببيانات أخرى';
        break;
      case '42501': // Insufficient privilege
        message = 'ليس لديك صلاحية لتنفيذ هذا الإجراء';
        break;
      default:
        message = userMessage ?? 'خطأ في قاعدة البيانات';
    }

    if (showSnackBar && context != null) {
      _showErrorSnackBar(context, message);
    }
  }

  /// معالجة أخطاء التحقق من صحة البيانات
  static void _handleValidationError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final validationError = error as ArgumentError;
    final message =
        userMessage ?? validationError.message ?? 'بيانات غير صحيحة';

    if (showSnackBar && context != null) {
      _showErrorSnackBar(context, message);
    }
  }

  /// معالجة أخطاء نقص المخزون
  static void _handleStockError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final stockError = error as InsufficientStockException;
    final message = userMessage ?? stockError.message;

    if (showSnackBar && context != null) {
      _showErrorSnackBar(
        context,
        message,
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: 'تحديث',
          onPressed: () => _refreshProductStock(),
        ),
      );
    }
  }

  /// معالجة أخطاء انتهاء المهلة الزمنية
  static void _handleTimeoutError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final message = userMessage ?? 'انتهت المهلة الزمنية للطلب. حاول مرة أخرى.';

    if (showSnackBar && context != null) {
      _showErrorSnackBar(
        context,
        message,
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          onPressed: () => _retryLastOperation(),
        ),
      );
    }
  }

  /// معالجة الأخطاء العامة
  static void _handleGenericError(
    dynamic error,
    BuildContext? context,
    String? userMessage,
    bool showSnackBar,
  ) {
    final message = userMessage ?? 'حدث خطأ غير متوقع. حاول مرة أخرى.';

    if (showSnackBar && context != null) {
      _showErrorSnackBar(context, message);
    }
  }

  /// عرض رسالة خطأ في SnackBar
  static void _showErrorSnackBar(
    BuildContext context,
    String message, {
    Color? backgroundColor,
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(color: Colors.white)),
        backgroundColor: backgroundColor ?? Colors.red,
        action: action,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// تسجيل الخطأ للتحليل
  static void _logError(
    dynamic error,
    String? context,
    StackTrace? stackTrace,
  ) {
    // تسجيل في وحدة التحكم للتطوير
    if (kDebugMode) {
      debugPrint('🚨 خطأ في التطبيق:');
      debugPrint('النوع: ${error.runtimeType}');
      debugPrint('الرسالة: $error');
      if (context != null) debugPrint('السياق: $context');
      if (stackTrace != null) debugPrint('Stack Trace: $stackTrace');
    }

    // تسجيل في خدمة التحليلات (يمكن إضافتها لاحقاً)
    // TODO: إضافة تسجيل في خدمة التحليلات
    debugPrint('📊 تسجيل الخطأ: ${error.runtimeType} - $error');
  }

  /// إعادة المحاولة للعملية الأخيرة
  static void _retryLastOperation() {
    // يمكن تطبيق منطق إعادة المحاولة هنا
    debugPrint('🔄 إعادة المحاولة...');
  }

  /// تحديث مخزون المنتج
  static void _refreshProductStock() {
    // يمكن تطبيق منطق تحديث المخزون هنا
    debugPrint('🔄 تحديث المخزون...');
  }

  /// توجيه لشاشة تسجيل الدخول
  static void _redirectToLogin(BuildContext? context) {
    if (context != null) {
      Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
    }
  }

  /// عرض حوار خطأ مفصل
  static void showErrorDialog(
    BuildContext context,
    String title,
    String message, {
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              if (onRetry != null)
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onRetry();
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}

/// نوع دالة معالجة الأخطاء
typedef ErrorHandlerFunction =
    void Function(
      dynamic error,
      BuildContext? context,
      String? userMessage,
      bool showSnackBar,
    );

/// استثناءات مخصصة
class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
  @override
  String toString() => message;
}

class InsufficientStockException implements Exception {
  final String message;
  InsufficientStockException(this.message);
  @override
  String toString() => message;
}

class ValidationException implements Exception {
  final String message;
  ValidationException(this.message);
  @override
  String toString() => message;
}

class TimeoutException implements Exception {
  final String message;
  TimeoutException(this.message);
  @override
  String toString() => message;
}
