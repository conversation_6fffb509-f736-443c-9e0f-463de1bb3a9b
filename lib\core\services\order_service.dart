import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/models/orders/order_item_model.dart';
import 'package:motorcycle_parts_shop/models/orders/order_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart'
    show Supabase, SupabaseClient;

/// خدمة إدارة الطلبات للتعامل مع عمليات جلب وإدارة الطلبات من Supabase
/// تم تحسينها لدعم التخزين المؤقت وترقيم الصفحات وإحصائيات الطلبات
class OrderService {
  static final OrderService _instance = OrderService._internal();
  final AuthSupabaseService _authService = AuthSupabaseService();
  final SupabaseClient _supabase = Supabase.instance.client;
  bool _isInitialized = false;
  static const Duration _cacheDuration = Duration(minutes: 5);
  final Map<String, dynamic> _cache = {};

  // حجم الصفحة الافتراضي (عدد الطلبات في كل تحميل)
  static const int defaultPageSize = 10;

  factory OrderService() => _instance;

  OrderService._internal();

  bool get isInitialized => _isInitialized;

  /// تهيئة خدمة الطلبات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // التحقق من الاتصال بـ Supabase
      await _checkConnection();
      _isInitialized = true;
      debugPrint('تم تهيئة خدمة الطلبات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الطلبات: $e');
      // لا نرمي استثناء للسماح للتطبيق بالاستمرار حتى لو فشلت التهيئة الأولية
    }
  }

  Future<void> _checkConnection() async {
    try {
      await _supabase
          .from(AppConstants.ordersTable)
          .select('*')
          .limit(1)
          .timeout(const Duration(seconds: 10)); // زيادة مهلة الاتصال
    } catch (e) {
      debugPrint('خطأ في الاتصال بخادم Supabase: $e');
      // لا نرمي استثناء هنا للسماح للتطبيق بالعمل في وضع عدم الاتصال
      // بدلاً من ذلك، نسجل الخطأ ونستمر
      _isInitialized = true; // نعتبر الخدمة مهيأة حتى في حالة الفشل
    }
  }

  T? _getFromCache<T>(String key) {
    final cachedData = _cache[key];
    if (cachedData != null &&
        cachedData['timestamp'] != null &&
        DateTime.now().difference(cachedData['timestamp']) < _cacheDuration) {
      return cachedData['data'] as T;
    }
    return null;
  }

  void _addToCache(String key, dynamic data) {
    _cache[key] = {'data': data, 'timestamp': DateTime.now()};
  }

  /// جلب قائمة طلبات المستخدم الحالي مع دعم ترقيم الصفحات
  /// [page] رقم الصفحة (يبدأ من 0)
  /// [pageSize] عدد العناصر في الصفحة
  Future<List<OrderModel>> getUserOrders({
    int page = 0,
    int pageSize = defaultPageSize,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      // التأكد من تسجيل دخول المستخدم
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final cacheKey = 'user_orders_${userId}_${page}_$pageSize';
      final cachedOrders = _getFromCache<List<OrderModel>>(cacheKey);
      if (cachedOrders != null) {
        return cachedOrders;
      }

      final int startRange = page * pageSize;
      final int endRange = startRange + pageSize - 1;

      // استعلام Supabase لجلب طلبات المستخدم الحالي
      final response = await _supabase
          .from(AppConstants.ordersTable)
          .select(
            '*, address:${AppConstants.addressesTable}(*), items:${AppConstants.orderItemsTable}(*)',
          )
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .range(startRange, endRange)
          .timeout(const Duration(seconds: 10));

      // تحويل البيانات إلى نماذج OrderModel
      final orders =
          (response as List<dynamic>)
              .map((order) => OrderModel.fromJson(order))
              .toList();

      _addToCache(cacheKey, orders);
      return orders;
    } catch (e) {
      debugPrint('خطأ في جلب طلبات المستخدم: $e');
      return [];
    }
  }

  /// جلب تفاصيل طلب محدد بواسطة معرف الطلب
  Future<OrderModel?> getOrderDetails(String orderId) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = 'order_details_$orderId';
      final cachedOrder = _getFromCache<OrderModel>(cacheKey);
      if (cachedOrder != null) {
        return cachedOrder;
      }

      // استعلام Supabase لجلب تفاصيل الطلب
      final response =
          await _supabase
              .from(AppConstants.ordersTable)
              .select(
                '*, address:${AppConstants.addressesTable}(*), items:${AppConstants.orderItemsTable}(*)',
              )
              .eq('id', orderId)
              .single();

      final order = OrderModel.fromJson(response);
      _addToCache(cacheKey, order);
      return order;
    } catch (e) {
      debugPrint('خطأ في جلب تفاصيل الطلب: $e');
      return null;
    }
  }

  /// جلب حالة تتبع الطلب
  Future<List<Map<String, dynamic>>> getOrderTrackingStatus(
    String orderId,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = 'order_tracking_$orderId';
      final cachedTracking = _getFromCache<List<Map<String, dynamic>>>(
        cacheKey,
      );
      if (cachedTracking != null) {
        return cachedTracking;
      }

      // استعلام Supabase لجلب سجل حالة الطلب من الجدول الجديد
      final response = await _supabase
          .from(AppConstants.orderTrackingTable)
          .select('*')
          .eq('order_id', orderId)
          .order('created_at', ascending: true);

      final List<Map<String, dynamic>> trackingData =
          List<Map<String, dynamic>>.from(response);
      _addToCache(cacheKey, trackingData);
      return trackingData;
    } catch (e) {
      debugPrint('خطأ في جلب حالة تتبع الطلب: $e');
      return [];
    }
  }

  /// جلب الطلبات الأخيرة
  /// [limit] عدد الطلبات المطلوبة
  Future<List<OrderModel>> getRecentOrders({int limit = 5}) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = 'recent_orders_$limit';
      final cachedOrders = _getFromCache<List<OrderModel>>(cacheKey);
      if (cachedOrders != null) {
        return cachedOrders;
      }

      final response = await _supabase
          .from(AppConstants.ordersTable)
          .select(
            '*, address:${AppConstants.addressesTable}(*), items:${AppConstants.orderItemsTable}(*)',
          )
          .order('created_at', ascending: false)
          .limit(limit)
          .timeout(const Duration(seconds: 10));

      final orders =
          (response as List<dynamic>)
              .map((data) => OrderModel.fromJson(data))
              .toList();

      _addToCache(cacheKey, orders);
      return orders;
    } catch (e) {
      debugPrint('خطأ في جلب الطلبات الأخيرة: $e');
      return [];
    }
  }

  /// جلب إحصائيات الطلبات
  /// يعيد خريطة تحتوي على إحصائيات مختلفة مثل عدد الطلبات حسب الحالة
  Future<Map<String, dynamic>> getOrderStats() async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = 'order_stats';
      final cachedStats = _getFromCache<Map<String, dynamic>>(cacheKey);
      if (cachedStats != null) {
        return cachedStats;
      }

      // جلب عدد الطلبات حسب الحالة
      final pendingCountResponse = await _supabase
          .from('orders')
          .select('*')
          .eq('status', AppConstants.orderStatusPending)
          .timeout(const Duration(seconds: 5));
      final pendingCount = (pendingCountResponse as List).length;

      // إضافة حالة قيد المعالجة
      final processingCountResponse = await _supabase
          .from('orders')
          .select('*')
          .eq('status', AppConstants.orderStatusProcessing)
          .timeout(const Duration(seconds: 5));
      final processingCount = (processingCountResponse as List).length;

      final deliveringCountResponse = await _supabase
          .from('orders')
          .select('*')
          .eq('status', AppConstants.orderStatusShipped)
          .timeout(const Duration(seconds: 5));
      final deliveringCount = (deliveringCountResponse as List).length;

      final deliveredCountResponse = await _supabase
          .from('orders')
          .select('*')
          .eq('status', AppConstants.orderStatusDelivered)
          .timeout(const Duration(seconds: 5));
      final deliveredCount = (deliveredCountResponse as List).length;

      // جلب إجمالي المبيعات اليوم
      final todaySales = await _supabase
          .from('orders')
          .select('total_amount')
          .gte(
            'created_at',
            DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
          )
          .timeout(const Duration(seconds: 5));

      // حساب إجمالي المبيعات اليوم
      double todayTotal = 0;
      for (var order in todaySales) {
        todayTotal += (order['total_amount'] ?? 0);
      }

      // إنشاء خريطة الإحصائيات
      final stats = {
        'pending_count': pendingCount,
        'processing_count': processingCount,
        'delivering_count': deliveringCount,
        'delivered_count': deliveredCount,
        'today_sales': todayTotal,
      };

      _addToCache(cacheKey, stats);
      return stats;
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات الطلبات: $e');
      return {};
    }
  }

  /// إنشاء طلب جديد
  Future<String?> createOrder({
    required String addressId,
    required double totalAmount,
    // تم حذف paymentMethodId حسب المتطلبات
    String? shippingMethodId,
    String? notes,
    required List<OrderItemModel> items,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      // التأكد من تسجيل دخول المستخدم
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إنشاء الطلب
      final orderData = {
        'user_id': userId,
        'address_id': addressId,
        'total_amount': totalAmount,
        'status': AppConstants.orderStatusPending,
        // تم حذف payment_method_id و payment_status حسب المتطلبات
        'shipping_method_id': shippingMethodId,
        'notes': notes,
      };

      final orderResponse =
          await _supabase
              .from(AppConstants.ordersTable)
              .insert(orderData)
              .select('id')
              .single();

      final orderId = orderResponse['id'] as String;

      // إضافة عناصر الطلب
      final orderItems =
          items
              .map(
                (item) => {
                  'order_id': orderId,
                  'product_id': item.productId,
                  'quantity': item.quantity,
                  'unit_price': item.unitPrice,
                  'total_price': item.totalPrice,
                },
              )
              .toList();

      await _supabase.from(AppConstants.orderItemsTable).insert(orderItems);

      // مسح ذاكرة التخزين المؤقت للطلبات
      _clearOrderCache(userId);

      return orderId;
    } catch (e) {
      debugPrint('خطأ في إنشاء الطلب: $e');
      return null;
    }
  }

  /// تحديث حالة الطلب
  Future<bool> updateOrderStatus({
    required String orderId,
    required String status,
    String? trackingNumber,
    DateTime? estimatedDelivery,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      // التحقق من صحة حالة الطلب
      if (!AppConstants.isValidOrderStatus(status)) {
        throw Exception('حالة الطلب غير صالحة: $status');
      }

      // تحديث حالة الطلب
      await _supabase
          .from(AppConstants.ordersTable)
          .update({
            'status': status,
            'tracking_number': trackingNumber,
            'estimated_delivery': estimatedDelivery?.toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId);

      // إضافة سجل لتاريخ حالة الطلب
      await _supabase.from(AppConstants.orderStatusHistoryTable).insert({
        'order_id': orderId,
        'status': status,
        'notes': 'تم تحديث حالة الطلب إلى $status',
        'created_at': DateTime.now().toIso8601String(),
      });

      // مسح ذاكرة التخزين المؤقت للطلب
      _clearOrderCache(null, orderId: orderId);

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// مسح ذاكرة التخزين المؤقت للطلبات
  void _clearOrderCache(String? userId, {String? orderId}) {
    if (orderId != null) {
      // مسح ذاكرة التخزين المؤقت لطلب محدد
      _cache.remove('order_details_$orderId');
      _cache.remove('order_tracking_$orderId');
    }

    if (userId != null) {
      // مسح ذاكرة التخزين المؤقت لطلبات المستخدم
      _cache.removeWhere((key, value) => key.startsWith('user_orders_$userId'));
    }

    // مسح ذاكرة التخزين المؤقت للإحصائيات والطلبات الأخيرة
    _cache.remove('order_stats');
    _cache.removeWhere((key, value) => key.startsWith('recent_orders_'));
  }

  Future<void> cancelOrder(String orderId) async {
    try {
      await _supabase
          .from('orders')
          .update({
            'status': AppConstants.orderStatusCancelled,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId);
    } catch (e) {
      throw Exception('Failed to cancel order: $e');
    }
  }
}
