-- ===================================================================
-- جداول المنتجات والكتالوج
-- 
-- هذا الملف يحتوي على:
-- 1. جدول الشركات المصنعة
-- 2. جدول الفئات والفئات الفرعية
-- 3. جدول المنتجات الرئيسي
-- 4. جدول صور المنتجات
-- 5. جدول مواصفات المنتجات
-- 6. الفهارس المحسنة للبحث والأداء
-- 7. فهارس البحث النصي المتقدم
-- ===================================================================

-- ===================================================================
-- 1. جدول الشركات المصنعة
-- ===================================================================
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    logo TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    products_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_is_active ON companies(is_active);


-- ===================================================================
-- 2. جدول الفئات
-- ===================================================================
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    image_url TEXT,
    icon_data INTEGER,
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    products_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_is_active ON categories(is_active);
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);


-- ===================================================================
-- 3. جدول المنتجات
-- ===================================================================
-- جدول المنتجات الرئيسي
-- يخزن جميع بيانات المنتجات المتاحة في المتجر
CREATE TABLE IF NOT EXISTS products (
    -- معلومات المنتج الأساسية
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sku VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    brand VARCHAR(100),
    
    -- معلومات التسعير
    price DECIMAL(12,2) NOT NULL CHECK (price >= 0),
    discount_price DECIMAL(12,2) CHECK (discount_price >= 0 AND discount_price <= price),
    original_price DECIMAL(12,2),
    
    -- العلاقات مع الجداول الأخرى
    category_id UUID NOT NULL REFERENCES categories(id) ON DELETE RESTRICT,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE RESTRICT,
    
    -- الوسائط والمواصفات
    image_urls JSONB DEFAULT '[]'::jsonb,
    specifications JSONB DEFAULT '{}'::jsonb,
    
    -- معلومات المخزون
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    min_stock_level INTEGER DEFAULT 5,
    
    -- الخصائص الفيزيائية
    weight DECIMAL(8,3),
    dimensions JSONB,
    
    -- إحصائيات وبيانات تتبع
    view_count INTEGER DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    search_count INTEGER DEFAULT 0,
    
    -- علامات التصنيف والعرض
    is_featured BOOLEAN DEFAULT false,
    is_best_selling BOOLEAN DEFAULT false,
    is_available BOOLEAN DEFAULT true,
    is_on_sale BOOLEAN DEFAULT false,
    is_new BOOLEAN DEFAULT true,
    new_until TIMESTAMPTZ DEFAULT NOW() + INTERVAL '10 days',
    
    -- بيانات تحسين محركات البحث (SEO)
    meta_title VARCHAR(255),
    meta_description TEXT,
    tags TEXT[],
    
    -- طوابع زمنية
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء والبحث - محسنة ومنظمة
-- فهارس أساسية للبحث والفرز
CREATE INDEX IF NOT EXISTS idx_products_name ON products USING gin(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING gin(tags);

-- فهرس فريد للـ SKU (يغني عن idx_products_sku العادي)
CREATE UNIQUE INDEX IF NOT EXISTS idx_products_sku_unique 
ON products(sku) INCLUDE (name, price, is_available);

-- فهارس مركبة محسنة للاستعلامات الشائعة
-- يغني عن idx_products_category_id و idx_products_category_price
CREATE INDEX IF NOT EXISTS idx_products_category_composite 
ON products(category_id, is_available, price) 
INCLUDE (name, stock_quantity);

-- يغني عن idx_products_company_id و idx_products_company_available
CREATE INDEX IF NOT EXISTS idx_products_company_composite 
ON products(company_id, is_available) 
INCLUDE (name, price);

-- يغني عن idx_products_is_featured و idx_products_featured_available
CREATE INDEX IF NOT EXISTS idx_products_featured_composite 
ON products(is_featured, is_available) 
INCLUDE (name, price, created_at)
WHERE is_featured = true;

-- يغني عن idx_products_is_new و idx_products_new_until و idx_products_new_available
CREATE INDEX IF NOT EXISTS idx_products_new_composite 
ON products(is_new, is_available, new_until) 
INCLUDE (name, price, created_at)
WHERE is_new = true;

-- فهارس للتصنيف والترتيب
-- يغني عن idx_products_bestsellers و idx_products_sales_count
CREATE INDEX IF NOT EXISTS idx_products_bestsellers 
ON products(sales_count DESC, view_count DESC) 
WHERE is_available = true;

-- يغني عن idx_products_trending و idx_products_view_count
CREATE INDEX IF NOT EXISTS idx_products_trending 
ON products(view_count DESC, created_at DESC) 
WHERE is_available = true;

-- فهارس للمخزون والتنبيهات
CREATE INDEX IF NOT EXISTS idx_products_stock_reorder 
ON products(stock_quantity, min_stock_level) 
INCLUDE (name, sku)
WHERE is_available = true;

-- فهرس للبحث بالسعر - يغني عن idx_products_price و idx_products_price_range
CREATE INDEX IF NOT EXISTS idx_products_price_optimized 
ON products(price, is_available) 
INCLUDE (name, category_id);

-- فهرس البحث البسيط بالتشابه
CREATE INDEX IF NOT EXISTS idx_products_search_similarity ON products USING gin(
    (COALESCE(name, '') || ' ' || COALESCE(brand, '')) gin_trgm_ops
);


-- ===================================================================
-- 4. جدول مشاهدات المنتجات
-- ===================================================================
-- جدول تقييمات المنتجات
CREATE TABLE IF NOT EXISTS product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT false,
    is_approved BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_user_id ON product_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_rating ON product_reviews(rating);
-- فهرس مركب مفقود للمراجعات المعتمدة
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_approved ON product_reviews(product_id, is_approved);
-- فهرس لتحسين البحث في المراجعات المعتمدة فقط
CREATE INDEX IF NOT EXISTS idx_product_reviews_approved_rating ON product_reviews(is_approved, rating) WHERE is_approved = true;


-- ===================================================================
-- جدول مشاهدات المنتجات (مُقسم شهرياً لتحسين الأداء)
CREATE TABLE IF NOT EXISTS product_views (
    id UUID DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    session_id VARCHAR(100),
    view_duration_seconds INTEGER,
    referrer_source VARCHAR(100), -- 'search', 'category', 'recommendation', 'direct'
    device_type VARCHAR(20),
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- إنشاء تقسيمات شهرية للأشهر الحالية والقادمة
CREATE TABLE IF NOT EXISTS product_views_y2024m12 PARTITION OF product_views
    FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

CREATE TABLE IF NOT EXISTS product_views_y2025m01 PARTITION OF product_views
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE IF NOT EXISTS product_views_y2025m02 PARTITION OF product_views
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE IF NOT EXISTS product_views_y2025m03 PARTITION OF product_views
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

-- دالة لإنشاء تقسيمات شهرية تلقائياً
CREATE OR REPLACE FUNCTION create_monthly_partition()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    start_date DATE;
    end_date DATE;
    table_name TEXT;
BEGIN
    start_date := date_trunc('month', NEW.created_at);
    end_date := start_date + INTERVAL '1 month';
    table_name := 'product_views_y' || 
                  to_char(start_date, 'YYYY') || 'm' || 
                  to_char(start_date, 'MM');
    
    -- إنشاء التقسيم إذا لم يكن موجوداً
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF product_views
                    FOR VALUES FROM (%L) TO (%L)',
                   table_name, start_date, end_date);
    
    RETURN NEW;
END;
$$;


-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_product_views_product_id ON product_views(product_id);
CREATE INDEX IF NOT EXISTS idx_product_views_user_id ON product_views(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_product_views_created_at ON product_views(created_at);
-- فهرس للجلسات المجهولة
CREATE INDEX IF NOT EXISTS idx_product_views_session ON product_views(session_id) WHERE user_id IS NULL;
-- فهرس مركب لتحليل سلوك المستخدم
CREATE INDEX IF NOT EXISTS idx_product_views_user_date ON product_views(user_id, created_at) WHERE user_id IS NOT NULL;


-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة تحديث عدد المنتجات في الفئة - محسنة مع معالجة أفضل للأخطاء
CREATE OR REPLACE FUNCTION update_category_product_count()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_old_category_id UUID;
    v_new_category_id UUID;
    v_affected_rows INTEGER;
BEGIN
    -- التحقق من وجود جدول الفئات
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'categories') THEN
        RAISE WARNING 'جدول الفئات غير موجود، تم تجاهل تحديث العداد';
        RETURN COALESCE(NEW, OLD);
    END IF;

    -- معالجة الإدراج
    IF TG_OP = 'INSERT' THEN
        v_new_category_id := NEW.category_id;
        
        -- التحقق من وجود الفئة
        IF v_new_category_id IS NULL THEN
            RAISE WARNING 'معرف الفئة فارغ، تم تجاهل تحديث العداد';
            RETURN NEW;
        END IF;
        
        -- تحديث العداد بأمان
        BEGIN
            UPDATE categories SET products_count = GREATEST(products_count + 1, 1) 
            WHERE id = v_new_category_id
            RETURNING 1 INTO v_affected_rows;
            
            IF v_affected_rows IS NULL OR v_affected_rows = 0 THEN
                RAISE WARNING 'الفئة برقم % غير موجودة، تم تجاهل تحديث العداد', v_new_category_id;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'خطأ عند تحديث عداد المنتجات للفئة %: %', v_new_category_id, SQLERRM;
        END;
        
        RETURN NEW;
    END IF;

    -- معالجة التحديث
    IF TG_OP = 'UPDATE' THEN
        v_old_category_id := OLD.category_id;
        v_new_category_id := NEW.category_id;
        
        -- التحقق من تغيير الفئة
        IF v_old_category_id IS DISTINCT FROM v_new_category_id THEN
            -- تخفيض العداد في الفئة القديمة
            IF v_old_category_id IS NOT NULL THEN
                BEGIN
                    UPDATE categories SET products_count = GREATEST(products_count - 1, 0) 
                    WHERE id = v_old_category_id
                    RETURNING 1 INTO v_affected_rows;
                    
                    IF v_affected_rows IS NULL OR v_affected_rows = 0 THEN
                        RAISE WARNING 'الفئة القديمة برقم % غير موجودة، تم تجاهل تخفيض العداد', v_old_category_id;
                    END IF;
                EXCEPTION WHEN OTHERS THEN
                    RAISE WARNING 'خطأ عند تخفيض عداد المنتجات للفئة %: %', v_old_category_id, SQLERRM;
                END;
            END IF;
            
            -- زيادة العداد في الفئة الجديدة
            IF v_new_category_id IS NOT NULL THEN
                BEGIN
                    UPDATE categories SET products_count = GREATEST(products_count + 1, 1) 
                    WHERE id = v_new_category_id
                    RETURNING 1 INTO v_affected_rows;
                    
                    IF v_affected_rows IS NULL OR v_affected_rows = 0 THEN
                        RAISE WARNING 'الفئة الجديدة برقم % غير موجودة، تم تجاهل زيادة العداد', v_new_category_id;
                    END IF;
                EXCEPTION WHEN OTHERS THEN
                    RAISE WARNING 'خطأ عند زيادة عداد المنتجات للفئة %: %', v_new_category_id, SQLERRM;
                END;
            END IF;
        END IF;
        
        RETURN NEW;
    END IF;

    -- معالجة الحذف
    IF TG_OP = 'DELETE' THEN
        v_old_category_id := OLD.category_id;
        
        -- التحقق من وجود الفئة
        IF v_old_category_id IS NULL THEN
            RAISE WARNING 'معرف الفئة فارغ، تم تجاهل تحديث العداد';
            RETURN OLD;
        END IF;
        
        -- تخفيض العداد بأمان
        BEGIN
            UPDATE categories SET products_count = GREATEST(products_count - 1, 0) 
            WHERE id = v_old_category_id
            RETURNING 1 INTO v_affected_rows;
            
            IF v_affected_rows IS NULL OR v_affected_rows = 0 THEN
                RAISE WARNING 'الفئة برقم % غير موجودة، تم تجاهل تخفيض العداد', v_old_category_id;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'خطأ عند تخفيض عداد المنتجات للفئة %: %', v_old_category_id, SQLERRM;
        END;
        
        RETURN OLD;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$;

-- دالة تحديث إحصائيات المنتج
CREATE OR REPLACE FUNCTION update_product_stats()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- تم حذف تحديث عدد المراجعات ومتوسط التقييم

        -- تحديث عدد المبيعات
        IF TG_TABLE_NAME = 'order_items' THEN
            UPDATE products SET
                sales_count = sales_count + NEW.quantity
            WHERE id = NEW.product_id;
        END IF;
        RETURN NEW;
    END IF;

    IF TG_OP = 'UPDATE' THEN
        -- تم حذف تحديث إحصائيات المراجعات عند التحديث
        RETURN NEW;
    END IF;

    IF TG_OP = 'DELETE' THEN
        -- تم حذف تحديث الإحصائيات عند الحذف
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$;

-- دالة إرسال إشعارات المنتجات الجديدة
CREATE OR REPLACE FUNCTION notify_new_product()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_user RECORD;
    v_product_name VARCHAR;
    v_company_name VARCHAR;
    v_category_name VARCHAR;
    v_notification_count INTEGER := 0;
BEGIN
    -- التأكد من أن المنتج جديد ومتاح
    IF NEW.is_new = true AND NEW.is_available = true THEN
        -- الحصول على بيانات المنتج
        SELECT NEW.name INTO v_product_name;

        SELECT c.name INTO v_company_name
        FROM companies c WHERE c.id = NEW.company_id;

        SELECT cat.name INTO v_category_name
        FROM categories cat WHERE cat.id = NEW.category_id;

        -- التحقق من وجود الجداول المطلوبة قبل إرسال الإشعارات
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') AND
           EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_settings') THEN

            -- إرسال إشعار لجميع المستخدمين الذين فعلوا إشعارات المنتجات الجديدة
            FOR v_user IN (
                SELECT p.id, p.name
                FROM profiles p
                INNER JOIN notification_settings ns ON p.id = ns.user_id
                WHERE p.profile_type = 'customer'
                AND ns.new_products = true
                AND ns.push_notifications = true
            ) LOOP
                -- إرسال الإشعار
                BEGIN
                    PERFORM send_notification(
                        v_user.id,
                        'new_product',
                        jsonb_build_object(
                            'product_name', v_product_name,
                            'company_name', v_company_name,
                            'category_name', v_category_name,
                            'product_id', NEW.id::text,
                            'price', NEW.price::text
                        )
                    );
                    v_notification_count := v_notification_count + 1;
                EXCEPTION
                    WHEN OTHERS THEN
                        -- تجاهل الأخطاء في إرسال الإشعارات
                        NULL;
                END;
            END LOOP;
        END IF;

        -- تسجيل في السجل
        RAISE NOTICE 'تم إرسال % إشعار للمنتج الجديد: %', v_notification_count, v_product_name;
    END IF;

    RETURN NEW;
END;
$$;

-- دالة تحديث حالة المنتجات الجديدة (إزالة علامة "جديد" بعد انتهاء المدة)
CREATE OR REPLACE FUNCTION update_new_products_status()
RETURNS TABLE (
    updated_count INTEGER,
    cleanup_enabled BOOLEAN,
    next_cleanup TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_updated_count INTEGER := 0;
    v_cleanup_enabled BOOLEAN;
    v_cleanup_interval INTEGER;
    v_next_cleanup TIMESTAMPTZ;
BEGIN
    -- التحقق من تفعيل التنظيف التلقائي
    BEGIN
        SELECT
            CASE WHEN setting_value = 1 THEN true ELSE false END
        INTO v_cleanup_enabled
        FROM new_product_settings
        WHERE setting_name = 'auto_cleanup_enabled' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_enabled := NULL;
    END;

    -- إذا لم توجد الإعدادات، استخدم القيم الافتراضية
    IF v_cleanup_enabled IS NULL THEN
        v_cleanup_enabled := true;
    END IF;

    -- تنفيذ التنظيف إذا كان مفعلاً
    IF v_cleanup_enabled THEN
        -- تحديث المنتجات التي انتهت مدة كونها "جديدة"
        UPDATE products
        SET is_new = false, updated_at = NOW()
        WHERE is_new = true
        AND new_until < NOW();

        GET DIAGNOSTICS v_updated_count = ROW_COUNT;

        -- تسجيل عملية التنظيف
        BEGIN
            INSERT INTO system_logs (
                log_type, message, details, created_at
            ) VALUES (
                'auto_cleanup',
                'تم تنظيف المنتجات الجديدة تلقائياً',
                jsonb_build_object(
                    'updated_products', v_updated_count,
                    'cleanup_time', NOW()
                ),
                NOW()
            );
        EXCEPTION
            WHEN OTHERS THEN
                -- تجاهل الخطأ إذا لم يكن جدول system_logs موجود
                NULL;
        END;
    END IF;

    -- حساب موعد التنظيف التالي
    BEGIN
        SELECT setting_value INTO v_cleanup_interval
        FROM new_product_settings
        WHERE setting_name = 'cleanup_interval_hours' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_interval := NULL;
    END;

    IF v_cleanup_interval IS NULL THEN
        v_cleanup_interval := 24;
    END IF;

    v_next_cleanup := NOW() + (v_cleanup_interval || ' hours')::INTERVAL;

    -- إرجاع النتائج
    updated_count := v_updated_count;
    cleanup_enabled := v_cleanup_enabled;
    next_cleanup := v_next_cleanup;
    RETURN NEXT;
END;
$$;


-- ===================================================================
-- دوال التنظيف التلقائي للمنتجات الجديدة
-- ===================================================================

-- دالة التنظيف التلقائي البديل (بدون pg_cron)
CREATE OR REPLACE FUNCTION auto_cleanup_new_products()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_last_cleanup TIMESTAMPTZ;
    v_cleanup_interval INTEGER;
    v_should_cleanup BOOLEAN := false;
BEGIN
    -- التحقق من وجود الجداول المطلوبة
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_logs') THEN
        RETURN NEW;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'new_product_settings') THEN
        RETURN NEW;
    END IF;

    -- الحصول على آخر عملية تنظيف
    BEGIN
        SELECT MAX(created_at) INTO v_last_cleanup
        FROM system_logs
        WHERE log_type = 'auto_cleanup';
    EXCEPTION
        WHEN OTHERS THEN
            v_last_cleanup := NULL;
    END;

    -- الحصول على فترة التنظيف (افتراضي 24 ساعة)
    v_cleanup_interval := 24;

    BEGIN
        SELECT setting_value INTO v_cleanup_interval
        FROM new_product_settings
        WHERE setting_name = 'cleanup_interval_hours' AND is_active = true;
    EXCEPTION
        WHEN OTHERS THEN
            v_cleanup_interval := 24;
    END;

    -- التحقق من الحاجة للتنظيف
    IF v_last_cleanup IS NULL OR
       v_last_cleanup < NOW() - (v_cleanup_interval || ' hours')::INTERVAL THEN
        v_should_cleanup := true;
    END IF;

    -- تنفيذ التنظيف إذا كان مطلوباً
    IF v_should_cleanup THEN
        -- تحديث المنتجات التي انتهت مدة كونها "جديدة"
        UPDATE products
        SET is_new = false, updated_at = NOW()
        WHERE is_new = true
        AND new_until < NOW();

        -- تسجيل عملية التنظيف إذا كان الجدول موجود
        BEGIN
            INSERT INTO system_logs (
                log_type, message, details, created_at
            ) VALUES (
                'auto_cleanup',
                'تم تنظيف المنتجات الجديدة تلقائياً',
                jsonb_build_object(
                    'cleanup_time', NOW(),
                    'trigger_source', 'auto_cleanup_new_products'
                ),
                NOW()
            );
        EXCEPTION
            WHEN OTHERS THEN
                -- تجاهل الخطأ إذا لم يكن جدول system_logs موجود
                NULL;
        END;
    END IF;

    RETURN NEW;
END;
$$;


-- ===================================================================
-- 5. جدول تنبيهات المخزون
-- ===================================================================
CREATE TABLE IF NOT EXISTS inventory_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    alert_type VARCHAR(20) NOT NULL CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock')),
    threshold_value INTEGER,
    current_value INTEGER,
    message TEXT,
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_product_id ON inventory_alerts(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_type ON inventory_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_resolved ON inventory_alerts(is_resolved);
CREATE INDEX IF NOT EXISTS idx_inventory_alerts_unresolved ON inventory_alerts(product_id, is_resolved) WHERE is_resolved = false;