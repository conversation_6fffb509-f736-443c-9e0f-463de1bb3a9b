import 'package:flutter/foundation.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/models/cart_item_model.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// نموذج عنصر السلة المحسن للاستخدام الداخلي في خدمة السلة
/// يستخدم هذا النموذج لتسهيل التعامل مع المنتجات في السلة
class CartServiceItem {
  final String id;
  final ProductModel product;
  int quantity;
  final DateTime addedAt;
  final Map<String, dynamic>? selectedOptions;
  bool selected;

  CartServiceItem({
    required this.id,
    required this.product,
    required this.quantity,
    required this.addedAt,
    this.selectedOptions,
    this.selected = false,
  });

  double get totalPrice => product.price * quantity;

  /// تحويل CartServiceItem إلى CartItemModel
  CartItemModel toCartItemModel() {
    return CartItemModel(
      id: id,
      userId: '', // سيتم تعيينه لاحقًا
      productId: product.id,
      productName: product.name,
      productImage: product.mainImage ?? 'assets/images/default_product.png',
      price: product.price,
      discountPrice: product.discountPrice,
      quantity: quantity,
      createdAt: addedAt,
      updatedAt: DateTime.now(),
      selected: selected,
    );
  }

  /// إنشاء CartServiceItem من CartItemModel والمنتج
  static CartServiceItem fromCartItemAndProduct(
    CartItemModel cartItem,
    ProductModel product,
  ) {
    return CartServiceItem(
      id: cartItem.id,
      product: product,
      quantity: cartItem.quantity,
      addedAt: cartItem.createdAt,
      selectedOptions: null, // لا يوجد في CartItemModel
      selected: cartItem.selected,
    );
  }

  /// إنشاء CartServiceItem من بيانات Supabase
  factory CartServiceItem.fromSupabaseData(Map<String, dynamic> json) {
    return CartServiceItem(
      id: json['id'] as String,
      product: ProductModel.fromJson(
        json['products'],
      ), // Supabase يرجع كائن 'products' متداخل
      quantity: json['quantity'],
      addedAt: DateTime.parse(
        json['created_at'],
      ), // استخدام 'created_at' من Supabase
      selectedOptions: json['selected_options'],
      selected: json['selected'] ?? false,
    );
  }

  /// تحويل CartServiceItem إلى Map لحفظه في Supabase
  Map<String, dynamic> toSupabaseData() => {
    'id': id,
    'product_id': product.id, // تخزين معرف المنتج بدلاً من كائن المنتج كاملاً
    'quantity': quantity,
    'selected_options': selectedOptions,
    'selected': selected,
  };

  /// تحويل CartServiceItem إلى Map لتصديره كـ JSON
  Map<String, dynamic> toJson() => {
    'id': id,
    'product': product.toJson(),
    'quantity': quantity,
    'added_at': addedAt.toIso8601String(),
    'selected_options': selectedOptions,
    'selected': selected,
  };
}

/// خدمة السلة المحسنة والسريعة
class CartService extends ChangeNotifier {
  static final CartService _instance = CartService._internal();
  factory CartService() => _instance;
  CartService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final AuthSupabaseService _authService = AuthSupabaseService();
  final List<CartServiceItem> _items = [];
  bool _isInitialized = false;

  // Getters
  List<CartServiceItem> get items => List.unmodifiable(_items);
  int get itemCount => _items.length;
  int get totalQuantity => _items.fold(0, (sum, item) => sum + item.quantity);
  double get totalPrice =>
      _items.fold(0.0, (sum, item) => sum + item.totalPrice);
  bool get isEmpty => _items.isEmpty;
  bool get isNotEmpty => _items.isNotEmpty;
  bool get isInitialized => _isInitialized;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _fetchCartFromSupabase();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة السلة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة السلة: $e');
      _isInitialized = true; // تهيئة حتى مع وجود خطأ
    }
  }

  /// جلب السلة من Supabase
  Future<void> _fetchCartFromSupabase() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        debugPrint(
          '❌ المستخدم غير مسجل الدخول، لا يمكن جلب السلة من Supabase.',
        );
        return;
      }

      final response = await _supabase
          .from(AppConstants.cartItemsTable)
          .select(
            '*, ${AppConstants.productsTable}(*)',
          ) // جلب بيانات المنتج المرتبطة
          .eq('user_id', userId);

      _items.clear();
      for (final itemData in response) {
        // Supabase يرجع المنتج ككائن متداخل، نحتاج لتحويله إلى ProductModel
        final productData =
            itemData[AppConstants.productsTable] as Map<String, dynamic>;
        final product = ProductModel.fromJson(productData);

        _items.add(
          CartServiceItem(
            id: itemData['id'] as String,
            product: product,
            quantity: itemData['quantity'],
            addedAt: DateTime.parse(
              itemData['created_at'],
            ), // نفترض أن created_at هو وقت الإضافة
            selectedOptions:
                itemData['selected_options'], // إذا كان لديك هذا العمود
            selected: itemData['selected'] ?? false,
          ),
        );
      }
      debugPrint('📦 تم تحميل ${_items.length} عنصر من السلة من Supabase');
    } catch (e) {
      debugPrint('❌ خطأ في جلب السلة من Supabase: $e');
    }
  }

  /// حفظ السلة في Supabase (هذه الدالة لن تستخدم مباشرة بعد الآن، سيتم التعامل معها في addToCart, updateQuantity, removeFromCart)

  Future<void> addToCart(
    ProductModel product, {
    int quantity = 1,
    Map<String, dynamic>? selectedOptions,
  }) async {
    final userId = _authService.currentUser?.id;
    if (userId == null) {
      debugPrint('❌ لا يمكن إضافة المنتج إلى السلة، المستخدم غير مسجل الدخول.');
      return;
    }

    try {
      // التحقق مما إذا كان المنتج موجودًا بالفعل في سلة المستخدم في Supabase
      final existingCartItemResponse =
          await _supabase
              .from(AppConstants.cartItemsTable)
              .select('*')
              .eq('user_id', userId)
              .eq('product_id', product.id)
              .maybeSingle();

      if (existingCartItemResponse != null) {
        // تحديث الكمية إذا كان المنتج موجودًا بالفعل
        final currentQuantity = existingCartItemResponse['quantity'] as int;
        final newQuantity = currentQuantity + quantity;
        await _supabase
            .from(AppConstants.cartItemsTable)
            .update({'quantity': newQuantity})
            .eq('id', existingCartItemResponse['id']);

        // تحديث القائمة المحلية
        final localItemIndex = _items.indexWhere(
          (item) => item.id == existingCartItemResponse['id'],
        );
        if (localItemIndex != -1) {
          _items[localItemIndex].quantity = newQuantity;
        }
        debugPrint(
          '🛒 تم تحديث كمية ${product.name} في السلة إلى $newQuantity.',
        );
      } else {
        // إضافة منتج جديد إلى Supabase
        final newCartItemResponse =
            await _supabase
                .from(AppConstants.cartItemsTable)
                .insert({
                  'user_id': userId,
                  'product_id': product.id,
                  'quantity': quantity,
                  'selected_options': selectedOptions, // إضافة الخيارات المحددة
                })
                .select()
                .single();

        // إضافة المنتج إلى القائمة المحلية
        _items.add(
          CartServiceItem(
            id: newCartItemResponse['id'] as String,
            product: product,
            quantity: newCartItemResponse['quantity'],
            addedAt: DateTime.parse(
              newCartItemResponse['created_at'],
            ), // استخدام created_at
            selectedOptions: newCartItemResponse['selected_options'],
          ),
        );
        debugPrint('🛒 تم إضافة ${product.name} إلى السلة. الكمية: $quantity');
      }
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في إضافة/تحديث المنتج في السلة في Supabase: $e');
    }
  }

  Future<void> updateQuantity(String itemId, int newQuantity) async {
    final userId = _authService.currentUser?.id;
    if (userId == null) {
      debugPrint('❌ لا يمكن تحديث الكمية، المستخدم غير مسجل الدخول.');
      return;
    }

    try {
      if (newQuantity > 0) {
        await _supabase
            .from(AppConstants.cartItemsTable)
            .update({'quantity': newQuantity})
            .eq('id', itemId)
            .eq('user_id', userId);

        final localItemIndex = _items.indexWhere((item) => item.id == itemId);
        if (localItemIndex != -1) {
          _items[localItemIndex].quantity = newQuantity;
        }
        notifyListeners();
        debugPrint(
          '✅ تم تحديث كمية العنصر $itemId إلى $newQuantity في Supabase',
        );
      } else {
        await removeFromCart(itemId);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث كمية العنصر $itemId في Supabase: $e');
    }
  }

  Future<void> removeFromCart(String itemId) async {
    final userId = _authService.currentUser?.id;
    if (userId == null) {
      debugPrint('❌ لا يمكن حذف العنصر، المستخدم غير مسجل الدخول.');
      return;
    }

    try {
      await _supabase
          .from(AppConstants.cartItemsTable)
          .delete()
          .eq('id', itemId)
          .eq('user_id', userId);

      _items.removeWhere((item) => item.id == itemId);
      notifyListeners();
      debugPrint('✅ تم حذف العنصر $itemId من السلة في Supabase');
    } catch (e) {
      debugPrint('❌ خطأ في حذف العنصر $itemId من السلة في Supabase: $e');
    }
  }

  Future<void> clearCart() async {
    final userId = _authService.currentUser?.id;
    if (userId == null) {
      debugPrint('❌ لا يمكن مسح السلة، المستخدم غير مسجل الدخول.');
      return;
    }

    try {
      await _supabase
          .from(AppConstants.cartItemsTable)
          .delete()
          .eq('user_id', userId);

      _items.clear();
      notifyListeners();
      debugPrint('✅ تم مسح السلة في Supabase');
    } catch (e) {
      debugPrint('❌ خطأ في مسح السلة في Supabase: $e');
    }
  }

  /// التحقق من وجود منتج في السلة
  bool isInCart(String productId) {
    return _items.any((item) => item.product.id == productId);
  }

  /// الحصول على كمية منتج معين
  int getProductQuantity(String productId) {
    try {
      final item = _items.firstWhere((item) => item.product.id == productId);
      return item.quantity;
    } catch (e) {
      // المنتج غير موجود في السلة
      return 0;
    }
  }

  /// الحصول على عنصر من السلة
  CartServiceItem? getCartItem(String itemId) {
    try {
      return _items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  /// تطبيق كوبون خصم
  double applyDiscount(String couponCode) {
    // يمكن تطوير هذه الدالة لاحقاً
    switch (couponCode.toUpperCase()) {
      case 'SAVE10':
        return totalPrice * 0.1;
      case 'SAVE20':
        return totalPrice * 0.2;
      default:
        return 0.0;
    }
  }

  /// حساب الضرائب
  double calculateTax() {
    return totalPrice * 0.15; // 15% ضريبة
  }

  /// حساب رسوم الشحن
  double calculateShipping() {
    if (totalPrice > 500) return 0.0; // شحن مجاني فوق 500
    return 50.0; // رسوم شحن ثابتة
  }

  /// المجموع النهائي
  double getFinalTotal({String? couponCode}) {
    double total = totalPrice;

    if (couponCode != null) {
      total -= applyDiscount(couponCode);
    }

    total += calculateTax();
    total += calculateShipping();

    return total;
  }

  /// إحصائيات السلة
  Map<String, dynamic> getCartStatistics() {
    return {
      'itemCount': itemCount,
      'totalQuantity': totalQuantity,
      'totalPrice': totalPrice,
      'averageItemPrice': itemCount > 0 ? totalPrice / itemCount : 0.0,
      'oldestItem':
          _items.isNotEmpty
              ? _items
                  .reduce((a, b) => a.addedAt.isBefore(b.addedAt) ? a : b)
                  .addedAt
              : null,
      'newestItem':
          _items.isNotEmpty
              ? _items
                  .reduce((a, b) => a.addedAt.isAfter(b.addedAt) ? a : b)
                  .addedAt
              : null,
    };
  }

  /// تصدير السلة كـ JSON
  Map<String, dynamic> exportCart() {
    return {
      'items': _items.map((item) => item.toJson()).toList(),
      'statistics': getCartStatistics(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  /// استيراد السلة من JSON
  Future<void> importCart(Map<String, dynamic> cartData) async {
    try {
      _items.clear();

      if (cartData['items'] != null) {
        for (final item in cartData['items']) {
          try {
            final productData = item['product'] as Map<String, dynamic>;
            final product = ProductModel.fromJson(productData);

            _items.add(
              CartServiceItem(
                id: item['id'] as String,
                product: product,
                quantity: item['quantity'] as int,
                addedAt: DateTime.parse(item['added_at'] as String),
                selectedOptions:
                    item['selected_options'] as Map<String, dynamic>?,
                selected: item['selected'] as bool? ?? false,
              ),
            );
          } catch (e) {
            debugPrint('❌ خطأ في استيراد عنصر السلة: $e');
          }
        }
      }

      notifyListeners();
      debugPrint('✅ تم استيراد السلة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في استيراد السلة: $e');
      throw Exception('فشل في استيراد السلة');
    }
  }

  /// تنظيف السلة من المنتجات المنتهية الصلاحية
  Future<void> cleanupExpiredItems() async {
    try {
      final now = DateTime.now();
      final expiredDuration = const Duration(days: 30); // 30 يوم

      _items.removeWhere((item) {
        return now.difference(item.addedAt) > expiredDuration;
      });

      notifyListeners();
      debugPrint('✅ تم تنظيف السلة من العناصر المنتهية الصلاحية');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف السلة: $e');
    }
  }

  /// مزامنة السلة مع قاعدة البيانات
  Future<void> syncCartWithDatabase() async {
    try {
      // يمكن تطوير هذه الدالة لاحقاً للمزامنة مع Supabase
      debugPrint('🔄 تم مزامنة السلة مع قاعدة البيانات');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة السلة: $e');
    }
  }

  /// تبديل تحديد جميع العناصر
  void toggleSelectAll() {
    final allSelected = _items.every((item) => item.selected);
    for (var item in _items) {
      item.selected = !allSelected;
    }
    notifyListeners();
  }

  /// تبديل تحديد عنصر معين
  void toggleItemSelection(String itemId) {
    // يمكن تطوير هذه الدالة لاحقاً
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _items[index].selected = !_items[index].selected;
    }
    notifyListeners();
  }

  /// حذف العناصر المحددة
  Future<void> removeSelectedItems() async {
    final userId = _authService.currentUser?.id;
    if (userId == null) {
      debugPrint('❌ لا يمكن حذف العناصر المحددة، المستخدم غير مسجل الدخول.');
      return;
    }

    try {
      final selectedItems = _items.where((item) => item.selected).toList();
      if (selectedItems.isEmpty) return;

      for (final item in selectedItems) {
        await _supabase
            .from('cart_items')
            .delete()
            .eq('id', item.id)
            .eq('user_id', userId);
      }

      _items.removeWhere((item) => item.selected);
      notifyListeners();
      debugPrint('✅ تم حذف ${selectedItems.length} عنصر محدد من السلة');
    } catch (e) {
      debugPrint('❌ خطأ في حذف العناصر المحددة من السلة: $e');
    }
  }

  /// المجموع (alias للتوافق)
  double get total => totalPrice;

  @override
  void dispose() {
    _items.clear();
    super.dispose();
  }
}
