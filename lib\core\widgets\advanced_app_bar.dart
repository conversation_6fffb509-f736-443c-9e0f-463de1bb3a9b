import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/theme/gradients.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';


class _AnimatedScaleOnTap extends StatefulWidget {
  final Widget child;
  const _AnimatedScaleOnTap({required this.child});
  @override
  State<_AnimatedScaleOnTap> createState() => _AnimatedScaleOnTapState();
}

class _AnimatedScaleOnTapState extends State<_AnimatedScaleOnTap> {
  bool _pressed = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => _pressed = true),
      onTapUp: (_) => setState(() => _pressed = false),
      onTapCancel: () => setState(() => _pressed = false),
      child: AnimatedScale(
        scale: _pressed ? 0.92 : 1.0,
        duration: const Duration(milliseconds: 120),
        curve: Curves.easeOut,
        child: widget.child,
      ),
    );
  }
}


class AdvancedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final bool showBackButton;
  final List<Widget>? actions;
  final Widget? leading;
  final PreferredSizeWidget? bottom;
  final double elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final LinearGradient? gradient;
  final bool useGlassEffect;
  final bool showBadge;
  final String? badgeText;
  final VoidCallback? onTitleTap;
  final bool centerTitle;
  final bool roundedCorners;
  final Widget? customTitle;
  final bool showSearchButton;
  final VoidCallback? onSearchPressed;
  final bool showNotificationButton;
  final int notificationCount;
  final VoidCallback? onNotificationPressed;
  final bool showLogo;
  final Widget? logo;
  final bool expandedHeight;
  final Widget? background;

  const AdvancedAppBar({
    super.key,
    this.title = '',
    this.subtitle,
    this.showBackButton = true,
    this.actions,
    this.leading,
    this.bottom,
    this.elevation = 0.0,
    this.backgroundColor,
    this.foregroundColor,
    this.gradient,
    this.useGlassEffect = true,
    this.showBadge = false,
    this.badgeText,
    this.onTitleTap,
    this.centerTitle = false,
    this.roundedCorners = true,
    this.customTitle,
    this.showSearchButton = false,
    this.onSearchPressed,
    this.showNotificationButton = false,
    this.notificationCount = 0,
    this.onNotificationPressed,
    this.showLogo = false,
    this.logo,
    this.expandedHeight = false,
    this.background,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveGradient = AppGradients.blueWaveGradient;

    return PreferredSize(
      preferredSize: preferredSize,
      child: Container(
        decoration: BoxDecoration(
          gradient: effectiveGradient,
          borderRadius:
              roundedCorners
                  ? const BorderRadius.only(
                    bottomLeft: Radius.circular(28),
                    bottomRight: Radius.circular(28),
                  )
                  : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.18),
              blurRadius: 24,
              offset: const Offset(0, 8),
              spreadRadius: 2,
            ),
          ],
        ),
        child: SafeArea(
          child: ClipRRect(
            borderRadius:
                roundedCorners
                    ? const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    )
                    : BorderRadius.zero,
            child: Stack(
              children: [
                // خلفية مخصصة إذا كانت موجودة
                if (background != null) Positioned.fill(child: background!),

                // تأثير زجاجي
                Positioned.fill(
                  child: BackdropFilter(
                    filter:
                        useGlassEffect
                            ? ImageFilter.blur(sigmaX: 10, sigmaY: 10)
                            : ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                    child: Container(color: Colors.transparent),
                  ),
                ),

                // محتوى الشريط
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          // زر الرجوع مع تأثير زجاجي
                          if (showBackButton) _buildBackButton(context),

                          if (showBackButton) const SizedBox(width: 15),

                          // الشعار إذا كان مطلوبًا
                          if (showLogo) ...[
                            logo ?? _buildDefaultLogo(),
                            const SizedBox(width: 12),
                          ],

                          // عنوان الصفحة
                          Expanded(child: customTitle ?? _buildTitle(context)),

                          // أزرار الإجراءات
                          _buildActionButtons(context),
                        ],
                      ),

                      // محتوى إضافي للشريط الموسع
                      if (expandedHeight && bottom == null)
                        const SizedBox(height: 40),

                      // شريط سفلي إضافي
                      if (bottom != null) bottom!,
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء زر الرجوع بتأثير زجاجي
  Widget _buildBackButton(BuildContext context) {
    return Hero(
      tag: 'back_button',
      child: Material(
        color: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                spreadRadius: 0.5,
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new_rounded),
            color: Colors.white,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
      ),
    );
  }

  /// بناء عنوان الصفحة مع النص الفرعي والشارة
  Widget _buildTitle(BuildContext context) {
    return GestureDetector(
      onTap: onTitleTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment:
            centerTitle ? CrossAxisAlignment.center : CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 350),
                transitionBuilder:
                    (child, anim) =>
                        FadeTransition(opacity: anim, child: child),
                child: Text(
                  title,
                  key: ValueKey(title),
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      ResponsiveHelper.isMobile(context) ? 22 : 26,
                    ),
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              ),
              if (showBadge) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.secondaryColor.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    badgeText ?? 'جديد',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 350),
              transitionBuilder:
                  (child, anim) => FadeTransition(opacity: anim, child: child),
              child: Text(
                subtitle!,
                key: ValueKey(subtitle),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: ResponsiveHelper.getFontSize(
                    context,
                    ResponsiveHelper.isMobile(context) ? 13 : 15,
                  ),
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context) {
    final List<Widget> actionWidgets = [];

    // زر البحث
    if (showSearchButton) {
      actionWidgets.add(
        buildGlassButton(
          icon: Icons.search_rounded,
          tooltip: 'بحث',
          onPressed: onSearchPressed ?? () {},
        ),
      );
      actionWidgets.add(const SizedBox(width: 10));
    }

    // زر الإشعارات
    if (showNotificationButton) {
      actionWidgets.add(
        Stack(
          clipBehavior: Clip.none,
          children: [
            buildGlassButton(
              icon: Icons.notifications_rounded,
              tooltip: 'الإشعارات',
              onPressed: onNotificationPressed ?? () {},
            ),
            if (notificationCount > 0)
              Positioned(
                top: -5,
                right: -5,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.errorColor.withOpacity(0.5),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 18,
                    minHeight: 18,
                  ),
                  child: Center(
                    child: Text(
                      notificationCount > 9 ? '9+' : '$notificationCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
      actionWidgets.add(const SizedBox(width: 10));
    }

    // أزرار إضافية
    if (actions != null && actions!.isNotEmpty) {
      actionWidgets.addAll(
        actions!.map((action) {
          return Padding(
            padding: const EdgeInsets.only(left: 8),
            child: action,
          );
        }).toList(),
      );
    }

    return Row(mainAxisSize: MainAxisSize.min, children: actionWidgets);
  }

  /// شعار متحرك احترافي
  Widget _buildDefaultLogo() {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Center(
        child: ClipOval(
          child: SizedBox(
            width: 36,
            height: 36,
            child: Image.asset(
              'assets/images/motorcycle.png',
              fit: BoxFit.contain,
              errorBuilder:
                  (context, error, stackTrace) => Icon(
                    Icons.motorcycle_rounded,
                    color: AppTheme.primaryColor,
                    size: 28,
                  ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء زر زجاجي للشريط العلوي
  static Widget buildGlassButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    Color? iconColor,
    double size = 24,
  }) {
    return _AnimatedScaleOnTap(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              spreadRadius: 0.5,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(15),
            onTap: onPressed,
            child: Container(
              padding: const EdgeInsets.all(12),
              child: Icon(icon, color: iconColor ?? Colors.white, size: size),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize {
    double height = kToolbarHeight;

    // إضافة ارتفاع للنص الفرعي
    if (subtitle != null) {
      height += 15;
    }

    // إضافة ارتفاع للشريط السفلي
    if (bottom != null) {
      height += bottom!.preferredSize.height;
    }

    // إضافة ارتفاع إضافي للشريط الموسع
    if (expandedHeight) {
      height += 40;
    }

    return Size.fromHeight(height);
  }
}
