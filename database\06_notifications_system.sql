-- ===================================================================
-- نظام التنبيهات والإشعارات
-- 

-- ===================================================================
-- 1. جدول أنواع الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type_key VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    default_enabled BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    category VARCHAR(50) DEFAULT 'general',
    icon VARCHAR(50),
    color VARCHAR(7), -- لون hex
    priority INTEGER DEFAULT 1, -- 1: منخفضة, 2: متوسطة, 3: عالية
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_types_type_key ON notification_types(type_key);
CREATE INDEX IF NOT EXISTS idx_notification_types_is_active ON notification_types(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_types_category ON notification_types(category);

-- ===================================================================
-- 2. جدول قوالب الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    title_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    variables JSONB DEFAULT '[]'::jsonb, -- متغيرات القالب
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_templates_name ON notification_templates(name);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(type);
CREATE INDEX IF NOT EXISTS idx_notification_templates_is_active ON notification_templates(is_active);

-- ===================================================================
-- 3. جدول الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    template_id UUID REFERENCES notification_templates(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    sent_at TIMESTAMPTZ,
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read, created_at) WHERE is_read = false;
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority, created_at) WHERE priority IN ('high', 'urgent');
-- فهرس مفقود لحالة التسليم
CREATE INDEX IF NOT EXISTS idx_notifications_delivery_status ON notifications(delivery_status);
-- فهرس مركب للإشعارات المرسلة
CREATE INDEX IF NOT EXISTS idx_notifications_status_date ON notifications(delivery_status, created_at);

-- ===================================================================
-- 4. جدول إحصائيات الإشعارات للمستخدمين
-- ===================================================================
CREATE TABLE IF NOT EXISTS user_notification_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    total_notifications INTEGER DEFAULT 0,
    unread_notifications INTEGER DEFAULT 0,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_read INTEGER DEFAULT 0,
    total_clicked INTEGER DEFAULT 0,
    last_notification_at TIMESTAMPTZ,
    last_notification_sent TIMESTAMPTZ,
    last_notification_read TIMESTAMPTZ,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_notification_stats_user_id ON user_notification_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notification_stats_total_notifications ON user_notification_stats(total_notifications);
CREATE INDEX IF NOT EXISTS idx_user_notification_stats_unread_notifications ON user_notification_stats(unread_notifications);
CREATE INDEX IF NOT EXISTS idx_user_notification_stats_last_updated ON user_notification_stats(last_updated);

-- ===================================================================
-- 5. جدول تحليلات الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('sent', 'delivered', 'opened', 'clicked', 'dismissed')),
    event_timestamp TIMESTAMPTZ DEFAULT NOW(),
    device_info JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_analytics_notification_id ON notification_analytics(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_type ON notification_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_event_timestamp ON notification_analytics(event_timestamp);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة إرسال إشعار
CREATE OR REPLACE FUNCTION send_notification(
    p_user_id UUID,
    p_template_name VARCHAR,
    p_variables JSONB DEFAULT '{}'::jsonb,
    p_custom_title VARCHAR DEFAULT NULL,
    p_custom_body TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_template RECORD;
    v_title TEXT;
    v_body TEXT;
    v_notification_id UUID;
    v_variable_key TEXT;
    v_variable_value TEXT;
BEGIN
    -- الحصول على قالب الإشعار
    SELECT * INTO v_template
    FROM notification_templates
    WHERE name = p_template_name AND is_active = true;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'قالب الإشعار غير موجود: %', p_template_name;
    END IF;

    -- استخدام العنوان والنص المخصص أو من القالب
    v_title := COALESCE(p_custom_title, v_template.title_template);
    v_body := COALESCE(p_custom_body, v_template.body_template);

    -- استبدال المتغيرات في العنوان والنص
    FOR v_variable_key IN SELECT jsonb_object_keys(p_variables)
    LOOP
        v_variable_value := p_variables->>v_variable_key;
        v_title := REPLACE(v_title, '{{' || v_variable_key || '}}', v_variable_value);
        v_body := REPLACE(v_body, '{{' || v_variable_key || '}}', v_variable_value);
    END LOOP;

    -- إنشاء الإشعار
    INSERT INTO notifications (
        user_id, template_id, title, body, type, data
    ) VALUES (
        p_user_id, v_template.id, v_title, v_body, v_template.type, p_variables
    ) RETURNING id INTO v_notification_id;

    -- تحديث إحصائيات المستخدم
    INSERT INTO user_notification_stats (
        user_id,
        total_sent,
        total_notifications,
        unread_notifications,
        last_notification_sent,
        last_notification_at
    )
    VALUES (p_user_id, 1, 1, 1, NOW(), NOW())
    ON CONFLICT (user_id) DO UPDATE SET
        total_sent = user_notification_stats.total_sent + 1,
        total_notifications = user_notification_stats.total_notifications + 1,
        unread_notifications = user_notification_stats.unread_notifications + 1,
        last_notification_sent = NOW(),
        last_notification_at = NOW(),
        last_updated = NOW(),
        updated_at = NOW();

    -- تحديث عداد الإشعارات في الملف الشخصي (إذا كانت الحقول موجودة)
    BEGIN
        UPDATE profiles SET
            total_notifications_count = COALESCE(total_notifications_count, 0) + 1,
            unread_notifications_count = COALESCE(unread_notifications_count, 0) + 1
        WHERE id = p_user_id;
    EXCEPTION
        WHEN undefined_column THEN
            -- تجاهل الخطأ إذا لم تكن الحقول موجودة
            NULL;
    END;

    RETURN v_notification_id;
END;
$$;
-- ===================================================================

-- دالة تحديث حالة قراءة الإشعار
CREATE OR REPLACE FUNCTION mark_notification_read(
    p_notification_id UUID,
    p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_row_count INTEGER;
BEGIN
    -- تحديث حالة الإشعار
    UPDATE notifications
    SET is_read = true, read_at = NOW()
    WHERE id = p_notification_id AND user_id = p_user_id AND is_read = false;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;

    IF v_row_count > 0 THEN
        -- تحديث عداد الإشعارات غير المقروءة
        BEGIN
            UPDATE profiles SET
                unread_notifications_count = GREATEST(COALESCE(unread_notifications_count, 0) - 1, 0),
                last_notification_check = NOW()
            WHERE id = p_user_id;
        EXCEPTION
            WHEN undefined_column THEN
                -- تجاهل الخطأ إذا لم تكن الحقول موجودة
                NULL;
        END;

        -- تحديث إحصائيات المستخدم
        UPDATE user_notification_stats SET
            total_read = total_read + 1,
            unread_notifications = GREATEST(unread_notifications - 1, 0),
            last_notification_read = NOW(),
            last_updated = NOW(),
            updated_at = NOW()
        WHERE user_id = p_user_id;

        -- تسجيل حدث القراءة
        INSERT INTO notification_analytics (notification_id, event_type)
        VALUES (p_notification_id, 'opened');

        RETURN true;
    END IF;

    RETURN false;
END;
$$;
-- ===================================================================

-- دالة تنظيف الإشعارات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- حذف الإشعارات المقروءة الأقدم من 6 أشهر
    DELETE FROM notifications
    WHERE is_read = true AND created_at < NOW() - INTERVAL '6 months';

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    -- حذف تحليلات الإشعارات الأقدم من سنة
    DELETE FROM notification_analytics
    WHERE created_at < NOW() - INTERVAL '1 year';

    RETURN v_deleted_count;
END;
$$;

-- ===================================================================
-- دالة تحديث إحصائيات الإشعارات للمستخدم
-- ===================================================================
CREATE OR REPLACE FUNCTION update_user_notification_stats(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_total_count INTEGER;
    v_unread_count INTEGER;
    v_last_notification TIMESTAMPTZ;
BEGIN
    -- حساب الإحصائيات من جدول الإشعارات
    SELECT
        COUNT(*),
        COUNT(*) FILTER (WHERE NOT is_read),
        MAX(created_at)
    INTO v_total_count, v_unread_count, v_last_notification
    FROM notifications
    WHERE user_id = p_user_id;

    -- تحديث أو إدراج الإحصائيات
    INSERT INTO user_notification_stats (
        user_id,
        total_notifications,
        unread_notifications,
        last_notification_at,
        last_updated
    ) VALUES (
        p_user_id,
        COALESCE(v_total_count, 0),
        COALESCE(v_unread_count, 0),
        v_last_notification,
        NOW()
    )
    ON CONFLICT (user_id) DO UPDATE SET
        total_notifications = COALESCE(v_total_count, 0),
        unread_notifications = COALESCE(v_unread_count, 0),
        last_notification_at = v_last_notification,
        last_updated = NOW(),
        updated_at = NOW();
END;
$$;


-- ===================================================================


DO $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN SELECT id FROM profiles LOOP
        PERFORM update_user_notification_stats(user_record.id);
    END LOOP;
    RAISE NOTICE '📊 تم تحديث إحصائيات الإشعارات لجميع المستخدمين';
END $$;

