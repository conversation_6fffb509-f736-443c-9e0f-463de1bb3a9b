import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/core/utils/service_locator.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:path_provider/path_provider.dart';

// تعريف أخطاء مخصصة
class ImageRecognitionException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  ImageRecognitionException(this.message, {this.code, this.originalError});

  @override
  String toString() =>
      'ImageRecognitionException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// نموذج نتائج تحليل الصورة
class ImageAnalysisResult {
  final String imageId;
  final List<String> keywords;
  final Map<String, double> categoryScores;
  final DateTime timestamp;
  final String? imagePath;
  final int? imageSize;
  final String? imageHash;

  ImageAnalysisResult({
    required this.imageId,
    required this.keywords,
    required this.categoryScores,
    required this.timestamp,
    this.imagePath,
    this.imageSize,
    this.imageHash,
  });

  Map<String, dynamic> toJson() => {
    'imageId': imageId,
    'keywords': keywords,
    'categoryScores': categoryScores,
    'timestamp': timestamp.toIso8601String(),
    'imagePath': imagePath,
    'imageSize': imageSize,
    'imageHash': imageHash,
  };

  factory ImageAnalysisResult.fromJson(Map<String, dynamic> json) {
    return ImageAnalysisResult(
      imageId: json['imageId'],
      keywords: List<String>.from(json['keywords']),
      categoryScores: Map<String, double>.from(json['categoryScores']),
      timestamp: DateTime.parse(json['timestamp']),
      imagePath: json['imagePath'],
      imageSize: json['imageSize'],
      imageHash: json['imageHash'],
    );
  }
}

/// خدمة التعرف على الصور المحسنة
/// توفر هذه الخدمة وظائف متقدمة للتعامل مع الصور وتحليلها والبحث عن المنتجات
/// مع دعم التخزين المؤقت المتقدم والمعالجة المتوازية
class ImageRecognitionService {
  static final ImageRecognitionService _instance =
      ImageRecognitionService._internal();
  late final ProductService _productService;
  final ImagePicker _imagePicker = ImagePicker();
  final _storage = ServiceLocator.storage;

  // إعدادات الأمان والأداء
  static const int _maxFileSizeMB = 8;
  static const List<String> _allowedExtensions = [
    'jpg',
    'jpeg',
    'png',
    'webp',
    'heic',
  ];
  static const int _minImageDimension = 100;
  static const int _maxImageDimension = 3000;
  static const int _maxImagesPerBatch = 10;
  static const int _compressionQuality = 85;
  // حجم الصورة المصغرة للعرض - سيتم استخدامه في واجهة المستخدم لاحقاً
  // تم تعليق هذا المتغير لأنه غير مستخدم حالياً
  // static const int _thumbnailSize = 300;

  // مفاتيح التخزين المؤقت
  static const String _cacheKeyPrefix = 'image_recognition_';
  static const String _analysisResultsKey =
      '${_cacheKeyPrefix}analysis_results';
  static const String _imageHashesKey = '${_cacheKeyPrefix}image_hashes';
  static const String _recentSearchesKey = '${_cacheKeyPrefix}recent_searches';

  // إعدادات التخزين المؤقت
  static const Duration _cacheDuration = Duration(days: 7);
  static const int _maxCachedResults = 100;
  static const int _maxRecentSearches = 20;

  // مسار التخزين المؤقت للصور
  String? _tempImageDir;

  // حالة المعالجة
  bool _isProcessing = false;
  bool _isInitialized = false;
  String? _error;
  int _pendingOperations = 0;

  // إحصائيات
  int _totalProcessedImages = 0;
  int _cacheHits = 0;
  int _cacheMisses = 0;

  // قائمة الكلمات المفتاحية المحسنة للمنتجات
  static const Map<String, List<String>> _keywordsByCategory = {
    'engine': [
      'محرك',
      'engine',
      'سلندر',
      'cylinder',
      'كباس',
      'piston',
      'صمام',
      'valve',
      'زيت',
      'oil',
      'فلتر',
      'filter',
      'كاربراتير',
      'carburetor',
      'حقن',
      'injection',
      'تبريد',
      'cooling',
    ],
    'brakes': [
      'فرامل',
      'brake',
      'قرص',
      'disc',
      'حذاء',
      'shoe',
      'سائل',
      'fluid',
      'اسطوانة',
      'cylinder',
      'ليفر',
      'lever',
      'هوز',
      'hose',
    ],
    'electrical': [
      'كهرباء',
      'electrical',
      'بطارية',
      'battery',
      'شمعة',
      'spark plug',
      'دينامو',
      'alternator',
      'سلك',
      'wire',
      'فيوز',
      'fuse',
      'لمبة',
      'bulb',
      'سينسور',
      'sensor',
    ],
    'body': [
      'هيكل',
      'body',
      'مقعد',
      'seat',
      'مرآة',
      'mirror',
      'مقود',
      'handlebar',
      'فيرنج',
      'fairing',
      'خزان',
      'tank',
      'شاسيه',
      'chassis',
    ],
    'wheels': [
      'عجلة',
      'wheel',
      'إطار',
      'tire',
      'جنط',
      'rim',
      'محور',
      'axle',
      'بلف',
      'valve',
      'تيوب',
      'tube',
      'بلبرنج',
      'bearing',
    ],
    'transmission': [
      'ناقل حركة',
      'transmission',
      'كلتش',
      'clutch',
      'جير',
      'gear',
      'سلسلة',
      'chain',
      'سبروكت',
      'sprocket',
      'بكرة',
      'pulley',
    ],
    'suspension': [
      'تعليق',
      'suspension',
      'شوكة',
      'fork',
      'امتصاص',
      'shock',
      'نابض',
      'spring',
      'مساعد',
      'damper',
    ],
  };

  // Getters
  bool get isProcessing => _isProcessing || _pendingOperations > 0;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  int get totalProcessedImages => _totalProcessedImages;
  int get cacheHits => _cacheHits;
  int get cacheMisses => _cacheMisses;
  double get cacheHitRate =>
      _totalProcessedImages > 0 ? _cacheHits / _totalProcessedImages : 0.0;

  factory ImageRecognitionService(ProductService productService) {
    _instance._productService = productService;
    return _instance;
  }

  ImageRecognitionService._internal();

  /// تهيئة الخدمة مع إعداد التخزين المؤقت
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة خدمة التخزين الموحدة
      await _storage.initialize();

      // إنشاء دليل مؤقت للصور (إذا كان متاح)
      try {
        final tempDir = await getTemporaryDirectory();
        _tempImageDir = '${tempDir.path}/image_recognition_cache';
        final cacheDir = Directory(_tempImageDir!);
        if (!await cacheDir.exists()) {
          await cacheDir.create(recursive: true);
        }
      } catch (e) {
        debugPrint('⚠️ تحذير: فشل في إنشاء دليل التخزين المؤقت: $e');
        _tempImageDir = null; // سيعمل بدون تخزين مؤقت للملفات
      }

      // تحميل إحصائيات التخزين المؤقت
      _loadCacheStats();

      // تنظيف الذاكرة المؤقتة القديمة
      await _cleanupCache();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة التعرف على الصور بنجاح');
      debugPrint('📂 مسار التخزين المؤقت: $_tempImageDir');
    } catch (e) {
      _error = 'خطأ في تهيئة خدمة التعرف على الصور: $e';
      debugPrint('❌ Error initializing image recognition service: $e');
      // السماح بالاستمرار حتى مع وجود خطأ
      _isInitialized = true;
    }
  }

  /// تحميل إحصائيات التخزين المؤقت
  void _loadCacheStats() {
    try {
      final statsJson = _storage.getString('${_cacheKeyPrefix}stats');
      if (statsJson != null) {
        final stats = jsonDecode(statsJson) as Map<String, dynamic>;
        _totalProcessedImages = stats['totalProcessedImages'] ?? 0;
        _cacheHits = stats['cacheHits'] ?? 0;
        _cacheMisses = stats['cacheMisses'] ?? 0;
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في تحميل إحصائيات التخزين المؤقت: $e');
    }
  }

  /// حفظ إحصائيات التخزين المؤقت
  Future<void> _saveCacheStats() async {
    try {
      final stats = {
        'totalProcessedImages': _totalProcessedImages,
        'cacheHits': _cacheHits,
        'cacheMisses': _cacheMisses,
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      await _storage.setJson('${_cacheKeyPrefix}stats', stats);
    } catch (e) {
      debugPrint('⚠️ خطأ في حفظ إحصائيات التخزين المؤقت: $e');
    }
  }

  /// التحقق من صحة ملف الصورة مع تحسينات الأداء
  Future<void> _validateImageFile(File imageFile) async {
    if (!await imageFile.exists()) {
      throw ImageRecognitionException(
        'ملف الصورة غير موجود',
        code: 'FILE_NOT_FOUND',
      );
    }

    // التحقق من حجم الملف
    final fileSize = await imageFile.length();
    final fileSizeMB = fileSize / (1024 * 1024);
    if (fileSizeMB > _maxFileSizeMB) {
      throw ImageRecognitionException(
        'حجم الصورة يتجاوز الحد الأقصى ($_maxFileSizeMB ميجابايت)',
        code: 'FILE_TOO_LARGE',
      );
    }

    // التحقق من امتداد الملف
    final extension = imageFile.path.split('.').last.toLowerCase();
    if (!_allowedExtensions.contains(extension)) {
      throw ImageRecognitionException(
        'امتداد الملف غير مدعوم. الامتدادات المدعومة: $_allowedExtensions',
        code: 'INVALID_EXTENSION',
      );
    }

    // التحقق من أبعاد الصورة باستخدام معالجة متوازية
    final bytes = await imageFile.readAsBytes();
    final image = await compute(_decodeImage, bytes);

    if (image.width < _minImageDimension || image.height < _minImageDimension) {
      throw ImageRecognitionException(
        'أبعاد الصورة صغيرة جداً. الحد الأدنى هو $_minImageDimension بكسل',
        code: 'IMAGE_TOO_SMALL',
      );
    }

    if (image.width > _maxImageDimension || image.height > _maxImageDimension) {
      throw ImageRecognitionException(
        'أبعاد الصورة كبيرة جداً. الحد الأقصى هو $_maxImageDimension بكسل',
        code: 'IMAGE_TOO_LARGE',
      );
    }

    // التحقق من سلامة الصورة
    await _checkImageSafety(imageFile, bytes);
  }

  /// فك ترميز الصورة في معالج منفصل
  static Future<ui.Image> _decodeImage(Uint8List bytes) {
    final completer = Completer<ui.Image>();
    ui.decodeImageFromList(bytes, (result) {
      completer.complete(result);
    });
    return completer.future;
  }

  /// التحقق من سلامة الصورة مع تحسينات الأمان
  Future<String> _checkImageSafety(File imageFile, [Uint8List? bytes]) async {
    try {
      // استخدام البيانات المقدمة أو قراءة الملف
      final imageBytes = bytes ?? await imageFile.readAsBytes();

      // حساب البصمة باستخدام معالجة متوازية
      final hash = await compute(_calculateHash, imageBytes);

      // تحميل قائمة البصمات المحظورة
      final blockedHashes =
          _storage.getStringList('${_cacheKeyPrefix}blocked_hashes') ?? [];

      // التحقق من البصمة
      if (blockedHashes.contains(hash)) {
        throw ImageRecognitionException(
          'تم اكتشاف صورة محظورة',
          code: 'BLOCKED_IMAGE',
        );
      }

      // حفظ البصمة في قائمة البصمات المعروفة
      final knownHashes = _storage.getJson(_imageHashesKey) ?? {};
      knownHashes[hash] = {
        'path': imageFile.path,
        'size': imageBytes.length,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // تقليص حجم قائمة البصمات إذا تجاوزت الحد
      if (knownHashes.length > _maxCachedResults * 2) {
        final sortedEntries =
            knownHashes.entries.toList()..sort((a, b) {
              final aTime = DateTime.parse(
                (a.value as Map)['timestamp'] as String,
              );
              final bTime = DateTime.parse(
                (b.value as Map)['timestamp'] as String,
              );
              return bTime.compareTo(aTime); // الأحدث أولاً
            });

        // الاحتفاظ بأحدث البصمات فقط
        final newHashes = <String, dynamic>{};
        for (var i = 0; i < _maxCachedResults; i++) {
          if (i >= sortedEntries.length) break;
          newHashes[sortedEntries[i].key] = sortedEntries[i].value;
        }

        await _storage.setJson(_imageHashesKey, newHashes);
      } else {
        await _storage.setJson(_imageHashesKey, knownHashes);
      }

      return hash;
    } catch (e) {
      if (e is ImageRecognitionException) rethrow;

      throw ImageRecognitionException(
        'فشل في التحقق من سلامة الصورة: $e',
        code: 'SAFETY_CHECK_FAILED',
        originalError: e,
      );
    }
  }

  /// حساب بصمة الصورة في معالج منفصل
  static String _calculateHash(Uint8List bytes) {
    return sha256.convert(bytes).toString();
  }

  /// التقاط صورة من الكاميرا أو المعرض مع تحسينات الأداء
  Future<File?> captureImage({
    ImageSource source = ImageSource.camera,
    int? maxWidth,
    int? maxHeight,
    int? imageQuality,
    bool optimizeImage = true,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      _error = null;
      _isProcessing = true;
      _pendingOperations++;

      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        imageQuality: imageQuality ?? _compressionQuality,
        maxWidth: maxWidth?.toDouble() ?? 1600,
        maxHeight: maxHeight?.toDouble() ?? 1600,
        preferredCameraDevice:
            source == ImageSource.camera
                ? CameraDevice.rear
                : CameraDevice.front,
      );

      if (pickedFile == null) return null;

      final imageFile = File(pickedFile.path);

      // التحقق من صحة الصورة
      await _validateImageFile(imageFile);

      // تحسين الصورة إذا كان مطلوباً
      if (optimizeImage) {
        return await _optimizeImage(imageFile);
      }

      return imageFile;
    } catch (e) {
      _error = 'فشل في التقاط الصورة: $e';
      debugPrint('❌ Error capturing image: $e');
      return null;
    } finally {
      _pendingOperations--;
      _isProcessing = false;
    }
  }

  /// تحسين الصورة وضغطها
  Future<File> _optimizeImage(File imageFile) async {
    try {
      // إنشاء اسم ملف فريد للصورة المحسنة
      final originalPath = imageFile.path;
      final extension = originalPath.split('.').last.toLowerCase();
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // إذا لم يكن التخزين المؤقت متاح، إرجاع الملف الأصلي
      if (_tempImageDir == null) {
        return imageFile;
      }

      final optimizedPath = '$_tempImageDir/optimized_$timestamp.$extension';

      // قراءة بيانات الصورة
      final bytes = await imageFile.readAsBytes();

      // ضغط الصورة باستخدام معالجة متوازية
      final optimizedBytes = await compute(_compressImage, {
        'bytes': bytes,
        'quality': _compressionQuality,
        'maxWidth': 1600,
        'maxHeight': 1600,
      });

      // حفظ الصورة المحسنة
      final optimizedFile = File(optimizedPath);
      await optimizedFile.writeAsBytes(optimizedBytes);

      debugPrint(
        '✅ تم تحسين الصورة: ${originalPath.split('/').last} -> ${optimizedPath.split('/').last}',
      );
      debugPrint(
        '📊 حجم الصورة: ${(bytes.length / 1024).toStringAsFixed(2)} كيلوبايت -> ${(optimizedBytes.length / 1024).toStringAsFixed(2)} كيلوبايت',
      );

      return optimizedFile;
    } catch (e) {
      debugPrint('⚠️ فشل في تحسين الصورة: $e');
      return imageFile; // إرجاع الصورة الأصلية في حالة الفشل
    }
  }

  /// ضغط الصورة في معالج منفصل
  static Future<Uint8List> _compressImage(Map<String, dynamic> params) async {
    final bytes = params['bytes'] as Uint8List;
    final quality = params['quality'] as int;
    final maxWidth = params['maxWidth'] as int;
    final maxHeight = params['maxHeight'] as int;

    // فك ترميز الصورة
    final codec = await ui.instantiateImageCodec(
      bytes,
      targetWidth: maxWidth,
      targetHeight: maxHeight,
    );
    final frame = await codec.getNextFrame();
    final image = frame.image;

    // تحويل الصورة إلى بيانات PNG
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();

    // تحرير الموارد
    image.dispose();

    return pngBytes;
  }

  /// التقاط صور متعددة من المعرض مع تحسينات الأداء
  Future<List<File>> captureMultipleImages({
    int? maxWidth,
    int? maxHeight,
    int? imageQuality,
    bool optimizeImages = true,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      _error = null;
      _isProcessing = true;
      _pendingOperations++;

      final List<XFile> pickedFiles = await _imagePicker.pickMultiImage(
        imageQuality: imageQuality ?? _compressionQuality,
        maxWidth: maxWidth?.toDouble() ?? 1600,
        maxHeight: maxHeight?.toDouble() ?? 1600,
      );

      if (pickedFiles.isEmpty) return [];

      // معالجة الصور بشكل متوازي
      final futures = <Future<File?>>[];
      for (final pickedFile in pickedFiles.take(_maxImagesPerBatch)) {
        futures.add(_processPickedFile(pickedFile, optimizeImages));
      }

      // انتظار اكتمال جميع العمليات
      final results = await Future.wait(futures);

      // تصفية النتائج الفارغة
      final validFiles = results.whereType<File>().toList();

      debugPrint(
        '✅ تم معالجة ${validFiles.length} من ${pickedFiles.length} صورة',
      );
      return validFiles;
    } catch (e) {
      _error = 'فشل في التقاط الصور: $e';
      debugPrint('❌ Error capturing multiple images: $e');
      return [];
    } finally {
      _pendingOperations--;
      _isProcessing = false;
    }
  }

  /// معالجة ملف صورة تم التقاطه
  Future<File?> _processPickedFile(XFile pickedFile, bool optimize) async {
    try {
      final imageFile = File(pickedFile.path);
      await _validateImageFile(imageFile);

      if (optimize) {
        return await _optimizeImage(imageFile);
      }

      return imageFile;
    } catch (e) {
      debugPrint('⚠️ تم تخطي صورة غير صالحة: $e');
      return null;
    }
  }

  /// تنظيف الذاكرة المؤقتة القديمة
  Future<void> _cleanupCache() async {
    try {
      if (_tempImageDir == null) return;

      final cacheDir = Directory(_tempImageDir!);
      if (!await cacheDir.exists()) return;

      // حذف الملفات القديمة
      final now = DateTime.now();
      final files = await cacheDir.list().toList();

      int deletedCount = 0;
      for (final entity in files) {
        if (entity is File) {
          final stat = await entity.stat();
          final fileAge = now.difference(stat.modified);

          if (fileAge > _cacheDuration) {
            await entity.delete();
            deletedCount++;
          }
        }
      }

      if (deletedCount > 0) {
        debugPrint('🧹 تم حذف $deletedCount ملف قديم من الذاكرة المؤقتة');
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في تنظيف الذاكرة المؤقتة: $e');
    }
  }

  /// تحليل صورة للبحث عن المنتجات المشابهة
  Future<ImageAnalysisResult> analyzeImage(File imageFile) async {
    if (!_isInitialized) await initialize();

    try {
      _isProcessing = true;
      _pendingOperations++;
      _totalProcessedImages++;

      // حساب بصمة الصورة للتخزين المؤقت
      final imageHash = await _checkImageSafety(imageFile);

      // التحقق من وجود نتائج محللة مسبقاً في الذاكرة المؤقتة
      final cachedResults = _storage.getJson(_analysisResultsKey) ?? {};

      if (cachedResults.containsKey(imageHash)) {
        _cacheHits++;
        final cachedResult = ImageAnalysisResult.fromJson(
          cachedResults[imageHash] as Map<String, dynamic>,
        );

        debugPrint(
          '🔍 تم استخدام نتائج تحليل مخزنة مؤقتاً للصورة: ${imageHash.substring(0, 8)}',
        );
        await _saveCacheStats();

        return cachedResult;
      }

      _cacheMisses++;

      // تحليل الصورة واستخراج الكلمات المفتاحية
      final keywords = await _extractKeywords(imageFile);

      // حساب درجات التصنيف
      final categoryScores = _calculateCategoryScores(keywords);

      // إنشاء معرف فريد للصورة
      final imageId =
          'img_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(10000)}';

      // إنشاء نتيجة التحليل
      final result = ImageAnalysisResult(
        imageId: imageId,
        keywords: keywords,
        categoryScores: categoryScores,
        timestamp: DateTime.now(),
        imagePath: imageFile.path,
        imageSize: await imageFile.length(),
        imageHash: imageHash,
      );

      // تخزين النتائج في الذاكرة المؤقتة
      cachedResults[imageHash] = result.toJson();

      // تقليص حجم الذاكرة المؤقتة إذا تجاوزت الحد
      if (cachedResults.length > _maxCachedResults) {
        final sortedEntries =
            cachedResults.entries.toList()..sort((a, b) {
              final aTime = DateTime.parse(
                (a.value as Map<String, dynamic>)['timestamp'] as String,
              );
              final bTime = DateTime.parse(
                (b.value as Map<String, dynamic>)['timestamp'] as String,
              );
              return aTime.compareTo(bTime); // الأقدم أولاً
            });

        // حذف أقدم النتائج
        for (var i = 0; i < sortedEntries.length - _maxCachedResults; i++) {
          cachedResults.remove(sortedEntries[i].key);
        }
      }

      await _storage.setJson(_analysisResultsKey, cachedResults);
      await _saveCacheStats();

      debugPrint('✅ تم تحليل الصورة بنجاح: ${keywords.length} كلمة مفتاحية');

      return result;
    } catch (e) {
      _error = 'فشل في تحليل الصورة: $e';
      debugPrint('❌ Error analyzing image: $e');
      rethrow;
    } finally {
      _pendingOperations--;
      _isProcessing = false;
    }
  }

  /// استخراج الكلمات المفتاحية من الصورة
  Future<List<String>> _extractKeywords(File imageFile) async {
    // محاكاة استخراج الكلمات المفتاحية من الصورة
    // في التطبيق الحقيقي، يمكن استخدام خدمة تعرف على الصور مثل Google Vision API

    // اختيار كلمات مفتاحية عشوائية من القائمة المحددة مسبقاً
    final allKeywords = <String>[];
    _keywordsByCategory.forEach((_, keywords) {
      allKeywords.addAll(keywords);
    });

    // اختيار عدد عشوائي من الكلمات المفتاحية
    final random = Random();
    final keywordCount = random.nextInt(10) + 5; // 5-15 كلمة مفتاحية

    final selectedKeywords = <String>[];
    for (var i = 0; i < keywordCount; i++) {
      final index = random.nextInt(allKeywords.length);
      selectedKeywords.add(allKeywords[index]);
    }

    // إضافة تأخير لمحاكاة معالجة الصورة
    await Future.delayed(const Duration(milliseconds: 500));

    return selectedKeywords;
  }

  /// حساب درجات التصنيف بناءً على الكلمات المفتاحية
  Map<String, double> _calculateCategoryScores(List<String> keywords) {
    final scores = <String, double>{};

    // حساب عدد الكلمات المفتاحية لكل فئة
    for (final category in _keywordsByCategory.keys) {
      final categoryKeywords = _keywordsByCategory[category]!;
      int matches = 0;

      for (final keyword in keywords) {
        if (categoryKeywords.contains(keyword)) {
          matches++;
        }
      }

      // حساب الدرجة كنسبة مئوية من إجمالي الكلمات المفتاحية
      final score = matches / keywords.length;
      scores[category] = score;
    }

    return scores;
  }

  /// البحث عن منتجات باستخدام صورة
  Future<List<ProductModel>> searchProductsByImage(File imageFile) async {
    if (!_isInitialized) await initialize();

    try {
      _isProcessing = true;
      _pendingOperations++;

      // تحليل الصورة
      final analysisResult = await analyzeImage(imageFile);

      // استخدام الكلمات المفتاحية للبحث عن المنتجات
      final keywords = analysisResult.keywords;

      // تحديد الفئة الأكثر احتمالاً
      String? topCategory;
      double topScore = 0;

      analysisResult.categoryScores.forEach((category, score) {
        if (score > topScore) {
          topScore = score;
          topCategory = category;
        }
      });

      final searchQuery = keywords.join(' ');

      final products = await _productService.searchProducts(
        searchQuery: searchQuery,
        page: 0,
      );

      _saveRecentImageSearch(analysisResult, products);

      debugPrint(
        '🔍 تم البحث عن المنتجات باستخدام الصورة: ${products.length} نتيجة',
      );

      return products;
    } catch (e) {
      _error = 'فشل في البحث عن المنتجات باستخدام الصورة: $e';
      debugPrint('❌ Error searching products by image: $e');
      return [];
    } finally {
      _pendingOperations--;
      _isProcessing = false;
    }
  }

  Future<void> _saveRecentImageSearch(
    ImageAnalysisResult analysisResult,
    List<ProductModel> products,
  ) async {
    try {
      final searchesData = _storage.getJson(_recentSearchesKey);
      final recentSearches = searchesData?['searches'] as List<dynamic>? ?? [];

      final searchRecord = {
        'imageId': analysisResult.imageId,
        'imageHash': analysisResult.imageHash,
        'timestamp': DateTime.now().toIso8601String(),
        'keywords': analysisResult.keywords,
        'categoryScores': analysisResult.categoryScores,
        'resultCount': products.length,
        'topProductIds': products.take(5).map((p) => p.id).toList(),
      };

      recentSearches.insert(0, searchRecord);

      // تقليص القائمة إذا تجاوزت الحد
      if (recentSearches.length > _maxRecentSearches) {
        recentSearches.removeRange(_maxRecentSearches, recentSearches.length);
      }

      // حفظ القائمة
      final Map<String, dynamic> searchesMap = {'searches': recentSearches};
      await _storage.setJson(_recentSearchesKey, searchesMap);
    } catch (e) {
      debugPrint('⚠️ خطأ في حفظ البحث الأخير: $e');
    }
  }

  /// الحصول على قائمة عمليات البحث الأخيرة
  List<Map<String, dynamic>> getRecentImageSearches() {
    try {
      final searchesData = _storage.getJson(_recentSearchesKey);
      final recentSearches = searchesData?['searches'] as List<dynamic>? ?? [];
      return recentSearches.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('⚠️ خطأ في جلب عمليات البحث الأخيرة: $e');
      return [];
    }
  }

  /// مسح الذاكرة المؤقتة
  Future<void> clearCache() async {
    try {
      _isProcessing = true;

      // مسح بيانات التخزين المؤقت
      await _storage.remove(_analysisResultsKey);
      await _storage.remove(_imageHashesKey);

      // مسح الملفات المؤقتة
      if (_tempImageDir != null) {
        final cacheDir = Directory(_tempImageDir!);
        if (await cacheDir.exists()) {
          await cacheDir.delete(recursive: true);
          await cacheDir.create(recursive: true);
        }
      }

      // إعادة تعيين الإحصائيات
      _totalProcessedImages = 0;
      _cacheHits = 0;
      _cacheMisses = 0;
      await _saveCacheStats();

      debugPrint('🧹 تم مسح الذاكرة المؤقتة لخدمة التعرف على الصور');
    } catch (e) {
      _error = 'فشل في مسح الذاكرة المؤقتة: $e';
      debugPrint('❌ Error clearing cache: $e');
    } finally {
      _isProcessing = false;
    }
  }

  /// الحصول على إحصائيات الذاكرة المؤقتة
  Map<String, dynamic> getCacheStats() {
    return {
      'totalProcessedImages': _totalProcessedImages,
      'cacheHits': _cacheHits,
      'cacheMisses': _cacheMisses,
      'hitRate':
          _totalProcessedImages > 0 ? _cacheHits / _totalProcessedImages : 0.0,
      'cachedResults':
          _storage.getJson(_analysisResultsKey) != null
              ? (_storage.getJson(_analysisResultsKey) as Map).length
              : 0,
      'knownHashes':
          _storage.getJson(_imageHashesKey) != null
              ? (_storage.getJson(_imageHashesKey) as Map).length
              : 0,
      'recentSearches':
          _storage.getJson(_recentSearchesKey) != null
              ? (_storage.getJson(_recentSearchesKey)
                          as Map<String, dynamic>)['searches']
                      ?.length ??
                  0
              : 0,
    };
  }

  /// تحرير الموارد
  Future<void> dispose() async {
    try {
      await _saveCacheStats();
    } catch (e) {
      debugPrint('⚠️ خطأ في تحرير موارد خدمة التعرف على الصور: $e');
    }
  }
}
