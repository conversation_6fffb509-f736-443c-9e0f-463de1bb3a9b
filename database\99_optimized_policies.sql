-- ===================================================================
-- تحسين السياسات الأمنية للأداء العالي
-- Performance Optimized RLS Policies
-- ===================================================================

-- إنشاء دالة محسنة للحصول على معرف المستخدم الحالي
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS UUID
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT auth.uid();
$$;

-- إنشاء دالة محسنة للتحقق من صلاحيات الإدارة
CREATE OR REPLACE FUNCTION is_admin_optimized()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
AS $$
  SELECT COALESCE(
    (SELECT is_admin FROM profiles WHERE id = auth.uid()),
    false
  );
$$;

-- ===================================================================
-- السياسات المحسنة للأداء
-- ===================================================================

-- إسقاط السياسات القديمة وإنشاء محسنة
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    -- إسقاط جميع السياسات الموجودة
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
                      policy_record.policyname, 
                      policy_record.schemaname, 
                      policy_record.tablename);
    END LOOP;
END $$;

-- ===================================================================
-- السياسات المحسنة للجداول الحساسة
-- ===================================================================

-- profiles: محسن للأداء
CREATE POLICY "profiles_optimized" ON profiles FOR ALL 
USING (id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (id = get_current_user_id() OR is_admin_optimized());

-- addresses: محسن للأداء
CREATE POLICY "addresses_optimized" ON addresses FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- notification_settings: محسن للأداء
CREATE POLICY "notification_settings_optimized" ON notification_settings FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_locations: محسن للأداء
CREATE POLICY "user_locations_optimized" ON user_locations FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- orders: محسن للأداء
CREATE POLICY "orders_optimized" ON orders FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- order_items: محسن للأداء مع فهرس
CREATE POLICY "order_items_optimized" ON order_items FOR ALL 
USING (
    EXISTS (
        SELECT 1 FROM orders 
        WHERE orders.id = order_items.order_id 
        AND orders.user_id = get_current_user_id()
    ) OR is_admin_optimized()
) 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM orders 
        WHERE orders.id = order_items.order_id 
        AND orders.user_id = get_current_user_id()
    ) OR is_admin_optimized()
);

-- carts: محسن للأداء
CREATE POLICY "carts_optimized" ON carts FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- cart_items: محسن للأداء
CREATE POLICY "cart_items_optimized" ON cart_items FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- wishlists: محسن للأداء
CREATE POLICY "wishlists_optimized" ON wishlists FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- notifications: محسن للأداء
CREATE POLICY "notifications_optimized" ON notifications FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_notification_stats: محسن للأداء
CREATE POLICY "user_notification_stats_optimized" ON user_notification_stats FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- support_interactions: محسن للأداء
CREATE POLICY "support_interactions_optimized" ON support_interactions FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- coupon_usage: محسن للأداء
CREATE POLICY "coupon_usage_optimized" ON coupon_usage FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- product_reviews: محسن للأداء
CREATE POLICY "product_reviews_optimized" ON product_reviews FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_app_settings: محسن للأداء (هذا هو الجدول المذكور في المشكلة)
CREATE POLICY "user_app_settings_optimized" ON user_app_settings FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_devices: محسن للأداء
CREATE POLICY "user_devices_optimized" ON user_devices FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_privacy_settings: محسن للأداء
CREATE POLICY "user_privacy_settings_optimized" ON user_privacy_settings FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_security_settings: محسن للأداء
CREATE POLICY "user_security_settings_optimized" ON user_security_settings FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- wishlist_collections: محسن للأداء
CREATE POLICY "wishlist_collections_optimized" ON wishlist_collections FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- ai_recommendations: محسن للأداء
CREATE POLICY "ai_recommendations_optimized" ON ai_recommendations FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- search_logs: محسن للأداء
CREATE POLICY "search_logs_optimized" ON search_logs FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- notification_analytics: محسن للأداء مع فهرس
CREATE POLICY "notification_analytics_optimized" ON notification_analytics FOR ALL 
USING (
    EXISTS (
        SELECT 1 FROM notifications 
        WHERE notifications.id = notification_analytics.notification_id 
        AND notifications.user_id = get_current_user_id()
    ) OR is_admin_optimized()
) 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM notifications 
        WHERE notifications.id = notification_analytics.notification_id 
        AND notifications.user_id = get_current_user_id()
    ) OR is_admin_optimized()
);

-- app_events: محسن للأداء
CREATE POLICY "app_events_optimized" ON app_events FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_behavior_analytics: محسن للأداء
CREATE POLICY "user_behavior_analytics_optimized" ON user_behavior_analytics FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- user_sessions: محسن للأداء
CREATE POLICY "user_sessions_optimized" ON user_sessions FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- active_sessions: محسن للأداء
CREATE POLICY "active_sessions_optimized" ON active_sessions FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- product_comparisons: محسن للأداء
CREATE POLICY "product_comparisons_optimized" ON product_comparisons FOR ALL 
USING (user_id = get_current_user_id() OR is_admin_optimized()) 
WITH CHECK (user_id = get_current_user_id() OR is_admin_optimized());

-- wishlist_items: محسن للأداء مع فهرس
CREATE POLICY "wishlist_items_optimized" ON wishlist_items FOR ALL 
USING (
    EXISTS (
        SELECT 1 FROM wishlist_collections 
        WHERE id = collection_id 
        AND user_id = get_current_user_id()
    ) OR is_admin_optimized()
) 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM wishlist_collections 
        WHERE id = collection_id 
        AND user_id = get_current_user_id()
    ) OR is_admin_optimized()
);

-- ===================================================================
-- فهارس إضافية لتحسين الأداء
-- ===================================================================

-- فهارس للجداول المرتبطة بالمستخدمين
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user_id_optimized ON orders(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_id_optimized ON notifications(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlist_collections_user_id_optimized ON wishlist_collections(user_id) WHERE user_id IS NOT NULL;

-- فهارس للعلاقات المعقدة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order_id_optimized ON order_items(order_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notification_analytics_notification_id_optimized ON notification_analytics(notification_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlist_items_collection_id_optimized ON wishlist_items(collection_id);

-- ===================================================================
-- تعليقات وتوثيق
-- ===================================================================

COMMENT ON FUNCTION get_current_user_id() IS 'دالة محسنة للحصول على معرف المستخدم الحالي - تحسن الأداء عبر تجنب إعادة تقييم auth.uid() لكل صف';
COMMENT ON FUNCTION is_admin_optimized() IS 'دالة محسنة للتحقق من صلاحيات الإدارة - تحسن الأداء عبر التخزين المؤقت';

-- تسجيل تطبيق التحسينات
INSERT INTO system_logs (log_level, message, details) 
VALUES ('INFO', 'RLS Policies Optimized', 'Applied performance optimizations to all RLS policies using subqueries and optimized functions');
