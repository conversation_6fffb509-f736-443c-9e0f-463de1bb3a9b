import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:motorcycle_parts_shop/core/constants/app_constants.dart';
import 'package:motorcycle_parts_shop/core/utils/service_locator.dart';
import 'package:motorcycle_parts_shop/models/address_model.dart';
import 'package:motorcycle_parts_shop/models/category_model.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart';
import 'package:motorcycle_parts_shop/models/user/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart'
    show
        AuthChangeEvent,
        AuthException,
        GoTrueClientSignInProvider,
        OAuthProvider,
        OtpType,
        PostgrestException,
        Supabase,
        SupabaseClient,
        User,
        UserAttributes;

/// خدمة المصادقة باستخدام Supabase لإدارة عمليات تسجيل الدخول، التسجيل، وإدارة بيانات المستخدم.
class AuthSupabaseService extends ChangeNotifier {
  static final AuthSupabaseService _instance = AuthSupabaseService._internal();
  late final SupabaseClient _client;
  final _storageService = ServiceLocator.storage;

  bool _isInitialized = false;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _error;
  UserModel? _currentUser;
  DateTime? _lastAuthCheck;
  static const Duration _authCheckInterval = Duration(minutes: 5);
  static const Duration _cacheDuration = Duration(minutes: 5);
  static const Duration _sessionRefreshInterval = Duration(minutes: 4);

  final Map<String, dynamic> _cache = {};
  Timer? _sessionRefreshTimer;
  Timer? _connectionCheckTimer;
  StreamSubscription? _authStateSubscription;

  // Google Sign In
  late GoogleSignIn _googleSignIn;
  GoogleSignInAccount? _googleUser;
  bool _isGoogleSigningIn = false;

  factory AuthSupabaseService() => _instance;

  AuthSupabaseService._internal() {
    _client = Supabase.instance.client;
  }

  bool get isInitialized => _isInitialized;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;
  UserModel? get currentUser => _currentUser;
  SupabaseClient get client => _client;

  // Google Auth getters
  GoogleSignInAccount? get googleUser => _googleUser;
  bool get isGoogleSigningIn => _isGoogleSigningIn;
  bool get isGoogleSignedIn => _googleUser != null;

  /// تهيئة خدمة المصادقة وتكوين الاتصال بـ Supabase.
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('خدمة المصادقة مهيأة بالفعل');
      return;
    }

    try {
      await _storageService.initialize();
      debugPrint('تم تهيئة خدمة التخزين المحلي بنجاح');

      // تهيئة Google Sign In
      _initializeGoogleSignIn();

      // محاولة الاتصال بخادم Supabase مع إعادة المحاولة
      int retryCount = 0;
      const maxRetries = 3;
      const retryDelay = Duration(seconds: 2);

      while (retryCount < maxRetries) {
        try {
          await _checkConnection();
          break; // إذا نجح الاتصال، نخرج من الحلقة
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            debugPrint('فشل الاتصال بخادم Supabase بعد $maxRetries محاولات');
            // نستمر في العملية حتى مع عدم وجود اتصال
            break;
          }
          debugPrint(
            'محاولة الاتصال $retryCount/$maxRetries فشلت، إعادة المحاولة بعد ${retryDelay.inSeconds} ثواني',
          );
          await Future.delayed(retryDelay);
        }
      }

      // محاولة جلب بيانات المستخدم الحالي
      try {
        final authUser = _client.auth.currentUser;
        if (authUser != null) {
          _currentUser = await _fetchUserModel(authUser);
          _isAuthenticated = true;
        }
      } catch (e) {
        debugPrint('فشل جلب بيانات المستخدم: $e');
        // نستمر في العملية حتى مع فشل جلب بيانات المستخدم
      }

      _isInitialized = true;
      notifyListeners();
      debugPrint('تم تهيئة خدمة المصادقة بنجاح');

      // إعداد مراقبة تغييرات حالة المصادقة
      _client.auth.onAuthStateChange.listen(
        (state) => _handleAuthStateChange(state.event),
        onError: (e) => debugPrint('خطأ في مراقبة تغييرات المصادقة: $e'),
      );

      _setupSessionRefresh();
      _setupConnectionCheck();
    } catch (e) {
      _error = 'فشل في تهيئة خدمة المصادقة: $e';
      debugPrint(_error);
      // لا نرمي استثناء هنا للسماح للتطبيق بالعمل في وضع عدم الاتصال
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// التحقق من اتصال الشبكة بخادم Supabase.
  Future<void> _checkConnection() async {
    try {
      // التحقق من وجود جلسة حالية
      final session = _client.auth.currentSession;
      if (session == null) {
        // إذا لم تكن هناك جلسة، نحاول فقط التحقق من الاتصال بالخادم
        await _client
            .from('products')
            .select('count')
            .limit(1)
            .timeout(const Duration(seconds: 5));
        return;
      }

      // إذا كانت هناك جلسة، نتحقق من صلاحيتها
      await _client.auth.getUser().timeout(const Duration(seconds: 5));
    } catch (e) {
      debugPrint('خطأ في الاتصال بخادم Supabase: $e');
      // لا نرمي استثناء هنا للسماح للتطبيق بالعمل في وضع عدم الاتصال
      return;
    }
  }

  /// إعداد فحص دوري للاتصال بالخادم.
  void _setupConnectionCheck() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = Timer.periodic(const Duration(minutes: 1), (
      _,
    ) async {
      if (_isAuthenticated) {
        // فقط نتحقق من الاتصال إذا كان المستخدم مصادقاً
        try {
          await _checkConnection();
        } catch (e) {
          debugPrint('خطأ في التحقق من الاتصال: $e');
        }
      }
    });
  }

  /// جلب بيانات المستخدم من قاعدة البيانات بناءً على بيانات المصادقة.
  Future<UserModel> _fetchUserModel(User authUser) async {
    try {
      final cachedUser = _getCachedUser(authUser.id);
      if (cachedUser != null) {
        return cachedUser;
      }

      final query = _client
          .from(AppConstants.profilesTable)
          .select()
          .eq('id', authUser.id);

      final response = await query.maybeSingle();

      if (response == null) {
        debugPrint('لم يتم العثور على ملف شخصي للمستخدم: ${authUser.id}');
        throw Exception(
          'لم يتم العثور على ملف شخصي. قد يكون هناك مشكلة في إنشاء الملف الشخصي.',
        );
      }

      final user = UserModel.fromJson(response);
      _cacheUser(user);
      return user;
    } catch (e) {
      debugPrint('خطأ في جلب بيانات المستخدم: $e');
      rethrow;
    }
  }

  /// استرجاع بيانات المستخدم من المخبأ إذا كانت صالحة.
  UserModel? _getCachedUser(String userId) {
    final cacheKey = 'user_$userId';
    final cachedData = _cache[cacheKey];

    if (cachedData != null &&
        cachedData['timestamp'] != null &&
        DateTime.now().difference(cachedData['timestamp']) < _cacheDuration) {
      return cachedData['user'];
    }
    return null;
  }

  /// تخزين بيانات المستخدم في المخبأ.
  void _cacheUser(UserModel user) {
    final cacheKey = 'user_${user.id}';
    _cache[cacheKey] = {'user': user, 'timestamp': DateTime.now()};
  }

  /// معالجة تغييرات حالة المصادقة.
  void _handleAuthStateChange(AuthChangeEvent event) async {
    try {
      debugPrint('🔄 معالجة تغيير حالة المصادقة: $event');

      switch (event) {
        case AuthChangeEvent.signedIn:
          final authUser = _client.auth.currentUser;
          if (authUser != null) {
            debugPrint('✅ تسجيل دخول ناجح للمستخدم: ${authUser.email}');
            _currentUser = await _fetchUserModel(authUser);
            _isAuthenticated = true;
            _setupSessionRefresh();

            // إشعار فوري للمستمعين
            notifyListeners();
          }
          break;
        case AuthChangeEvent.signedOut:
          debugPrint('🚪 تسجيل خروج المستخدم');
          _currentUser = null;
          _isAuthenticated = false;
          _sessionRefreshTimer?.cancel();

          // مسح البيانات المحفوظة محلياً
          await _storageService.remove('user');
          _cache.clear();

          // إشعار فوري للمستمعين
          notifyListeners();
          break;
        case AuthChangeEvent.tokenRefreshed:
          debugPrint('🔄 تم تجديد رمز الجلسة');
          final authUser = _client.auth.currentUser;
          if (authUser != null) {
            _currentUser = await _fetchUserModel(authUser);
            // لا نحتاج إشعار المستمعين هنا إلا إذا تغيرت البيانات
          }
          break;
        case AuthChangeEvent.userUpdated:
          debugPrint('👤 تم تحديث بيانات المستخدم');
          final authUser = _client.auth.currentUser;
          if (authUser != null) {
            _currentUser = await _fetchUserModel(authUser);
            notifyListeners();
          }
          break;
        case AuthChangeEvent.passwordRecovery:
          debugPrint('🔑 بدأت عملية استعادة كلمة المرور');
          break;
        default:
          debugPrint('⚠️ حدث تغيير حالة غير مدعوم: $event');
      }

      // إشعار عام للمستمعين (إذا لم يتم إشعارهم بالفعل)
      if (event != AuthChangeEvent.signedIn &&
          event != AuthChangeEvent.signedOut &&
          event != AuthChangeEvent.userUpdated) {
        notifyListeners();
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة تغيير حالة المصادقة: $e');
      _error = 'خطأ في تحديث حالة المصادقة';
      notifyListeners();
    }
  }

  /// التحقق من صلاحية الجلسة وتجديدها إذا لزم الأمر.
  Future<bool> _validateSession() async {
    final session = _client.auth.currentSession;
    if (session == null) return false;

    if (_lastAuthCheck != null &&
        DateTime.now().difference(_lastAuthCheck!) < _authCheckInterval) {
      return true;
    }

    try {
      if (session.isExpired) {
        final refreshedSession = await _client.auth.refreshSession();
        if (refreshedSession.session == null) return false;
        _lastAuthCheck = DateTime.now();
      }
      _lastAuthCheck = DateTime.now();
      return true;
    } catch (e) {
      debugPrint('خطأ في تجديد الجلسة: $e');
      return false;
    }
  }

  /// إعداد تجديد دوري للجلسة.
  void _setupSessionRefresh() {
    _sessionRefreshTimer?.cancel();
    _sessionRefreshTimer = Timer.periodic(_sessionRefreshInterval, (_) async {
      if (_isAuthenticated) {
        final isValid = await _validateSession();
        if (!isValid) {
          _isAuthenticated = false;
          _currentUser = null;
          notifyListeners();
        }
      }
    });
  }

  /// معالجة الطلبات مع التعامل مع الأخطاء.
  Future<T> _handleRequest<T>(
    Future<T> Function() request, {
    String? customError,
  }) async {
    try {
      return await request();
    } on TimeoutException {
      _error = 'انتهت مهلة الطلب. حاول مرة أخرى.';
      throw Exception(_error);
    } on AuthException catch (e) {
      _error = _handleAuthError(e);
      throw Exception(_error);
    } on PostgrestException catch (e) {
      _error = _handlePostgrestError(e);
      throw Exception(_error);
    } catch (e) {
      _error = customError ?? 'حدث خطأ غير متوقع: $e';
      throw Exception(_error);
    }
  }

  /// التحقق من صلاحية البريد الإلكتروني.
  bool _isVerEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  /// التحقق من صلاحية رقم الهاتف.
  bool _isValidPhone(String? phone) {
    if (phone == null || phone.isEmpty) return true;
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-()]'), '');
    return RegExp(r'^\+?[0-9]{8,15}$').hasMatch(cleanPhone);
  }

  /// التحقق من صلاحية كلمة المرور.
  bool _isValidPassword(String password) {
    // كلمة المرور يجب أن تكون 6 أحرف على الأقل
    if (password.length < 6) return false;
    return true;
  }

  /// جلب بيانات المستخدم الحالي.
  Future<void> _fetchUserData() async {
    final authUser = _client.auth.currentUser;
    if (authUser == null) {
      _isAuthenticated = false;
      _error = 'لا يوجد مستخدم حالي';
      return;
    }

    try {
      // استخدام _fetchUserModel بدلاً من getUserById
      _currentUser = await _fetchUserModel(authUser);
      _isAuthenticated = true;
      await _storageService.setJson('user', _currentUser!.toJson());
    } catch (e) {
      _isAuthenticated = false;
      _error = 'لم يتم العثور على ملف شخصي لهذا المستخدم';
      debugPrint('خطأ في جلب بيانات المستخدم: $e');
    }
  }

  /// تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور.
  Future<bool> signInWithEmail(String email, String password) async {
    debugPrint('بدء عملية تسجيل الدخول');
    debugPrint('البريد الإلكتروني: $email');

    if (!isInitialized) {
      _error = 'خدمة المصادقة غير مهيأة';
      return false;
    }

    if (email.isEmpty || password.isEmpty) {
      _error = 'جميع الحقول مطلوبة';
      return false;
    }

    if (!_isVerEmail(email)) {
      _error = 'البريد الإلكتروني غير صالح';
      return false;
    }

    if (!_isValidPassword(password)) {
      _error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      return false;
    }

    try {
      // تسجيل الدخول مباشرة
      final authResponse = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (authResponse.user == null) {
        _error = 'فشل تسجيل الدخول';
        // مسح الجلسة القديمة عند الفشل
        await signOut(forceSignOut: true, clearCache: true);
        return false;
      }

      debugPrint('تم تسجيل الدخول بنجاح');

      // جلب بيانات المستخدم
      await _fetchUserData();

      // حفظ رمز الوصول
      if (authResponse.session != null) {
        await _storageService.setString(
          'access_token',
          authResponse.session!.accessToken,
        );
      }

      // تحديث آخر تسجيل دخول
      if (_currentUser != null) {
        await _updateLastLogin(_currentUser!.id);
      }

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('خطأ في تسجيل الدخول: $e');
      if (e.toString().contains('Invalid login credentials')) {
        _error = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
      } else if (e.toString().contains('Email not confirmed')) {
        _error = 'يرجى تأكيد البريد الإلكتروني أولاً';
      } else {
        _error = 'حدث خطأ أثناء تسجيل الدخول';
      }
      // مسح الجلسة القديمة عند أي خطأ مصادقة
      await signOut(forceSignOut: true, clearCache: true);
      return false;
    }
  }

  /// إرسال رمز OTP إلى البريد الإلكتروني.
  Future<bool> sendEmailOtp(String email, {bool isRegistration = false}) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    _isLoading = true;
    _error = null;

    try {
      debugPrint('محاولة إرسال رمز OTP إلى: $email');

      if (!_isVerEmail(email)) {
        _error = 'البريد الإلكتروني غير صالح';
        debugPrint(_error);
        return false;
      }

      if (!isRegistration) {
        final emailExists = await checkEmailExists(email);
        if (!emailExists) {
          _error = 'البريد الإلكتروني غير مسجل';
          debugPrint(_error);
          return false;
        }
      } else {
        final emailExists = await checkEmailExists(email);
        if (emailExists) {
          _error = 'البريد الإلكتروني مسجل بالفعل';
          debugPrint(_error);
          return false;
        }
      }

      await _client.auth.signInWithOtp(
        email: email,
        emailRedirectTo:
            kIsWeb
                ? '${Uri.base.origin}/auth/callback'
                : 'io.supabase.motorcycleparts://login-callback/',
      );

      debugPrint('تم إرسال رمز التحقق بنجاح إلى: $email');
      return true;
    } on AuthException catch (e) {
      _error = _handleAuthError(e);
      if (e.statusCode == '429') {
        _error =
            'تم تجاوز الحد المسموح لإرسال الرموز. يرجى الانتظار 5 دقائق قبل المحاولة مرة أخرى';
      } else if (e.statusCode == '422') {
        _error = 'البريد الإلكتروني غير صالح أو محظور';
      } else if (e.message.contains('email_not_confirmed') == true) {
        _error = 'يجب تأكيد البريد الإلكتروني أولاً';
      }
      debugPrint('خطأ مصادقة في إرسال رمز التحقق: $_error');
      return false;
    } catch (e) {
      _error = 'فشل إرسال رمز التحقق: $e';
      debugPrint('خطأ غير متوقع في إرسال رمز التحقق: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// التحقق من رمز OTP المرسل إلى البريد الإلكتروني.
  Future<bool> verifyEmailOtp(
    String email,
    String token, {
    String? password,
  }) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    _isLoading = true;
    _error = null;

    try {
      // التحقق من صحة البيانات
      if (!_isVerEmail(email)) {
        _error = 'البريد الإلكتروني غير صالح';
        debugPrint(_error);
        return false;
      }
      if (!RegExp(r'^\d{6}$').hasMatch(token)) {
        _error = 'رمز التحقق يجب أن يكون 6 أرقام';
        debugPrint(_error);
        return false;
      }

      debugPrint('التحقق من رمز OTP للبريد: $email، الرمز: $token');

      // التحقق من الرمز
      final response = await _handleRequest(
        () => _client.auth.verifyOTP(
          email: email,
          token: token,
          type: OtpType.email,
        ),
        customError: 'فشل التحقق من الرمز',
      );

      debugPrint(
        'نتيجة التحقق من الرمز: ${response.session != null ? 'ناجح' : 'فاشل'}',
      );

      if (response.session != null && response.user != null) {
        debugPrint('✅ تم التحقق من OTP بنجاح، بدء تحديث بيانات المستخدم...');

        try {
          // تحديث بيانات المستخدم
          _currentUser = await _fetchUserModel(response.user!);
          _isAuthenticated = true;

          // تحديث آخر تسجيل دخول
          await _updateLastLogin(_currentUser!.id);

          // حفظ رمز الوصول والبيانات
          await Future.wait([
            _storageService.setString(
              'access_token',
              response.session!.accessToken,
            ),
            _storageService.setJson('user', _currentUser!.toJson()),
          ]);

          // حفظ بيانات تسجيل الدخول في الهاتف إذا تم توفير كلمة المرور
          if (password != null && password.isNotEmpty) {
            await _storageService.setJson('login_credentials', {
              'email': email,
              'password': password,
            });
            debugPrint('تم حفظ بيانات تسجيل الدخول في الهاتف');
          }

          // إعداد تحديث الجلسة
          _setupSessionRefresh();

          debugPrint('✅ تم التحقق من OTP وتسجيل الدخول بنجاح لـ: $email');
          debugPrint(
            '👤 المستخدم: ${_currentUser!.name} (${_currentUser!.profileType})',
          );

          // إشعار المستمعين بالتحديث
          notifyListeners();

          return true;
        } catch (e) {
          _error = 'فشل تحميل بيانات المستخدم بعد التحقق: $e';
          debugPrint('❌ $_error');
          _isAuthenticated = false;
        }
      } else {
        _error = 'فشل التحقق من الرمز: الجلسة أو المستخدم غير متوفر';
        debugPrint('❌ $_error');
      }
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في التحقق من OTP: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحليل أخطاء المصادقة العامة بشكل مفصل.
  String _getAuthErrorMessage(dynamic error) {
    final errorStr = error.toString().toLowerCase();
    debugPrint('تحليل خطأ المصادقة: $errorStr');

    if (errorStr.contains('invalid credentials') ||
        errorStr.contains('invalid login credentials')) {
      debugPrint('نوع الخطأ: بيانات تسجيل دخول غير صحيحة');
      return 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور';
    } else if (errorStr.contains('invalid email')) {
      debugPrint('نوع الخطأ: بريد إلكتروني غير صالح');
      return 'البريد الإلكتروني غير صالح. يرجى التأكد من كتابة البريد الإلكتروني بشكل صحيح';
    } else if (errorStr.contains('email already exists') ||
        errorStr.contains('unique constraint')) {
      debugPrint('نوع الخطأ: البريد الإلكتروني مستخدم بالفعل');
      return 'البريد الإلكتروني مستخدم بالفعل. يرجى تسجيل الدخول أو استخدام بريد إلكتروني آخر';
    } else if (errorStr.contains('weak password')) {
      debugPrint('نوع الخطأ: كلمة مرور ضعيفة');
      return 'كلمة المرور ضعيفة جداً. يجب أن تحتوي على 6 أحرف على الأقل';
    } else if (errorStr.contains('network') ||
        errorStr.contains('connection')) {
      debugPrint('نوع الخطأ: مشكلة في الشبكة');
      return 'خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';
    } else if (errorStr.contains('rate limit')) {
      debugPrint('نوع الخطأ: تجاوز الحد المسموح من المحاولات');
      return 'تم تجاوز عدد المحاولات المسموح بها. يرجى الانتظار لمدة دقيقة ثم المحاولة مرة أخرى';
    } else if (errorStr.contains('user not found')) {
      debugPrint('نوع الخطأ: المستخدم غير موجود');
      return 'البريد الإلكتروني غير مسجل في النظام. يرجى التأكد من البريد الإلكتروني أو إنشاء حساب جديد';
    } else if (errorStr.contains('timeout')) {
      debugPrint('نوع الخطأ: انتهاء مهلة الاتصال');
      return 'انتهت مهلة الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';
    } else if (errorStr.contains('email not confirmed')) {
      debugPrint('نوع الخطأ: البريد الإلكتروني غير مؤكد');
      return 'لم يتم تأكيد البريد الإلكتروني بعد. يرجى التحقق من بريدك الإلكتروني وتأكيد حسابك';
    } else if (errorStr.contains('invalid password')) {
      debugPrint('نوع الخطأ: كلمة مرور غير صحيحة');
      return 'كلمة المرور غير صحيحة. يرجى التأكد من كلمة المرور والمحاولة مرة أخرى';
    } else if (errorStr.contains('auth session')) {
      debugPrint('نوع الخطأ: مشكلة في جلسة المصادقة');
      return 'حدثت مشكلة في جلسة المصادقة. يرجى تسجيل الخروج وإعادة تسجيل الدخول';
    } else if (errorStr.contains('server')) {
      debugPrint('نوع الخطأ: مشكلة في الخادم');
      return 'حدثت مشكلة في الخادم. يرجى المحاولة مرة أخرى لاحقاً';
    } else {
      // تنظيف رسالة الخطأ من أي استثناءات إضافية
      String cleanError = error.toString();
      if (cleanError.contains('Exception:')) {
        cleanError = cleanError.split('Exception:').last.trim();
      }
      debugPrint('نوع الخطأ: خطأ غير معروف - $cleanError');
      return 'حدث خطأ غير متوقع: $cleanError';
    }
  }

  /// تنظيف المستخدمين المعلقين (بدون ملف شخصي)
  Future<void> _cleanupOrphanedUsers() async {
    try {
      debugPrint('بدء تنظيف المستخدمين المعلقين...');

      // استعلام للعثور على المستخدمين في auth.users بدون ملف شخصي
      final result = await _client.rpc('cleanup_orphaned_users');

      if (result != null) {
        debugPrint('تم تنظيف $result مستخدم معلق');
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف المستخدمين المعلقين: $e');
      // نتجاهل الخطأ لأنه ليس حرجاً
    }
  }

  /// إنشاء حساب جديد للمستخدم.
  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
    String? address,
    DateTime? birthDate,
    String? phone,
    List<String>? permissions,
    String? governorate,
    String? center,
    String? profileType,
  }) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // تنظيف المستخدمين المعلقين قبل البدء
      await _cleanupOrphanedUsers();

      // التحقق من صحة البيانات
      if (!_isVerEmail(email)) {
        _error = 'البريد الإلكتروني غير صالح';
        return false;
      }
      if (!_isValidPassword(password)) {
        _error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        return false;
      }
      if (name.trim().isEmpty) {
        _error = 'الاسم مطلوب';
        return false;
      }
      if (phone != null && !_isValidPhone(phone)) {
        _error = 'رقم الهاتف غير صالح';
        return false;
      }
      if (birthDate != null && birthDate.isAfter(DateTime.now())) {
        _error = 'تاريخ الميلاد غير صالح';
        return false;
      }
      if (governorate != null && governorate.trim().isEmpty) {
        _error = 'المحافظة غير صالحة';
        return false;
      }
      if (address != null && address.trim().isEmpty) {
        _error = 'العنوان غير صالح';
        return false;
      }

      final userProfileType = profileType ?? AppConstants.profileTypeCustomer;
      if (![
        AppConstants.profileTypeCustomer,
        AppConstants.profileTypeAdmin,
      ].contains(userProfileType)) {
        _error = 'نوع الملف الشخصي غير صالح';
        return false;
      }

      debugPrint('بدء عملية إنشاء حساب جديد للبريد: $email');

      // إنشاء الحساب في Supabase Auth مع البيانات الوصفية المنظمة
      // تنسيق البيانات بالطريقة التي يتوقعها المحفز create_user_and_profile
      final Map<String, dynamic> userData = {
        "name": name.trim(),
        "full_name": name.trim(),
        "email": email.trim(),
        "address": address?.trim() ?? '',
        "birth_date": birthDate?.toIso8601String() ?? '',
        "phone": phone?.trim() ?? '',
        "governorate": governorate?.trim() ?? '',
        "center": center?.trim() ?? '',
        "profile_type": userProfileType,
        "permissions": permissions ?? ['customer'],
      };

      // تسجيل البيانات للتشخيص
      debugPrint('بيانات المستخدم قبل إزالة القيم الفارغة: $userData');

      // إزالة القيم الفارغة
      userData.removeWhere((key, value) => value == null || value == '');

      // تسجيل البيانات بعد إزالة القيم الفارغة
      debugPrint('بيانات المستخدم بعد إزالة القيم الفارغة: $userData');

      debugPrint('إنشاء حساب مع البيانات الوصفية: $userData');

      final authResponse = await _handleRequest(
        () => _client.auth.signUp(
          email: email,
          password: password,
          data: userData,
        ),
        customError: 'فشل في إنشاء الحساب',
      );

      if (authResponse.user == null) {
        _error = 'فشل في إنشاء الحساب';
        return false;
      }

      // انتظار إنشاء الملف الشخصي تلقائيًا عبر المحفز (Trigger) في قاعدة البيانات
      try {
        // انتظار لحظة لإعطاء المحفز وقتًا للعمل
        await Future.delayed(const Duration(milliseconds: 1000));

        // التحقق من إنشاء الملف الشخصي
        final isProfileCreated = await _verifyProfileCreation(
          authResponse.user!.id,
        );

        if (!isProfileCreated) {
          debugPrint(
            'لم يتم إنشاء الملف الشخصي تلقائيًا، سيتم المحاولة مرة أخرى',
          );

          // تحديث البيانات الوصفية للمستخدم لتحفيز المحفز مرة أخرى
          // تنسيق البيانات بالطريقة التي يتوقعها المحفز sync_user_metadata
          await _client.auth.updateUser(
            UserAttributes(
              data: {
                'name': name,
                'full_name': name, // إضافة full_name أيضًا للتوافق مع المحفز
                'email': email, // إضافة البريد الإلكتروني للبيانات الوصفية
                'address': address,
                'birth_date': birthDate?.toIso8601String(),
                'phone': phone,
                'governorate': governorate,
                'center': center, // أضف هذا السطر
                'profile_type': userProfileType,
                'permissions': permissions ?? ['customer'],
              }..removeWhere((key, value) => value == null || value == ''),
            ),
          );

          // انتظار أطول في حالة تأخر المحفز
          await Future.delayed(const Duration(seconds: 2));

          // التحقق مرة أخرى
          final isProfileCreatedRetry = await _verifyProfileCreation(
            authResponse.user!.id,
          );

          if (!isProfileCreatedRetry) {
            throw Exception('فشل في إنشاء الملف الشخصي تلقائيًا');
          }
        }

        // تحديث المستخدم الحالي
        _currentUser = await _fetchUserModel(authResponse.user!);
        _isAuthenticated = true;

        // حفظ رمز الوصول
        if (authResponse.session != null) {
          await _storageService.setString(
            'access_token',
            authResponse.session!.accessToken,
          );
        }

        // إعداد تحديث الجلسة
        _setupSessionRefresh();

        debugPrint('تم إنشاء الحساب والملف الشخصي بنجاح');
        return true;
      } catch (e) {
        // في حالة فشل إنشاء الملف الشخصي، لا يمكن حذف الحساب من تطبيق العميل
        debugPrint('فشل في إنشاء الملف الشخصي: $e');
        _error =
            'فشل في إنشاء الملف الشخصي. يرجى المحاولة لاحقًا أو التواصل مع الدعم.';
        return false;
      }
    } catch (e) {
      _error = _getAuthErrorMessage(e);
      debugPrint('خطأ في التسجيل: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// التحقق من نجاح إنشاء الملف الشخصي
  Future<bool> _verifyProfileCreation(String userId) async {
    try {
      debugPrint('التحقق من إنشاء الملف الشخصي للمستخدم: $userId');

      // استخدام اسم الجدول من الثوابت بدلاً من النص المباشر
      final response =
          await _client
              .from(AppConstants.profilesTable)
              .select('*')
              .eq('id', userId)
              .maybeSingle();

      final exists = response != null;

      if (exists) {
        // طباعة بيانات الملف الشخصي للتشخيص
        debugPrint('✅ تم إنشاء الملف الشخصي بنجاح:');
        debugPrint('البيانات: $response');

        // التحقق من إنشاء إعدادات المستخدم
        try {
          final userSettingsService = ServiceLocator.userSettingsService;
          final settingsCreated = await userSettingsService
              .createDefaultSettings(userId);
          debugPrint('✅ إنشاء إعدادات المستخدم: $settingsCreated');
        } catch (settingsError) {
          debugPrint('❌ خطأ في إنشاء إعدادات المستخدم: $settingsError');
          // لا نريد إيقاف العملية إذا فشلت إعدادات المستخدم
        }
      } else {
        debugPrint('❌ لم يتم إنشاء الملف الشخصي');

        // التحقق من وجود المستخدم في جدول auth.users
        try {
          final authUser = await _client.auth.admin.getUserById(userId);
          debugPrint('المستخدم موجود في auth.users: ${authUser.user != null}');
          if (authUser.user != null) {
            debugPrint('بيانات المستخدم: ${authUser.user!.userMetadata}');

            // محاولة إنشاء الملف الشخصي يدويًا
            try {
              final userData = authUser.user!.userMetadata;
              if (userData != null) {
                final profileData = {
                  'id': userId,
                  'email': authUser.user!.email,
                  'name':
                      userData['name'] ??
                      authUser.user!.email?.split('@').first,
                  'full_name': userData['full_name'] ?? userData['name'],
                  'address': userData['address'],
                  'phone': userData['phone'],
                  'governorate': userData['governorate'],
                  'birth_date': userData['birth_date'],
                  'profile_type': userData['profile_type'] ?? 'customer',
                  'permissions': userData['permissions'] ?? ['customer'],
                };

                // إزالة القيم الفارغة
                profileData.removeWhere(
                  (key, value) => value == null || value == '',
                );

                debugPrint('محاولة إنشاء الملف الشخصي يدويًا: $profileData');

                await _client
                    .from(AppConstants.profilesTable)
                    .insert(profileData);
                debugPrint('✅ تم إنشاء الملف الشخصي يدويًا بنجاح');
                return true;
              }
            } catch (manualError) {
              debugPrint('❌ فشل في إنشاء الملف الشخصي يدويًا: $manualError');
            }
          }
        } catch (authError) {
          debugPrint(
            '❌ خطأ في التحقق من وجود المستخدم في auth.users: $authError',
          );
        }
      }

      return exists;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من إنشاء الملف الشخصي: $e');
      return false;
    }
  }

  /// تسجيل الدخول باستخدام حساب Google
  /// يستخدم OAuth للمصادقة عبر Google
  /// يعمل فقط في بيئة الويب
  ///
  /// Returns:
  /// * `true` إذا نجحت عملية تسجيل الدخول
  /// * `false` في حالة حدوث أي خطأ
  Future<bool> signInWithGoogle() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      debugPrint('بدء تسجيل الدخول باستخدام Google (OAuth)');

      // التحقق من تهيئة الخدمة
      if (!_isInitialized) {
        throw Exception('لم يتم تهيئة خدمة المصادقة');
      }

      // التحقق من المنصة
      if (!kIsWeb) {
        throw Exception(
          'هذه الطريقة مخصصة للويب فقط. استخدم GoogleAuthService للتطبيقات المحمولة',
        );
      }

      // تحديد رابط إعادة التوجيه
      final redirectUrl = '${Uri.base.origin}/auth/callback';
      debugPrint('URL إعادة التوجيه: $redirectUrl');

      // محاولة تسجيل الدخول باستخدام Google OAuth
      final response = await _handleRequest(
        () => _client.auth.signInWithOAuth(
          OAuthProvider.google,
          redirectTo: redirectUrl,
          queryParams: {
            'access_type': 'offline',
            'prompt': 'select_account', // تحسين تجربة المستخدم
            'include_granted_scopes': 'true',
            'response_type': 'code',
          },
        ),
        customError: 'فشل تسجيل الدخول باستخدام Google',
      );

      debugPrint('تم استلام الاستجابة من Google: $response');

      // التحقق من الجلسة وإعداد بيانات المستخدم
      final session = _client.auth.currentSession;
      if (session != null) {
        debugPrint('تم تسجيل الدخول بنجاح');

        // حفظ بيانات الجلسة
        await Future.wait([
          _saveAccessToken(session.accessToken),
          _fetchUserData(),
          _storageService.setInt(
            'last_authentication_time',
            DateTime.now().millisecondsSinceEpoch,
          ),
        ]);

        _isAuthenticated = true;
        _setupSessionRefresh();
        return true;
      } else {
        throw Exception('لم يتم العثور على جلسة صالحة');
      }
    } on AuthException catch (e) {
      debugPrint('خطأ مصادقة: ${e.message}');
      _error = 'خطأ في المصادقة: ${e.message}';
      return false;
    } catch (e) {
      // معالجة أخطاء Google Sign-In المحددة
      String errorMessage = e.toString();
      if (errorMessage.contains('flow_restarted')) {
        debugPrint(
          '⚠️ تم إعادة تشغيل تدفق Google Sign-In - سيتم المحاولة مرة أخرى',
        );
        _error = 'تم إلغاء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      } else if (errorMessage.contains('unknown_reason')) {
        debugPrint('⚠️ خطأ غير معروف في Google Sign-In');
        _error = 'حدث خطأ في تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      } else {
        debugPrint('خطأ غير متوقع: $e');
        _error = 'حدث خطأ غير متوقع أثناء تسجيل الدخول باستخدام Google: $e';
      }
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// حفظ رمز الوصول في التخزين المحلي.
  Future<void> _saveAccessToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', token);
  }

  /// حذف منتج من قاعدة البيانات
  ///
  /// المعلمات:
  /// * `productId`: معرف المنتج المراد حذفه
  ///
  /// يقوم بالتحقق من:
  /// * تهيئة الخدمة
  /// * مصادقة المستخدم
  /// * صلاحيات المستخدم
  /// حذف منتج من قاعدة البيانات.
  ///
  /// المعلمات:
  /// * `productId`: معرف المنتج المراد حذفه
  ///
  /// التحقق من:
  /// * تهيئة الخدمة
  /// * مصادقة المستخدم
  /// * صلاحيات المستخدم (يجب أن يكون admin)
  /// * وجود المنتج
  ///
  /// Returns:
  /// * `true` إذا تم الحذف بنجاح
  /// * `false` في حالة حدوث أي خطأ
  Future<bool> deleteProduct(String productId) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (!_isAuthenticated) throw Exception('المستخدم غير مصرح له');
    if (_currentUser?.profileType != 'admin') {
      throw Exception('ليس لديك صلاحية لحذف المنتجات');
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من وجود المنتج
      final product =
          await _client
              .from('products')
              .select('id, name')
              .eq('id', productId)
              .maybeSingle();

      if (product == null) {
        throw Exception('المنتج غير موجود');
      }

      // حذف المنتج
      await _client.from('products').delete().eq('id', productId);

      // تسجيل العملية في سجل النظام
      await _client
          .from('system_logs')
          .insert({
            'user_id': _currentUser?.id,
            'action': 'delete_product',
            'details': 'تم حذف المنتج: ${product['name']} (ID: $productId)',
            'timestamp': DateTime.now().toIso8601String(),
          })
          .onError((error, stackTrace) {
            // تجاهل أخطاء تسجيل العملية
            debugPrint('فشل تسجيل عملية حذف المنتج: $error');
          });

      debugPrint('تم حذف المنتج بنجاح: ${product['name']} (ID: $productId)');
      return true;
    } on PostgrestException catch (e, stackTrace) {
      _error = 'خطأ في قاعدة البيانات: ${e.message}';
      debugPrint('$_error\nStackTrace: $stackTrace');
      return false;
    } catch (e, stackTrace) {
      _error = 'فشل حذف المنتج: $e';
      debugPrint('$_error\nStackTrace: $stackTrace');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إعادة تعيين كلمة المرور.
  ///
  /// المعلمات:
  /// * `email`: البريد الإلكتروني للمستخدم
  ///
  /// يقوم بالتحقق من:
  /// * تهيئة الخدمة
  /// * صحة تنسيق البريد الإلكتروني
  /// * وجود حساب بهذا البريد الإلكتروني
  ///
  /// Returns:
  /// * `true` إذا تم إرسال رابط إعادة تعيين كلمة المرور بنجاح
  /// * `false` في حالة حدوث أي خطأ
  Future<bool> resetPassword(String email) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من صحة تنسيق البريد الإلكتروني
      if (!_isVerEmail(email)) {
        throw Exception('تنسيق البريد الإلكتروني غير صالح');
      }

      // التحقق من وجود حساب بهذا البريد الإلكتروني
      final userExists = await checkEmailExists(email);
      if (!userExists) {
        throw Exception('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
      }

      // تحديد رابط إعادة التوجيه بناءً على المنصة
      final redirectTo =
          kIsWeb
              ? '${Uri.base.origin}/reset-password'
              : 'io.supabase.motorcycleparts://reset-password';

      // إرسال رابط إعادة تعيين كلمة المرور
      await _handleRequest(
        () => _client.auth.resetPasswordForEmail(email, redirectTo: redirectTo),
        customError: 'فشل إرسال رابط إعادة تعيين كلمة المرور',
      );

      // تسجيل محاولة إعادة تعيين كلمة المرور
      await _client.from('system_logs').insert({
        'action': 'password_reset_request',
        'details': 'تم طلب إعادة تعيين كلمة المرور للبريد الإلكتروني: $email',
        'timestamp': DateTime.now().toIso8601String(),
      });

      debugPrint('تم إرسال رابط إعادة تعيين كلمة المرور إلى: $email');
      return true;
    } on AuthException catch (e) {
      _error = 'خطأ في المصادقة: ${e.message}';
      debugPrint(_error);
      return false;
    } catch (e) {
      _error = _getAuthErrorMessage(e);
      debugPrint('خطأ في إعادة تعيين كلمة المرور: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// جلب جميع المنتجات مع خيارات التصفية والترتيب المحسنة.
  ///
  /// المعلمات:
  /// * `limit`: عدد النتائج المطلوبة (الحد الأقصى 50)
  /// * `offset`: عدد النتائج التي يتم تخطيها
  /// * `searchQuery`: نص البحث (الاسم، الوصف، العلامة التجارية)
  /// * `categoryId`: معرف الفئة للتصفية
  /// * `sortBy`: عمود الترتيب
  /// * `ascending`: ترتيب تصاعدي أم تنازلي
  /// * `onlyAvailable`: عرض المنتجات المتوفرة فقط
  ///
  /// Returns:
  /// * قائمة من نموذج ProductModel
  Future<List<ProductModel>> getAllProducts({
    int limit = 20,
    int offset = 0,
    String? searchQuery,
    String? categoryId,
    String? sortBy,
    bool ascending = true,
    bool onlyAvailable = true,
  }) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');

    // التحقق من صحة المدخلات
    if (limit <= 0 || limit > 50) {
      throw Exception('عدد النتائج يجب أن يكون بين 1 و 50');
    }
    if (offset < 0) {
      throw Exception('قيمة الإزاحة يجب أن تكون 0 أو أكبر');
    }

    return await _handleRequest(() async {
      try {
        // تحسين الاستعلام لتقليل البيانات المنقولة
        const selectColumns = '''
          id,
          name,
          description,
          price,
          discount_price,
          original_price,
          category_id,
          company_id,
          image_urls,
          specifications,
          stock_quantity,
          view_count,
          is_featured,
          is_best_selling,
          created_at,
          updated_at,
          sku,
          brand,
          is_available
        ''';

        dynamic query = _client
            .from(AppConstants.productsTable)
            .select(selectColumns);

        // تطبيق فلتر التوفر أولاً لتحسين الأداء
        if (onlyAvailable) {
          query = query.eq('is_available', true);
        }

        // البحث النصي المحسن مع تنظيف المدخلات
        if (searchQuery != null && searchQuery.trim().isNotEmpty) {
          final sanitizedQuery = searchQuery.trim().replaceAll("'", "''");
          query = query.or(
            'name.ilike.%$sanitizedQuery%,description.ilike.%$sanitizedQuery%,brand.ilike.%$sanitizedQuery%',
          );
        }

        // فلتر الفئة مع التحقق من صحة المعرف
        if (categoryId != null && categoryId.trim().isNotEmpty) {
          // التحقق من وجود الفئة
          final categoryExists =
              await _client
                  .from('categories')
                  .select('id')
                  .eq('id', categoryId)
                  .maybeSingle();

          if (categoryExists == null) {
            throw Exception('الفئة المحددة غير موجودة');
          }

          query = query.eq('category_id', categoryId.trim());
        }

        // التحقق من صحة معيار الترتيب
        final validSortColumns = {
          'name': 'name',
          'price': 'price',
          'newest': 'created_at',
          'popular': 'view_count',
          'stock': 'stock_quantity',
        };

        // ترتيب محسن مع فهارس
        if (sortBy != null && validSortColumns.containsKey(sortBy)) {
          final sortColumn = validSortColumns[sortBy]!;
          // عكس الترتيب لبعض الأعمدة
          final shouldReverseOrder = ['newest', 'popular'].contains(sortBy);
          query = query.order(
            sortColumn,
            ascending: shouldReverseOrder ? !ascending : ascending,
          );
        } else {
          // الترتيب الافتراضي حسب تاريخ الإنشاء (الأحدث أولاً)
          query = query.order('created_at', ascending: false);
        }

        // تطبيق pagination مع timeout
        final response = await query
            .range(offset, offset + limit - 1)
            .timeout(
              const Duration(seconds: 10),
              onTimeout:
                  () => throw TimeoutException('انتهت مهلة جلب المنتجات'),
            );

        // تحويل النتائج إلى نماذج
        final products =
            response.map((json) => ProductModel.fromJson(json)).toList();

        // تسجيل عملية جلب المنتجات
        await _client
            .from(AppConstants.systemLogsTable)
            .insert({
              'action': 'fetch_products',
              'details': 'تم جلب ${products.length} منتج',
              'user_id': _currentUser?.id,
              'timestamp': DateTime.now().toIso8601String(),
            })
            .onError((error, stackTrace) {
              // تجاهل أخطاء تسجيل العملية
              debugPrint('خطأ في تسجيل عملية جلب المنتجات: $error');
              return null;
            });

        return products;
      } catch (e) {
        if (e is TimeoutException) {
          throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
        }
        rethrow;
      }
    }, customError: 'فشل في جلب المنتجات');
  }

  /// تسجيل الخروج ومسح البيانات المحلية.
  ///
  /// تقوم هذه الدالة بتسجيل خروج المستخدم الحالي وتنظيف جميع البيانات المحلية المرتبطة به.
  /// كما تقوم بتسجيل نشاط تسجيل الخروج وإلغاء جميع المؤقتات والاشتراكات.
  ///
  /// المعلمات:
  /// * [forceSignOut] - إذا كانت القيمة `true`، سيتم تنفيذ تسجيل الخروج بشكل إجباري حتى في حالة حدوث أخطاء.
  /// * [clearCache] - إذا كانت القيمة `true`، سيتم مسح ذاكرة التخزين المؤقت بالكامل (افتراضيًا `true`).
  /// * [notifyUser] - إذا كانت القيمة `true`، سيتم إشعار المستخدم بنجاح تسجيل الخروج (افتراضيًا `false`).
  ///
  /// تُرجع:
  /// * `Future<bool>` - `true` إذا تم تسجيل الخروج بنجاح، و`false` إذا فشلت العملية.
  Future<bool> signOut({
    bool forceSignOut = false,
    bool clearCache = true,
    bool notifyUser = false,
  }) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      debugPrint('محاولة تسجيل الخروج قبل تهيئة الخدمة');
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    // تسجيل وقت بدء عملية تسجيل الخروج للقياس
    final startTime = DateTime.now();

    // حفظ معلومات المستخدم قبل تسجيل الخروج لاستخدامها في تسجيل النشاط
    final userId = _currentUser?.id;
    final userEmail = _currentUser?.email;
    final deviceInfo = await _getDeviceInfo();
    bool logoutSuccessful = false;

    try {
      // التحقق من وجود اتصال بالإنترنت قبل محاولة تسجيل الخروج من الخادم
      try {
        await _checkConnection();
      } catch (connectionError) {
        debugPrint(
          'تحذير: لا يوجد اتصال بالإنترنت، سيتم تسجيل الخروج محليًا فقط',
        );
        // نستمر في العملية حتى مع عدم وجود اتصال
      }

      // تسجيل نشاط تسجيل الخروج إذا كان المستخدم مسجل الدخول
      if (_isAuthenticated && userId != null) {
        try {
          final logData = {
            'user_id': userId,
            'email': userEmail,
            'activity_type': 'logout',
            'device_info': deviceInfo,
            'ip_address': await _getIpAddress(),
            'created_at': DateTime.now().toIso8601String(),
            'status': 'success',
          };

          await _client.from('user_activity_logs').insert(logData);
          debugPrint('تم تسجيل نشاط تسجيل الخروج بنجاح');
        } catch (logError) {
          // لا نريد إيقاف عملية تسجيل الخروج إذا فشل تسجيل النشاط
          debugPrint('فشل تسجيل نشاط تسجيل الخروج: $logError');
        }
      }

      // تنفيذ تسجيل الخروج من Supabase
      await _handleRequest(
        () => _client.auth.signOut(),
        customError: 'فشل تسجيل الخروج من الخادم',
      );

      // تنظيف البيانات المحلية
      if (clearCache) {
        await _cleanupLocalData();
      }

      // إعادة تعيين حالة المصادقة
      _isAuthenticated = false;
      _currentUser = null;
      _lastAuthCheck = null;

      // إلغاء المؤقتات
      _sessionRefreshTimer?.cancel();
      _connectionCheckTimer?.cancel();

      // حساب الوقت المستغرق لتسجيل الخروج
      final duration = DateTime.now().difference(startTime);
      debugPrint('تم تسجيل الخروج بنجاح (${duration.inMilliseconds}ms)');
      logoutSuccessful = true;
      return true;
    } catch (e) {
      _error = 'خطأ أثناء تسجيل الخروج: $e';
      debugPrint(_error);

      // إذا كان التسجيل إجباري، نقوم بتنظيف البيانات المحلية بغض النظر عن الخطأ
      if (forceSignOut) {
        debugPrint('تنفيذ تسجيل الخروج الإجباري بسبب الخطأ: $e');

        if (clearCache) {
          await _cleanupLocalData();
        }

        _isAuthenticated = false;
        _currentUser = null;
        _lastAuthCheck = null;
        _sessionRefreshTimer?.cancel();
        _connectionCheckTimer?.cancel();

        debugPrint('تم تنفيذ تسجيل الخروج الإجباري بنجاح');
        logoutSuccessful = true;
        return true;
      }

      // محاولة تسجيل الخطأ في قاعدة البيانات إذا كان المستخدم لا يزال مصادقًا
      if (_isAuthenticated && userId != null) {
        try {
          await _client.from('user_activity_logs').insert({
            'user_id': userId,
            'email': userEmail,
            'activity_type': 'logout_failed',
            'device_info': deviceInfo,
            'ip_address': await _getIpAddress(),
            'created_at': DateTime.now().toIso8601String(),
            'status': 'failed',
            'error_details': e.toString(),
          });
        } catch (logError) {
          debugPrint('فشل تسجيل خطأ تسجيل الخروج: $logError');
        }
      }

      return false;
    } finally {
      _isLoading = false;
      notifyListeners();

      // إشعار المستخدم بنتيجة تسجيل الخروج إذا كان مطلوبًا
      if (notifyUser) {
        // يمكن تنفيذ آلية الإشعار هنا، مثل استخدام خدمة الإشعارات
        debugPrint(
          'إشعار المستخدم: ${logoutSuccessful ? 'تم تسجيل الخروج بنجاح' : 'فشل تسجيل الخروج'}',
        );
      }
    }
  }

  /// تنظيف البيانات المحلية المرتبطة بالمستخدم
  Future<void> _cleanupLocalData() async {
    try {
      // مسح جميع البيانات المخزنة محلياً
      await _storageService.clear();
      await _storageService.remove('last_authentication_time');

      // مسح ذاكرة التخزين المؤقت
      _cache.clear();

      // مسح أي بيانات مؤقتة أخرى
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('access_token');
      await prefs.remove('refresh_token');

      debugPrint('تم تنظيف البيانات المحلية بنجاح');
    } catch (e) {
      debugPrint('خطأ أثناء تنظيف البيانات المحلية: $e');
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final Map<String, dynamic> deviceData = {
        'platform': kIsWeb ? 'web' : Platform.operatingSystem,
        'timestamp': DateTime.now().toIso8601String(),
      };

      return deviceData;
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات الجهاز: $e');
      return {'platform': 'unknown', 'error': e.toString()};
    }
  }

  /// الحصول على عنوان IP
  Future<String> _getIpAddress() async {
    try {
      // هذه طريقة مبسطة، يمكن استخدام خدمة خارجية للحصول على عنوان IP
      return 'unknown'; // في التطبيق الحقيقي، يمكن استخدام خدمة مثل ipify
    } catch (e) {
      return 'unknown';
    }
  }

  /// تحديث ملف المستخدم.
  Future<void> updateProfile(UserModel user) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (_currentUser == null || user.id != _currentUser!.id) {
      throw Exception('لا يمكنك تحديث بيانات مستخدم آخر');
    }

    _isLoading = true;
    _error = null;

    try {
      // تم إزالة معالجة صورة المستخدم

      final updatedUser = user.copyWith(
        updatedAt: DateTime.now(),
        // تم إزالة الحقول المشفرة كجزء من تنظيف المشروع
      );

      await _handleRequest(
        () => _client
            .from(AppConstants.profilesTable)
            .update(updatedUser.toJson())
            .eq('id', user.id),
        customError: 'فشل تحديث الملف الشخصي',
      );

      _currentUser = updatedUser;
      await _storageService.setJson('user', updatedUser.toJson());
      _cacheUser(updatedUser);
    } catch (e) {
      throw Exception(_error ?? 'فشل تحديث الملف الشخصي: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // ✅ updateUserProfile مع تحقق إضافي:
  Future<bool> updateUserProfile({
    required String name,
    String? phone,
    String? address,
    DateTime? birthDate,
    String? governorate,
    String? center,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('لم يتم العثور على مستخدم مسجل الدخول');
      }

      // إنشاء خريطة البيانات للتحديث
      final Map<String, dynamic> updateData = {
        'name': name,
        'phone': phone,
        'address': address,
        'birth_date': birthDate?.toIso8601String(),
        'governorate': governorate,
        'center': center,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // إزالة القيم الفارغة
      updateData.removeWhere((key, value) => value == null);

      // تحديث الملف الشخصي
      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update(updateData)
              .eq('id', userId)
              .select()
              .maybeSingle();

      if (response == null) {
        throw Exception('فشل تحديث الملف الشخصي');
      }

      // تحديث بيانات المستخدم الحالي
      _currentUser = UserModel.fromJson(response);
      notifyListeners();

      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('خطأ في تحديث الملف الشخصي: $e');
      return false;
    }
  }

  /// تخزين رابط صورة Cloudinary في قاعدة البيانات.
  Future<bool> storeCloudinaryImageUrl({
    required String imageUrl,
    required String tableName,
    required String columnName,
    required String recordId,
  }) async {
    try {
      await _handleRequest(
        () => _client
            .from(tableName)
            .update({columnName: imageUrl})
            .eq('id', recordId),
        customError: 'فشل تخزين رابط الصورة',
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تخزين رابط صورة المنتج.
  Future<bool> storeProductImageUrl({
    required String imageUrl,
    required String productId,
    bool isMainImage = true,
  }) async {
    try {
      if (isMainImage) {
        return await storeCloudinaryImageUrl(
          imageUrl: imageUrl,
          tableName: AppConstants.productsTable,
          columnName: 'image_url',
          recordId: productId,
        );
      } else {
        final response =
            await _client
                .from(AppConstants.productsTable)
                .select('additional_images')
                .eq('id', productId)
                .single();
        List<String> additionalImages = List<String>.from(
          response['additional_images'] ?? [],
        );
        if (!additionalImages.contains(imageUrl)) {
          additionalImages.add(imageUrl);
        }
        await _handleRequest(
          () => _client
              .from(AppConstants.productsTable)
              .update({'additional_images': additionalImages})
              .eq('id', productId),
          customError: 'فشل إضافة صورة إضافية للمنتج',
        );
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /// تخزين رابط صورة الفئة.
  Future<bool> storeCategoryImageUrl({
    required String imageUrl,
    required String categoryId,
  }) async {
    return storeCloudinaryImageUrl(
      imageUrl: imageUrl,
      tableName: AppConstants.categoriesTable,
      columnName: 'image_url',
      recordId: categoryId,
    );
  }

  // تم إزالة دوال التشفير وفك التشفير

  /// جلب بيانات المستخدم بناءً على معرفه.
  Future<UserModel?> getUserById(String userId) async {
    try {
      debugPrint('جلب بيانات المستخدم بالمعرف: $userId');
      final query = _client
          .from(AppConstants.profilesTable)
          .select('*')
          .eq('id', userId);

      final response = await query.maybeSingle();

      if (response == null) {
        debugPrint('لم يتم العثور على ملف شخصي للمستخدم: $userId');
        return null;
      }

      // استخدام البيانات مباشرة بدون تشفير
      // تم إزالة جميع عمليات التشفير من المشروع

      debugPrint('تم العثور على ملف المستخدم: ${response['email']}');
      return UserModel.fromJson(response);
    } catch (e) {
      _error = 'فشل في جلب بيانات المستخدم: $e';
      debugPrint(_error);
      return null;
    }
  }

  /// جلب جميع عناوين المستخدم.
  Future<List<AddressModel>> getAddresses() async {
    return await _fetchWithCache('user_addresses', () async {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مصادق عليه');
      final query = _client
          .from(AppConstants.addressesTable)
          .select('*')
          .eq('user_id', userId);

      final response = await query
          .order('is_default', ascending: false)
          .order('created_at', ascending: false);
      return response.map((address) => AddressModel.fromJson(address)).toList();
    });
  }

  /// إضافة عنوان جديد للمستخدم.
  Future<void> addAddress(AddressModel address) async {
    try {
      await _client.from(AppConstants.addressesTable).insert({
        'title': address.title,
        'full_name': address.fullName,
        'street_address': address.streetAddress,
        'city': address.city,
        'state': address.state,
        'phone_number': address.phoneNumber,
        'is_default': address.isDefault,
        'user_id': _currentUser!.id,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      _cache.remove('user_addresses');
    } catch (e) {
      throw Exception('فشل في إضافة العنوان: $e');
    }
  }

  /// تحديث عنوان موجود.
  Future<void> updateAddress(AddressModel address) async {
    try {
      await _client
          .from(AppConstants.addressesTable)
          .update({
            'title': address.title,
            'full_name': address.fullName,
            'street_address': address.streetAddress,
            'city': address.city,
            'state': address.state,
            'phone_number': address.phoneNumber,
            'is_default': address.isDefault,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', address.id);
      _cache.remove('user_addresses');
    } catch (e) {
      throw Exception('فشل في تحديث العنوان: $e');
    }
  }

  /// جلب جميع الفئات النشطة.
  Future<List<CategoryModel>> getCategories() async {
    return await _fetchWithCache('categories', () async {
      final query = _client
          .from(AppConstants.categoriesTable)
          .select('*')
          .eq('is_active', true);

      final response = await query.order('name');
      return response.map((json) => CategoryModel.fromJson(json)).toList();
    });
  }

  /// جلب المنتجات بناءً على معرف الفئة.
  Future<List<ProductModel>> getProductsByCategory(String categoryId) async {
    return await _fetchWithCache('products_category_$categoryId', () async {
      final query = _client
          .from(AppConstants.productsTable)
          .select('*')
          .eq('category_id', categoryId)
          .eq('is_available', true);

      final response = await query.order('name');
      return response.map((json) => ProductModel.fromJson(json)).toList();
    });
  }

  /// جلب البيانات مع التخزين المؤقت.
  Future<T> _fetchWithCache<T>(
    String cacheKey,
    Future<T> Function() fetchData,
  ) async {
    final cachedData = _cache[cacheKey];
    if (cachedData != null &&
        cachedData['timestamp'] != null &&
        DateTime.now().difference(cachedData['timestamp']) < _cacheDuration) {
      return cachedData['data'] as T;
    }

    final data = await _handleRequest(
      fetchData,
      customError: 'فشل جلب البيانات',
    );
    _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
    return data;
  }

  /// جلب المنتجات الأكثر مبيعًا.
  Future<List<Map<String, dynamic>>> getBestSellingProducts() async {
    const cacheKey = 'best_selling_products';
    if (_cache.containsKey(cacheKey) &&
        (_cache[cacheKey]['timestamp'] as DateTime).isAfter(
          DateTime.now().subtract(_cacheDuration),
        )) {
      return _cache[cacheKey]['data'] as List<Map<String, dynamic>>;
    }

    try {
      final query = _client
          .from(AppConstants.productsTable)
          .select()
          .eq('is_best_selling', true);

      final response = await query
          .order('created_at', ascending: false)
          .timeout(const Duration(seconds: 15));
      final data = List<Map<String, dynamic>>.from(response);
      _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
      return data;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات الأكثر مبيعاً: $e');
      throw Exception('فشل جلب المنتجات الأكثر مبيعاً: $e');
    }
  }

  /// جلب العروض.
  Future<List<Map<String, dynamic>>> getOffers() async {
    const cacheKey = 'offers';
    if (_cache.containsKey(cacheKey) &&
        (_cache[cacheKey]['timestamp'] as DateTime).isAfter(
          DateTime.now().subtract(_cacheDuration),
        )) {
      return _cache[cacheKey]['data'] as List<Map<String, dynamic>>;
    }

    try {
      final query = _client
          .from(AppConstants.productsTable)
          .select()
          .not('discount_price', 'is', null);

      final response = await query
          .order('discount_price')
          .timeout(const Duration(seconds: 15));
      final data = List<Map<String, dynamic>>.from(response);
      _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
      return data;
    } catch (e) {
      debugPrint('خطأ في جلب العروض: $e');
      throw Exception('فشل جلب العروض: $e');
    }
  }

  /// جلب المنتجات المقترحة.
  Future<List<Map<String, dynamic>>> getSuggestedProducts() async {
    const cacheKey = 'suggested_products';
    if (_cache.containsKey(cacheKey) &&
        (_cache[cacheKey]['timestamp'] as DateTime).isAfter(
          DateTime.now().subtract(_cacheDuration),
        )) {
      return _cache[cacheKey]['data'] as List<Map<String, dynamic>>;
    }

    try {
      final query = _client
          .from(AppConstants.productsTable)
          .select()
          .eq('is_available', true);

      final response = await query
          .order('created_at', ascending: false)
          .limit(10)
          .timeout(const Duration(seconds: 15));
      final data = List<Map<String, dynamic>>.from(response);
      _cache[cacheKey] = {'data': data, 'timestamp': DateTime.now()};
      return data;
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات المقترحة: $e');
      throw Exception('فشل جلب المنتجات المقترحة: $e');
    }
  }

  /// حذف عنوان بناءً على معرفه.
  Future<void> deleteAddress(String addressId) async {
    if (addressId.isEmpty) {
      throw Exception('معرف العنوان مطلوب');
    }

    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مصادق عليه');

      await _client
          .from(AppConstants.addressesTable)
          .delete()
          .eq('id', addressId)
          .eq('user_id', userId)
          .timeout(const Duration(seconds: 10));
      _cache.remove('user_addresses');
    } catch (e) {
      debugPrint('خطأ في حذف العنوان: $e');
      throw Exception('فشل حذف العنوان: $e');
    }
  }

  /// تعيين عنوان كافتراضي.
  Future<void> setDefaultAddress(String id) async {
    if (id.isEmpty) {
      throw Exception('معرف العنوان مطلوب');
    }

    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('المستخدم غير مصادق عليه');

      await _client
          .from(AppConstants.addressesTable)
          .update({'is_default': false})
          .eq('user_id', userId)
          .timeout(const Duration(seconds: 10));

      await _client
          .from(AppConstants.addressesTable)
          .update({'is_default': true})
          .eq('id', id)
          .eq('user_id', userId)
          .timeout(const Duration(seconds: 10));
      _cache.remove('user_addresses');
    } catch (e) {
      debugPrint('خطأ في تعيين العنوان الافتراضي: $e');
      throw Exception('فشل تعيين الع عنوان الافتراضي: $e');
    }
  }

  /// البحث عن المنتجات بناءً على نص البحث.
  Future<List<Map<String, dynamic>>> searchProducts(String query) async {
    if (query.isEmpty) {
      throw Exception('نص البحث مطلوب');
    }

    try {
      final response = await _client
          .from(AppConstants.productsTable)
          .select()
          .ilike('name', '%$query%')
          .order('name')
          .timeout(const Duration(seconds: 15));
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('خطأ في البحث عن المنتجات: $e');
      throw Exception('فشل البحث عن المنتجات: $e');
    }
  }

  /// التحقق من وجود بريد إلكتروني في قاعدة البيانات.
  Future<bool> checkEmailExists(String email) async {
    try {
      if (email.isEmpty) {
        debugPrint('البريد الإلكتروني فارغ');
        return false;
      }

      email = email.trim().toLowerCase();
      debugPrint('التحقق من وجود البريد الإلكتروني: $email');

      // التحقق أولاً في جدول profiles
      final profileResponse =
          await _client
              .from(AppConstants.profilesTable)
              .select('email')
              .eq('email', email)
              .maybeSingle();

      final existsInProfiles = profileResponse != null;

      if (existsInProfiles) {
        debugPrint('البريد الإلكتروني $email موجود في جدول profiles');
        return true;
      }

      // التحقق الإضافي في جدول auth.users للتأكد من عدم وجود مستخدمين معلقين
      try {
        final authResponse = await _client.auth.admin.listUsers();
        final existsInAuth = authResponse.any(
          (user) => user.email?.toLowerCase() == email,
        );

        if (existsInAuth) {
          debugPrint('البريد الإلكتروني $email موجود في جدول auth.users');
          return true;
        }
      } catch (e) {
        debugPrint('تعذر التحقق من جدول auth.users: $e');
        // نتجاهل هذا الخطأ ونكمل بالتحقق العادي
      }

      // التحقق في جدول auth.users (إذا كان لديك صلاحية الوصول)
      try {
        final authResponse = await _client.rpc(
          'check_email_exists',
          params: {'email_to_check': email},
        );
        final existsInAuth = authResponse == true;

        debugPrint(
          'البريد الإلكتروني $email ${existsInAuth ? "موجود" : "غير موجود"} في auth.users',
        );
        return existsInAuth;
      } catch (authError) {
        debugPrint('خطأ في التحقق من auth.users: $authError');
        // إذا فشل التحقق في auth.users، نعتمد على نتيجة التحقق في profiles فقط
        return existsInProfiles;
      }
    } catch (e) {
      _error = 'فشل في التحقق من وجود البريد الإلكتروني: $e';
      debugPrint(_error);
      return false;
    }
  }

  /// تحديث وقت آخر تسجيل دخول للمستخدم.
  Future<bool> _updateLastLogin(String userId) async {
    try {
      debugPrint('تحديث وقت آخر تسجيل دخول للمستخدم: $userId');
      final now = DateTime.now().toIso8601String();

      await _client
          .from(AppConstants.profilesTable)
          .update({'last_login_at': now})
          .eq('id', userId);

      final updatedUser = await getUserById(userId);
      if (updatedUser?.lastLoginAt != null) {
        debugPrint(
          'تم تحديث وقت آخر تسجيل دخول بنجاح: ${updatedUser?.lastLoginAt}',
        );
        return true;
      }
      debugPrint('تم إرسال طلب التحديث ولكن لم يتم التأكد من نجاحه');
      return false;
    } catch (e) {
      debugPrint('فشل في تحديث وقت آخر تسجيل دخول: $e');
      return false;
    }
  }

  /// تجديد جلسة المستخدم باستخدام رمز الجلسة.
  Future<void> refreshSession(String token) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (token.isEmpty) throw Exception('رمز الجلسة مطلوب');

    _isLoading = true;
    _error = null;

    try {
      await _handleRequest(() async {
        final response = await _client.auth.setSession(token);
        if (response.session == null) throw Exception('فشل تهيئة الجلسة');
        await _fetchUserData();
        _isAuthenticated = _currentUser != null;
      }, customError: 'فشل تهيئة جلسة المستخدم');
    } catch (e) {
      _isAuthenticated = false;
      throw Exception(_error ?? 'فشل تهيئة الجلسة: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تنظيف الموارد عند التخلص من الكائن.
  @override
  void dispose() {
    // إلغاء جميع المؤقتات والاشتراكات
    _sessionRefreshTimer?.cancel();
    _connectionCheckTimer?.cancel();
    _authStateSubscription?.cancel();

    // تنظيف الذاكرة المؤقتة
    _cache.clear();

    // تنظيف اشتراك تغيير حالة المصادقة
    _client.auth.onAuthStateChange.drain();

    super.dispose();
  }

  /// معالجة أخطاء Postgrest.
  String _handlePostgrestError(PostgrestException e) {
    switch (e.code) {
      case '42501':
        return 'لا يمكن تنفيذ العملية. تواصل مع الدعم الفني.';
      case '23505':
        return 'البريد الإلكتروني مستخدم بالفعل.';
      case '23503':
        return 'خطأ في قاعدة البيانات. تواصل مع الدعم.';
      default:
        return 'خطأ في قاعدة البيانات: ${e.message}';
    }
  }

  /// معالجة أخطاء المصادقة.
  String _handleAuthError(AuthException e) {
    debugPrint('رمز الخطأ: ${e.statusCode}, رسالة الخطأ: ${e.message}');
    switch (e.statusCode) {
      case '400':
        if (e.message.toLowerCase().contains('invalid login credentials')) {
          return 'بيانات تسجيل الدخول غير صحيحة. تأكد من البريد الإلكتروني وكلمة المرور';
        } else if (e.message.toLowerCase().contains('email not confirmed')) {
          return 'يرجى تأكيد البريد الإلكتروني أولاً';
        }
        return 'بيانات غير صالحة. الرجاء التحقق من المعلومات المدخلة';
      case '401':
        if (e.message.toLowerCase().contains('invalid token')) {
          return 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى';
        }
        return 'غير مصرح لك بالدخول. تأكد من بيانات تسجيل الدخول';
      case '403':
        return 'ممنوع الوصول. ليس لديك صلاحية للوصول';
      case '404':
        return 'المستخدم غير موجود. الرجاء التأكد من البريد الإلكتروني';
      case '409':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case '422':
        if (e.message.toLowerCase().contains('password')) {
          return 'كلمة المرور غير صالحة. يجب أن تكون 6 أحرف على الأقل';
        } else if (e.message.toLowerCase().contains('email')) {
          return 'البريد الإلكتروني غير صالح. الرجاء التحقق من صيغة البريد';
        }
        return 'بيانات غير صالحة. الرجاء التحقق من المعلومات المدخلة';
      case '429':
        return 'تم تجاوز عدد المحاولات المسموح بها. الرجاء المحاولة لاحقاً';
      case '500':
        return 'خطأ في الخادم. الرجاء المحاولة لاحقاً';
      default:
        if (e.message.toLowerCase().contains('network')) {
          return 'خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت';
        } else if (e.message.toLowerCase().contains('timeout')) {
          return 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى';
        }
        return 'حدث خطأ غير متوقع: ${e.message}';
    }
  }

  /// التحقق مما إذا كان المستخدم مسؤولاً.
  Future<bool> isAdmin() async {
    try {
      if (!_isInitialized || !_isAuthenticated || _currentUser == null) {
        return false;
      }
      return _currentUser!.profileType == 'admin';
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحيات المسؤول: $e');
      return false;
    }
  }

  /// تغيير كلمة المرور للمستخدم الحالي
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    if (!_isInitialized || !_isAuthenticated) {
      _error = 'المستخدم غير مسجل الدخول';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // التحقق من صحة كلمة المرور الحالية
      final email = _currentUser?.email;
      if (email == null) {
        _error = 'لم يتم العثور على بريد إلكتروني للمستخدم';
        return false;
      }

      // حفظ الجلسة الحالية
      final currentSession = _client.auth.currentSession;

      // التحقق من كلمة المرور الحالية عن طريق محاولة تسجيل الدخول في عميل منفصل
      try {
        final tempClient = SupabaseClient(
          AppConstants.baseUrl, // استخدام baseUrl من AppConstants
          AppConstants.apiKey,
        );

        await tempClient.auth.signInWithPassword(
          email: email,
          password: currentPassword,
        );

        // تسجيل الخروج من العميل المؤقت
        await tempClient.auth.signOut();
      } catch (e) {
        _error = 'كلمة المرور الحالية غير صحيحة';
        return false;
      }

      // استعادة الجلسة الحالية إذا لزم الأمر
      if (currentSession != null && _client.auth.currentSession == null) {
        await _client.auth.setSession(currentSession.accessToken);
      }

      // التحقق من صحة كلمة المرور الجديدة
      if (!_isValidPassword(newPassword)) {
        _error = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        return false;
      }

      // تغيير كلمة المرور
      await _client.auth.updateUser(UserAttributes(password: newPassword));

      debugPrint('تم تغيير كلمة المرور بنجاح');
      return true;
    } catch (e) {
      _error = 'فشل تغيير كلمة المرور: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// حذف حساب المستخدم الحالي
  Future<bool> deleteAccount(String password) async {
    if (!_isInitialized || !_isAuthenticated || _currentUser == null) {
      _error = 'المستخدم غير مسجل الدخول';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final userId = _currentUser!.id;
      final email = _currentUser!.email;

      // التحقق من كلمة المرور عن طريق محاولة تسجيل الدخول
      try {
        await _client.auth.signInWithPassword(email: email, password: password);
      } catch (e) {
        _error = 'كلمة المرور غير صحيحة';
        return false;
      }

      // حذف بيانات المستخدم من جداول قاعدة البيانات
      // 1. حذف العناوين
      await _client
          .from(AppConstants.addressesTable)
          .delete()
          .eq('user_id', userId);

      // 2. حذف الملف الشخصي
      await _client.from(AppConstants.profilesTable).delete().eq('id', userId);

      // 3. حذف حساب المصادقة
      await _client.auth.admin.deleteUser(userId);

      // تسجيل الخروج وتنظيف البيانات المحلية
      await signOut();

      debugPrint('تم حذف الحساب بنجاح');
      return true;
    } catch (e) {
      _error = 'فشل حذف الحساب: $e';
      debugPrint(_error);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// تحديث عدد الطلبات للمستخدم
  Future<bool> updateOrderCount(String userId, {bool increment = true}) async {
    try {
      if (_currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      final currentCount = _currentUser!.orderCount ?? 0;
      final newCount = increment ? currentCount + 1 : currentCount - 1;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'order_count': newCount,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث عدد الطلبات: $e');
      return false;
    }
  }

  /// تحديث عدد المنتجات في قائمة الرغبات
  Future<bool> updateWishlistCount(
    String userId, {
    bool increment = true,
  }) async {
    try {
      if (_currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      final currentCount = _currentUser!.wishlistCount ?? 0;
      final newCount = increment ? currentCount + 1 : currentCount - 1;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'wishlist_count': newCount,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث عدد المنتجات في قائمة الرغبات: $e');
      return false;
    }
  }

  // تم حذف دالة تحديث متوسط التقييم حسب المتطلبات

  /// تحديث إجمالي المبلغ المنفق
  Future<bool> updateTotalSpent(String userId, double amount) async {
    try {
      if (_currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      final currentTotal = _currentUser!.totalSpent ?? 0.0;
      final newTotal = currentTotal + amount;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update({
                'total_spent': newTotal,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث إجمالي المبلغ المنفق: $e');
      return false;
    }
  }

  /// تحديث جميع إحصائيات المستخدم
  Future<bool> updateUserStats({
    required String userId,
    int? orderCount,
    int? wishlistCount,
    // تم حذف averageRating حسب المتطلبات
    double? totalSpent,
  }) async {
    try {
      final Map<String, dynamic> updateData = {
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (orderCount != null) updateData['order_count'] = orderCount;
      if (wishlistCount != null) updateData['wishlist_count'] = wishlistCount;
      // تم حذف تحديث average_rating حسب المتطلبات
      if (totalSpent != null) updateData['total_spent'] = totalSpent;

      final response =
          await _client
              .from(AppConstants.profilesTable)
              .update(updateData)
              .eq('id', userId)
              .select()
              .single();

      if (response != null) {
        _currentUser = UserModel.fromJson(response);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث إحصائيات المستخدم: $e');
      return false;
    }
  }

  /// إعادة إرسال رمز التحقق
  Future<bool> resendOTP(String email) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('إعادة إرسال رمز التحقق إلى: $email');

      final response = await _handleRequest(
        () => _client.auth.resend(type: OtpType.email, email: email),
        customError: 'فشل في إعادة إرسال رمز التحقق',
      );

      if (response != null) {
        debugPrint('تم إعادة إرسال رمز التحقق بنجاح');
        return true;
      } else {
        _error = 'فشل في إعادة إرسال رمز التحقق';
        return false;
      }
    } catch (e) {
      _error = _getAuthErrorMessage(e);
      debugPrint('خطأ في إعادة إرسال رمز التحقق: $_error');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // ========================================
  // امتداد إعدادات الأمان (مدمج من auth_supabase_service_extension.dart)
  // ========================================

  /// تحديث إعدادات الأمان للمستخدم
  Future<void> updateSecuritySettings(String setting, bool value) async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (!_isAuthenticated) throw Exception('المستخدم غير مسجل الدخول');

    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('المستخدم غير مصادق عليه');

    try {
      await _client.from(AppConstants.userSecuritySettingsTable).upsert({
        'user_id': userId,
        'setting_name': setting,
        'setting_value': value,
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث إعدادات الأمان: $e');
    }
  }

  /// حذف جميع بيانات المستخدم نهائياً
  Future<void> deleteAllUserData() async {
    if (!_isInitialized) throw Exception('الخدمة لم تُهيأ بعد');
    if (!_isAuthenticated) throw Exception('المستخدم غير مسجل الدخول');

    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('المستخدم غير مصادق عليه');

    try {
      // حذف البيانات من جميع الجداول المرتبطة
      await Future.wait([
        _client.from(AppConstants.profilesTable).delete().eq('id', userId),
        _client.from(AppConstants.ordersTable).delete().eq('user_id', userId),
        _client
            .from(AppConstants.cartItemsTable)
            .delete()
            .eq('user_id', userId),
        _client
            .from(AppConstants.favoritesTable)
            .delete()
            .eq('user_id', userId),
        _client
            .from(AppConstants.userInteractionsTable)
            .delete()
            .eq('user_id', userId),
        _client
            .from(AppConstants.userSecuritySettingsTable)
            .delete()
            .eq('user_id', userId),
      ]);

      // حذف الحساب من المصادقة
      await _client.auth.admin.deleteUser(userId);

      // تنظيف البيانات المحلية
      _currentUser = null;
      _isAuthenticated = false;
      _error = null;
      _cache.clear();
      await _storageService.clear();
      notifyListeners();
    } catch (e) {
      throw Exception('فشل في حذف بيانات المستخدم: $e');
    }
  }

  /// تحديث كلمة المرور للمستخدم الحالي
  Future<bool> updatePassword(String newPassword) async {
    if (!_isInitialized) {
      _error = 'الخدمة لم تُهيأ بعد';
      return false;
    }

    if (!_isAuthenticated || _currentUser == null) {
      _error = 'يجب تسجيل الدخول أولاً';
      return false;
    }

    if (!_isValidPassword(newPassword)) {
      _error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      return false;
    }

    try {
      debugPrint('تحديث كلمة المرور للمستخدم: ${_currentUser!.email}');

      await _client.auth.updateUser(UserAttributes(password: newPassword));

      debugPrint('تم تحديث كلمة المرور بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث كلمة المرور: $e');
      _error = 'فشل في تحديث كلمة المرور: $e';
      return false;
    }
  }

  /// التحقق من وجود كلمة مرور للمستخدم
  Future<bool> hasPassword(String email) async {
    if (!_isInitialized) {
      return false;
    }

    try {
      // محاولة تسجيل الدخول بكلمة مرور وهمية للتحقق من وجود كلمة مرور
      await _client.auth.signInWithPassword(
        email: email,
        password: 'dummy_password_check_123',
      );
      // إذا لم يحدث خطأ، فهذا يعني أن كلمة المرور موجودة (لكن خاطئة)
      return true;
    } catch (e) {
      if (e.toString().contains('Invalid login credentials')) {
        // إذا كان الخطأ "Invalid login credentials"، قد يعني عدم وجود كلمة مرور
        // أو كلمة مرور خاطئة، نحتاج لطريقة أخرى للتحقق
        return false;
      }
      // أي خطأ آخر يعني أن هناك مشكلة أخرى
      return false;
    }
  }

  // ==========================================================================
  // Google Authentication Methods
  // ==========================================================================

  /// تهيئة Google Sign In
  void _initializeGoogleSignIn() {
    try {
      _googleSignIn = GoogleSignIn(
        scopes: ['email', 'profile'],
        clientId: kIsWeb ? dotenv.env['GOOGLE_WEB_CLIENT_ID'] : null,
      );

      // الاستماع لتغييرات حالة تسجيل الدخول
      _googleSignIn.onCurrentUserChanged.listen((GoogleSignInAccount? account) {
        _googleUser = account;
        notifyListeners();
        debugPrint('Google Sign-In state changed: ${account?.email ?? 'null'}');
      });

      // محاولة تسجيل الدخول التلقائي بمعالجة أفضل للأخطاء
      _googleSignIn
          .signInSilently()
          .then((account) {
            if (account != null) {
              debugPrint(
                '✅ تم تسجيل الدخول التلقائي بـ Google: ${account.email}',
              );
            }
          })
          .catchError((error) {
            String errorMessage = error.toString();
            if (errorMessage.contains('flow_restarted') ||
                errorMessage.contains('unknown_reason')) {
              debugPrint(
                '⚠️ تم تجاهل خطأ Google Sign-In المعروف: $errorMessage',
              );
            } else {
              debugPrint('⚠️ فشل تسجيل الدخول التلقائي بـ Google: $error');
            }
            return null;
          });

      debugPrint('Google Sign-In initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize Google Sign-In: $e');
      _error = 'فشل في تهيئة خدمة Google Sign-In';
    }
  }

  /// ربط حساب Google بالحساب الحالي
  Future<bool> linkGoogleAccount() async {
    if (!_isAuthenticated || _currentUser == null) {
      _error = 'يجب أن تكون مسجل الدخول لربط حساب Google';
      return false;
    }

    if (_isGoogleSigningIn) {
      return false;
    }

    _setGoogleSigningIn(true);
    _error = null;

    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        _setGoogleSigningIn(false);
        return false;
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('فشل في الحصول على رموز Google Auth');
      }

      // ربط الحساب في Supabase - تم تعطيل هذه الميزة مؤقتاً
      // await _client.auth.linkIdentity({
      //   'provider': 'google',
      //   'id_token': googleAuth.idToken!,
      //   'access_token': googleAuth.accessToken,
      // });

      _googleUser = googleUser;
      debugPrint('Google account linked successfully');
      _setGoogleSigningIn(false);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error linking Google account: $e');
      _error = 'فشل في ربط حساب Google: $e';
      _setGoogleSigningIn(false);
      return false;
    }
  }

  /// إلغاء ربط حساب Google
  Future<bool> unlinkGoogleAccount() async {
    if (!_isAuthenticated) {
      _error = 'يجب أن تكون مسجل الدخول';
      return false;
    }

    try {
      // إلغاء ربط الحساب - تم تعطيل هذه الميزة مؤقتاً
      // await _client.auth.unlinkIdentity('google');
      await _googleSignIn.signOut();
      _googleUser = null;

      debugPrint('Google account unlinked successfully');
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error unlinking Google account: $e');
      _error = 'فشل في إلغاء ربط حساب Google: $e';
      return false;
    }
  }

  /// تسجيل الخروج من Google
  Future<void> signOutFromGoogle() async {
    try {
      await _googleSignIn.signOut();
      _googleUser = null;
      debugPrint('Signed out from Google successfully');
      notifyListeners();
    } catch (e) {
      debugPrint('Error signing out from Google: $e');
    }
  }

  /// تحديث حالة Google Sign In
  void _setGoogleSigningIn(bool isSigningIn) {
    _isGoogleSigningIn = isSigningIn;
    notifyListeners();
  }
}
