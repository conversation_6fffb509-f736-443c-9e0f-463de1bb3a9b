import 'dart:async';
import 'dart:math' as math;

import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/theme/gradients.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:motorcycle_parts_shop/screens/auth/login_screen.dart';
import 'package:motorcycle_parts_shop/screens/onboarding/onboarding_screen.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vibration/vibration.dart';

const _kAnimationDuration = Duration(
  milliseconds: 1500,
); // تقليل مدة الرسوم المتحركة
const _kImageHeight = 260.0; // تقليل حجم الصورة قليلاً
const _kButtonHeight = 56.0; // تحسين ارتفاع الأزرار
const _kParticleCount = 30; // تقليل عدد الجسيمات لتحسين الأداء

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _particleController;
  late ConfettiController _confettiController;

  late Animation<double> _fadeInAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _particleAnimation;

  bool _hasSeenOnboarding = false;
  bool _isLoading = true;
  bool _showParticles = false;
  Timer? _particleTimer;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _checkInitialStateFast();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      vsync: this,
      duration: _kAnimationDuration,
    );

    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    );

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _particleController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 2),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.easeOut),
    );

    _animationController.forward();
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);

    Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _showParticles = true;
        });
        _particleController.repeat();
      }
    });
  }

  Future<void> _checkInitialStateFast() async {
    try {
      setState(() {
        _isLoading = false;
      });

      // التحقق من SharedPreferences بسرعة
      final prefs = await SharedPreferences.getInstance();
      final hasSeenOnboarding = prefs.getBool('onboarding_complete') ?? false;

      if (!mounted) return;

      setState(() {
        _hasSeenOnboarding = hasSeenOnboarding;
      });

      // التحقق من المصادقة في الخلفية
      _checkAuthenticationInBackground();

      // إذا لم يشاهد المستخدم onboarding، انتقل إليه
      if (!_hasSeenOnboarding) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) {
            _navigateToOnboarding();
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ في التحقق السريع: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasSeenOnboarding = false;
        });

        // في حالة الخطأ، انتقل إلى onboarding
        if (!_hasSeenOnboarding) {
          _navigateToOnboarding();
        }
      }
    }
  }

  /// التحقق من المصادقة في الخلفية
  void _checkAuthenticationInBackground() {
    Future.delayed(const Duration(milliseconds: 100), () async {
      try {
        final authService = Provider.of<AuthSupabaseService>(
          context,
          listen: false,
        );

        // تهيئة خدمة المصادقة بدون انتظار
        if (!authService.isInitialized) {
          authService.initialize().catchError((e) {
            debugPrint('خطأ في تهيئة خدمة المصادقة: $e');
          });
        }

        // التحقق من حالة المصادقة
        if (authService.isAuthenticated && mounted) {
          _navigateToHome();
        }
      } catch (e) {
        debugPrint('خطأ في التحقق من المصادقة: $e');
      }
    });
  }

  /// الانتقال إلى شاشة تسجيل الدخول
  void _navigateToLogin() {
    debugPrint('🔄 محاولة الانتقال إلى شاشة تسجيل الدخول...');

    try {
      HapticFeedback.lightImpact();
      Vibration.vibrate(duration: 50); // إضافة اهتزاز خفيف

      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          transitionDuration: const Duration(milliseconds: 500),
          pageBuilder: (_, __, ___) {
            debugPrint('✅ تم إنشاء شاشة تسجيل الدخول');
            return const LoginScreen();
          },
          transitionsBuilder:
              (_, a, __, c) => FadeTransition(opacity: a, child: c),
        ),
      );

      debugPrint('✅ تم تنفيذ الانتقال بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في الانتقال إلى شاشة تسجيل الدخول: $e');

      // محاولة بديلة باستخدام pushNamed
      try {
        Navigator.of(context).pushReplacementNamed('/login');
        debugPrint('✅ تم الانتقال باستخدام pushReplacementNamed');
      } catch (e2) {
        debugPrint('❌ خطأ في pushReplacementNamed أيضاً: $e2');
      }
    }
  }

  /// الانتقال إلى شاشة التعريف
  void _navigateToOnboarding() {
    HapticFeedback.lightImpact();
    Vibration.vibrate(duration: 50); // إضافة اهتزاز خفيف
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        transitionDuration: const Duration(milliseconds: 500),
        pageBuilder: (_, __, ___) => const OnboardingScreen(),
        transitionsBuilder:
            (_, a, __, c) => FadeTransition(opacity: a, child: c),
      ),
    );
  }

  /// الانتقال إلى الشاشة الرئيسية
  void _navigateToHome() {
    Navigator.of(
      context,
    ).pushReplacementNamed('/home'); // افتراض وجود مسار للشاشة الرئيسية
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _particleController.dispose();
    _confettiController.dispose();
    _particleTimer?.cancel();
    super.dispose();
  }

  /// بناء صورة الترحيب المتحركة مع تحسينات للأداء والمظهر
  Widget _buildWelcomeImage() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final imageHeight =
            ResponsiveHelper.isMobile(context) ? 220.0 : _kImageHeight;
        final padding = ResponsiveHelper.getPadding(context);
        final isRTL = Directionality.of(context) == TextDirection.rtl;

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // طبقة خلفية إضافية للعمق
                  Container(
                    width: imageHeight + 60,
                    height: imageHeight + 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.white.withOpacity(0.05),
                          Colors.transparent,
                        ],
                        stops: const [0.6, 1.0],
                      ),
                    ),
                  ),

                  // خلفية دائرية متحركة محسنة
                  AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle:
                            _rotationAnimation.value *
                            (isRTL ? -1 : 1), // مراعاة اتجاه RTL
                        child: Container(
                          width: imageHeight + 40,
                          height: imageHeight + 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: SweepGradient(
                              colors: [
                                Colors.white.withOpacity(0.15),
                                Colors.white.withOpacity(0.05),
                                Colors.white.withOpacity(0.15),
                              ],
                              stops: const [0.3, 0.6, 1.0],
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // الصورة الرئيسية مع تأثير النبض محسن
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: padding),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white.withOpacity(0.3),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: ClipOval(
                            child: Container(
                              width: imageHeight,
                              height: imageHeight,
                              decoration: BoxDecoration(
                                gradient: RadialGradient(
                                  colors: [
                                    Colors.white.withOpacity(0.25),
                                    Colors.white.withOpacity(0.1),
                                  ],
                                  stops: const [0.4, 1.0],
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: Semantics(
                                label:
                                    'صورة ترحيبية لمتجر قطع غيار الدراجات النارية',
                                child: _buildImageContent(imageHeight),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  // تأثير توهج إضافي
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryColor.withOpacity(
                                  0.1 * _pulseAnimation.value,
                                ),
                                blurRadius: 30 * _pulseAnimation.value,
                                spreadRadius: 5 * _pulseAnimation.value,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء محتوى الصورة مع تحسينات للأداء والمظهر
  Widget _buildImageContent(double imageHeight) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // محاولة عرض Lottie أولاً مع تحسينات
        Lottie.asset(
          'assets/animations/welcome.json',
          height: imageHeight * 0.85, // زيادة حجم الصورة قليلاً
          width: imageHeight * 0.85,
          fit: BoxFit.contain,
          repeat: true,
          animate: true,
          frameRate: FrameRate.max, // تحسين معدل الإطارات
          delegates: LottieDelegates(
            values: [
              // تحسين ألوان الرسوم المتحركة لتتناسب مع الخلفية
              ValueDelegate.color(const ['**'], value: Colors.white),
            ],
          ),
          errorBuilder: (context, error, stackTrace) {
            // في حالة عدم وجود ملف Lottie، استخدم الصورة العادية مع تحسينات
            return Hero(
              tag: 'welcome_image',
              child: Image.asset(
                'assets/images/welcome_image.png',
                height: imageHeight * 0.85,
                width: imageHeight * 0.85,
                fit: BoxFit.contain,
                filterQuality: FilterQuality.high,
                semanticLabel: 'صورة ترحيبية لمتجر قطع غيار الدراجات النارية',
                // تحسين أداء الصورة
                cacheHeight:
                    (imageHeight *
                            0.85 *
                            MediaQuery.of(context).devicePixelRatio)
                        .toInt(),
                cacheWidth:
                    (imageHeight *
                            0.85 *
                            MediaQuery.of(context).devicePixelRatio)
                        .toInt(),
                errorBuilder:
                    (context, error, stackTrace) => _buildImageError(),
              ),
            );
          },
        ),

        // تأثير توهج إضافي فوق الصورة
        Container(
          width: imageHeight * 0.85,
          height: imageHeight * 0.85,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.white.withOpacity(0.2), Colors.transparent],
              stops: const [0.1, 1.0],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء خطأ الصورة
  Widget _buildImageError() {
    return Container(
      height: _kImageHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(24), // Consistent border radius
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 8),
          Text('تعذر تحميل الصورة', style: TextStyle(color: Colors.grey[600])),
        ],
      ),
    );
  }

  /// بناء عنوان الترحيب المتحرك مع تحسينات للمظهر والأداء
  Widget _buildWelcomeTitle() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final fontSize = ResponsiveHelper.getFontSize(
          context,
          ResponsiveHelper.isMobile(context) ? 32 : 36,
        );
        final isRTL = Directionality.of(context) == TextDirection.rtl;

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.02,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 14,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      color: Colors.white.withOpacity(0.12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.25),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 15,
                          spreadRadius: 1,
                          offset: const Offset(0, 5),
                        ),
                      ],
                      // إضافة تدرج خطي للخلفية
                      gradient: LinearGradient(
                        begin:
                            isRTL
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        end:
                            isRTL
                                ? Alignment.centerLeft
                                : Alignment.centerRight,
                        colors: [
                          Colors.white.withOpacity(0.15),
                          Colors.white.withOpacity(0.05),
                        ],
                      ),
                    ),
                    child: Semantics(
                      label:
                          _hasSeenOnboarding
                              ? 'مرحباً بعودتك'
                              : 'مرحباً بك في متجرنا',
                      child: Text(
                        _hasSeenOnboarding
                            ? '🏍️ مرحباً بعودتك! 🔧'
                            : '🏍️ مرحباً بك في متجرنا! 🔧',
                        textAlign: TextAlign.center,
                        style: Theme.of(
                          context,
                        ).textTheme.displayMedium!.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: fontSize,
                          color: Colors.white,
                          letterSpacing: 0.5, // تحسين قراءة النص
                          height: 1.2, // تحسين المسافة بين السطور
                          shadows: [
                            Shadow(
                              blurRadius: 12,
                              color: Colors.black.withOpacity(0.4),
                              offset: const Offset(2, 3),
                            ),
                            // إضافة ظل ثانوي لتأثير توهج
                            Shadow(
                              blurRadius: 20,
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              offset: const Offset(0, 0),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// بناء نص الترحيب المحسن مع تحسينات للمظهر والأداء
  Widget _buildWelcomeSubtitle() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final fontSize = ResponsiveHelper.getFontSize(context, 18);
        final isRTL = Directionality.of(context) == TextDirection.rtl;

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: padding),
              child: Column(
                children: [
                  // حاوية النص مع تحسينات
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 22,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: Colors.white.withOpacity(0.12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 10,
                          spreadRadius: 1,
                          offset: const Offset(0, 4),
                        ),
                      ],
                      // إضافة تدرج للخلفية
                      gradient: LinearGradient(
                        begin: isRTL ? Alignment.topRight : Alignment.topLeft,
                        end:
                            isRTL
                                ? Alignment.bottomLeft
                                : Alignment.bottomRight,
                        colors: [
                          Colors.white.withOpacity(0.15),
                          Colors.white.withOpacity(0.08),
                        ],
                      ),
                    ),
                    child: Semantics(
                      label:
                          _hasSeenOnboarding
                              ? 'نص ترحيبي للمستخدمين العائدين'
                              : 'نص ترحيبي للمستخدمين الجدد',
                      child: Text(
                        _hasSeenOnboarding
                            ? 'نحن سعداء برؤيتك مرة أخرى! استمتع بتجربة تسوق محسنة مع أحدث قطع الغيار وعروض حصرية تناسب احتياجاتك.'
                            : 'أفضل مكان للعثور على جميع قطع غيار دراجتك النارية بجودة عالية وأسعار لا تقبل المنافسة. نوفر لك تجربة تسوق فريدة ومميزة.',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                          color: Colors.white.withOpacity(0.95),
                          fontSize: fontSize,
                          height: 1.7, // زيادة المسافة بين السطور
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.3, // تحسين قراءة النص
                          shadows: [
                            Shadow(
                              blurRadius: 3,
                              color: Colors.black.withOpacity(0.2),
                              offset: const Offset(1, 1),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24), // زيادة المسافة
                  // قائمة المميزات المحسنة
                  _buildFeaturesList(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء قائمة المميزات مع تحسينات للمظهر والأداء
  Widget _buildFeaturesList() {
    // قائمة المميزات المحسنة مع وصف إضافي
    final features = [
      {'icon': '🏍️', 'text': 'قطع غيار أصلية', 'color': AppTheme.primaryColor},
      {'icon': '⚡', 'text': 'توصيل سريع', 'color': AppTheme.accentColor},
      {'icon': '💎', 'text': 'جودة عالية', 'color': AppTheme.successColor},
      {'icon': '🤖', 'text': 'ذكاء اصطناعي', 'color': AppTheme.secondaryColor},
      // إضافة ميزة جديدة
      {'icon': '🔧', 'text': 'ضمان شامل', 'color': AppTheme.warningColor},
    ];

    return Semantics(
      label: 'قائمة مميزات المتجر',
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 14, // زيادة المسافة بين العناصر
        runSpacing: 14, // زيادة المسافة بين الصفوف
        children:
            features.map((feature) {
              return AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  // تأثير نبض محسن
                  return Transform.scale(
                    scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.04,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 18,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(22),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: (feature['color'] as Color).withOpacity(0.2),
                            blurRadius: 8,
                            spreadRadius: 1,
                            offset: const Offset(0, 2),
                          ),
                        ],
                        // إضافة تدرج خفيف
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withOpacity(0.2),
                            (feature['color'] as Color).withOpacity(0.1),
                          ],
                        ),
                      ),
                      child: Semantics(
                        label: 'ميزة: ${feature['text']!.toString()}',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // أيقونة الميزة
                            Text(
                              feature['icon']!.toString(),
                              style: const TextStyle(fontSize: 18),
                            ),
                            const SizedBox(width: 8),
                            // نص الميزة
                            Text(
                              feature['text']!.toString(),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.3,
                                shadows: [
                                  Shadow(
                                    blurRadius: 2,
                                    color: Colors.black.withOpacity(0.2),
                                    offset: const Offset(1, 1),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }).toList(),
      ),
    );
  }

  /// بناء أزرار البدء المحسنة مع تحسينات للمظهر والأداء
  Widget _buildStartButton() {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final buttonHeight =
            ResponsiveHelper.isMobile(context) ? 64.0 : _kButtonHeight;
        final isRTL = Directionality.of(context) == TextDirection.rtl;

        return FadeTransition(
          opacity: _fadeInAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: padding),
                child: Column(
                  children: [
                    // الزر الرئيسي مع تحسينات
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: 1.0 + (_pulseAnimation.value - 1.0) * 0.05,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(32),
                              gradient: LinearGradient(
                                colors: [
                                  AppTheme.accentColor,
                                  AppTheme.accentColor.withOpacity(0.8),
                                  AppTheme.accentColor.withOpacity(0.9),
                                ],
                                begin:
                                    isRTL
                                        ? Alignment.topRight
                                        : Alignment.topLeft,
                                end:
                                    isRTL
                                        ? Alignment.bottomLeft
                                        : Alignment.bottomRight,
                                stops: const [0.0, 0.6, 1.0],
                              ),
                              boxShadow: [
                                // ظل أساسي
                                BoxShadow(
                                  color: AppTheme.accentColor.withOpacity(0.4),
                                  blurRadius: 20,
                                  spreadRadius: 2,
                                  offset: const Offset(0, 8),
                                ),
                                // ظل إضافي للتوهج
                                BoxShadow(
                                  color: Colors.white.withOpacity(0.1),
                                  blurRadius: 15,
                                  spreadRadius: -2,
                                  offset: const Offset(0, -4),
                                ),
                              ],
                              // إضافة حدود خفيفة
                              border: Border.all(
                                color: Colors.white.withOpacity(0.2),
                                width: 1.5,
                              ),
                            ),
                            child: Semantics(
                              label:
                                  _hasSeenOnboarding
                                      ? 'زر متابعة التسوق'
                                      : 'زر بدء الرحلة',
                              button: true,
                              child: ElevatedButton(
                                onPressed: () {
                                  HapticFeedback.heavyImpact();
                                  Vibration.vibrate(
                                    duration: 100,
                                    amplitude: 128,
                                  ); // اهتزاز أقوى
                                  _confettiController.play();
                                  _navigateToLogin();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white.withOpacity(
                                    0.15,
                                  ),
                                  foregroundColor: Colors.white,
                                  elevation: 8,
                                  shadowColor: Colors.black.withOpacity(0.3),
                                  minimumSize: Size(
                                    double.infinity,
                                    buttonHeight,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 32,
                                    vertical: 18,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(28),
                                  ),
                                  splashFactory: InkRipple.splashFactory,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // نص الزر
                                    Text(
                                      _hasSeenOnboarding
                                          ? '🚀 متابعة التسوق'
                                          : '🚀 ابدأ رحلتك معنا',
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 0.5,
                                        height: 1.2,
                                        shadows: [
                                          Shadow(
                                            blurRadius: 3,
                                            color: Colors.black26,
                                            offset: Offset(1, 1),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 14),
                                    // أيقونة الزر مع تأثيرات
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.25),
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(
                                              0.1,
                                            ),
                                            blurRadius: 4,
                                            spreadRadius: 0,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        isRTL
                                            ? Icons.arrow_back_ios
                                            : Icons.arrow_forward_ios,
                                        size: 18,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 20), // زيادة المسافة
                    // زر ثانوي للتعريف مع تحسينات
                    if (!_hasSeenOnboarding)
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1.5,
                          ),
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.15),
                              Colors.white.withOpacity(0.05),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                        ),
                        child: Semantics(
                          label: 'زر التعرف على المزيد',
                          button: true,
                          child: TextButton(
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              Vibration.vibrate(
                                duration: 50,
                              ); // إضافة اهتزاز خفيف
                              _navigateToOnboarding();
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 14,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 20,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 10),
                                Text(
                                  'تعرف على المزيد',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                    letterSpacing: 0.3,
                                    shadows: [
                                      Shadow(
                                        blurRadius: 2,
                                        color: Colors.black.withOpacity(0.2),
                                        offset: const Offset(1, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء الجسيمات المتحركة مع تحسينات للمظهر والأداء
  Widget _buildAnimatedParticles() {
    if (!_showParticles) return const SizedBox.shrink();

    // تحسين عدد الجسيمات حسب حجم الشاشة
    final screenSize = MediaQuery.of(context).size;
    final particleCount = (screenSize.width * screenSize.height) / 10000;
    final adjustedParticleCount = math.min(
      _kParticleCount,
      particleCount.toInt(),
    );

    return RepaintBoundary(
      // تحسين الأداء
      child: AnimatedBuilder(
        animation: _particleAnimation,
        builder: (context, child) {
          return Stack(
            children: List.generate(adjustedParticleCount, (index) {
              // استخدام بذور مختلفة لكل جسيم لضمان العشوائية
              final random = math.Random(index * 3 + 7);

              // موقع الجسيم
              final x = random.nextDouble() * screenSize.width;
              final y = random.nextDouble() * screenSize.height;

              // حجم الجسيم - جعل بعض الجسيمات أكبر للتنوع
              final size = random.nextDouble() * 5 + (index % 5 == 0 ? 4 : 2);

              // شفافية الجسيم
              final opacity = random.nextDouble() * 0.5 + 0.1;

              // سرعة حركة الجسيم - جعل بعض الجسيمات أسرع من غيرها
              final speed = random.nextDouble() * 150 + 50;

              // لون الجسيم - إضافة تنوع في الألوان
              final useColoredParticle = index % 8 == 0;
              final particleColor =
                  useColoredParticle
                      ? [
                        AppTheme.primaryColor,
                        AppTheme.secondaryColor,
                        AppTheme.accentColor,
                      ][index % 3].withOpacity(opacity * 0.8)
                      : Colors.white.withOpacity(opacity);

              // حركة الجسيم - بعضها يتحرك لأعلى وبعضها لأسفل
              final direction = index % 2 == 0 ? 1 : -1;
              final movement = _particleAnimation.value * speed * direction;

              return Positioned(
                left: x,
                top: y + movement,
                child: Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    color: particleColor,
                    shape:
                        index % 10 == 0 ? BoxShape.rectangle : BoxShape.circle,
                    borderRadius:
                        index % 10 == 0 ? BorderRadius.circular(2) : null,
                    boxShadow:
                        index % 15 == 0
                            ? [
                              BoxShadow(
                                color: particleColor.withOpacity(0.5),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ]
                            : null,
                  ),
                ),
              );
            }),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light.copyWith(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: AppTheme.primaryColor,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        extendBodyBehindAppBar: true, // تمديد المحتوى خلف شريط الحالة
        body: Stack(
          children: [
            // الخلفية المتدرجة المتحركة محسنة
            Container(
              decoration: BoxDecoration(gradient: AppGradients.welcomeGradient),
            ),

            // خلفية متحركة إضافية محسنة
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                final isRTL = Directionality.of(context) == TextDirection.rtl;
                return Transform.rotate(
                  angle: _rotationAnimation.value * 0.1 * (isRTL ? -1 : 1),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        center: isRTL ? Alignment.topLeft : Alignment.topRight,
                        radius: 1.8,
                        colors: [
                          Colors.white.withOpacity(0.15),
                          Colors.transparent,
                        ],
                        stops: const [0.2, 1.0],
                      ),
                    ),
                  ),
                );
              },
            ),

            // نمط زخرفي إضافي للخلفية
            Positioned(
              bottom: -100,
              right: -100,
              child: AnimatedBuilder(
                animation: _rotationAnimation,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotationAnimation.value * 0.2,
                    child: Container(
                      width: 300,
                      height: 300,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            AppTheme.primaryColor.withOpacity(0.1),
                            Colors.transparent,
                          ],
                          stops: const [0.2, 1.0],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // الجسيمات المتحركة
            _buildAnimatedParticles(),

            // الكونفيتي محسن
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.explosive,
                shouldLoop: false,
                maxBlastForce: 7, // قوة انفجار أكبر
                minBlastForce: 3, // قوة انفجار أدنى
                emissionFrequency: 0.05, // تردد إصدار أعلى
                numberOfParticles: 30, // عدد جسيمات أكبر
                gravity: 0.2, // جاذبية أقل للتأثير المطول
                colors: const [
                  Colors.blue,
                  Colors.yellow,
                  Colors.red,
                  Colors.green,
                  Colors.purple,
                  Colors.orange,
                  Colors.pink,
                  Colors.teal,
                ],
              ),
            ),

            // المحتوى الرئيسي
            SafeArea(
              child: Center(
                child:
                    _isLoading ? _buildLoadingIndicator() : _buildMainContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// مؤشر التحميل المحسن
  Widget _buildLoadingIndicator() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // مؤشر دائري محسن
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: const CircularProgressIndicator(
            color: Colors.white,
            strokeWidth: 3,
            backgroundColor: Colors.white24,
          ),
        ),
        const SizedBox(height: 24),
        // نص التحميل المحسن
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
          ),
          child: Text(
            'جاري التحضير...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
              shadows: [
                Shadow(
                  blurRadius: 2,
                  color: Colors.black.withOpacity(0.2),
                  offset: const Offset(1, 1),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// المحتوى الرئيسي المحسن
  Widget _buildMainContent() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(), // تحسين تجربة التمرير
      padding: const EdgeInsets.symmetric(vertical: 40.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // شعار متحرك أعلى الصفحة
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: SizedBox(
              height: 64,
              child: Lottie.asset(
                'assets/animations/motorcycle.json',
                repeat: true,
                animate: true,
                fit: BoxFit.contain,
                errorBuilder:
                    (context, error, stackTrace) => Image.asset(
                      'assets/images/motorcycle.png',
                      height: 48,
                      fit: BoxFit.contain,
                    ),
              ),
            ),
          ),
          // صورة الترحيب
          _buildWelcomeImage(),

          // المسافة بين العناصر
          const SizedBox(height: 40),

          // عنوان الترحيب
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 400),
            transitionBuilder:
                (child, anim) => FadeTransition(opacity: anim, child: child),
            child: _buildWelcomeTitle(),
          ),

          // المسافة بين العناصر
          const SizedBox(height: 28), // زيادة المسافة قليلاً
          // نص الترحيب الفرعي
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 400),
            transitionBuilder:
                (child, anim) => FadeTransition(opacity: anim, child: child),
            child: _buildWelcomeSubtitle(),
          ),

          // المسافة بين العناصر
          const SizedBox(height: 48),

          // زر البدء
          _buildStartButton(),

          // مسافة إضافية في الأسفل
          const SizedBox(height: 20),

          // شريط حالة صغير في الأسفل
          _AppFooter(),
        ],
      ),
    );
  }
}

/// شريط حالة صغير في الأسفل
class _AppFooter extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        border: const Border(
          top: BorderSide(color: Colors.white24, width: 0.5),
        ),
      ),
      child: Center(
        child: Text(
          'جميع الحقوق محفوظة © 2024 متجر قطع غيار الدراجات النارية',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 13,
            fontWeight: FontWeight.w400,
            letterSpacing: 0.2,
          ),
        ),
      ),
    );
  }
}
