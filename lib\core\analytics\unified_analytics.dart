import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../monitoring/app_logger.dart';

/// خدمة التحليلات الموحدة - دمج جميع خدمات التحليلات
/// تجمع AnalyticsService + UserAnalytics + EnhancedAnalyticsService
class UnifiedAnalyticsService extends ChangeNotifier {
  static final UnifiedAnalyticsService _instance =
      UnifiedAnalyticsService._internal();
  factory UnifiedAnalyticsService() => _instance;
  UnifiedAnalyticsService._internal();

  late final SupabaseClient _supabase;
  late final AppLogger _logger;
  late final SharedPreferences _prefs;

  final List<AnalyticsEvent> _eventQueue = [];
  final Map<String, dynamic> _userProperties = {};
  final Map<String, int> _eventCounts = {};
  final Map<String, int> _screenViews = {};
  final Map<String, int> _productViews = {};
  final Map<String, int> _searchQueries = {};
  final Map<String, int> _buttonClicks = {};
  final List<Map<String, dynamic>> _userJourney = [];
  final Map<String, DateTime> _lastEventTimes = {};
  final Map<String, dynamic> _userMetrics = {};
  final Map<String, dynamic> _cachedStats = {};
  final Map<String, List<double>> _performanceMetrics = {};
  final Map<String, int> _userInteractions = {};
  final Map<String, Duration> _screenTimeTracking = {};

  Timer? _sessionTimer;
  Timer? _flushTimer;
  DateTime? _sessionStart;
  DateTime? _lastCacheUpdate;
  String? _userId;
  String? _sessionId;
  String? _currentScreen;
  bool _isInitialized = false;
  bool _isEnabled = true;

  static const Duration _cacheValidDuration = Duration(minutes: 15);

  /// تهيئة خدمة التحليلات الموحدة
  Future<void> initialize({
    required SupabaseClient supabase,
    required SharedPreferences prefs,
    String? userId,
  }) async {
    if (_isInitialized) return;

    _supabase = supabase;
    _prefs = prefs;
    _logger = AppLogger();
    _userId = userId;
    _sessionId = _generateSessionId();
    _sessionStart = DateTime.now();

    await _loadUserProperties();
    await _loadSettings();
    await _startSession();

    // بدء مؤقت إرسال البيانات كل دقيقة
    _flushTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _flushEvents();
    });

    // بدء مؤقت الجلسة
    _sessionTimer = Timer.periodic(const Duration(minutes: 30), (_) {
      _updateSession();
    });

    _isInitialized = true;
    _logger.info('تم تهيئة خدمة التحليلات الموحدة');
  }

  /// تفعيل/إلغاء تفعيل التحليلات
  Future<void> setAnalyticsEnabled(bool enabled) async {
    _isEnabled = enabled;
    await _prefs.setBool('analytics_enabled', enabled);
    notifyListeners();
  }

  bool get isEnabled => _isEnabled;

  /// تتبع حدث عام
  void trackEvent(String eventName, {Map<String, dynamic>? properties}) {
    if (!_isEnabled || !_isInitialized) return;

    final event = AnalyticsEvent(
      name: eventName,
      properties: {
        ...?properties,
        'user_id': _userId,
        'session_id': _sessionId,
        'timestamp': DateTime.now().toIso8601String(),
        'screen': _currentScreen,
      },
      timestamp: DateTime.now(),
    );

    _eventQueue.add(event);
    _eventCounts[eventName] = (_eventCounts[eventName] ?? 0) + 1;
    _userInteractions[eventName] = (_userInteractions[eventName] ?? 0) + 1;
    _lastEventTimes[eventName] = DateTime.now();

    _logger.userAction('تتبع حدث: $eventName', data: properties);
    notifyListeners();

    // إرسال فوري للأحداث المهمة
    if (_isCriticalEvent(eventName)) {
      _flushEvents();
    }
  }

  /// تتبع عرض شاشة
  void trackScreenView(String screenName, {Map<String, dynamic>? properties}) {
    // تسجيل وقت دخول الشاشة لحساب مدة البقاء
    final now = DateTime.now();
    if (_currentScreen != null &&
        _lastEventTimes.containsKey('screen_view_$_currentScreen')) {
      final timeSpent = now.difference(
        _lastEventTimes['screen_view_$_currentScreen']!,
      );
      _screenTimeTracking[_currentScreen!] = timeSpent;
    }

    _currentScreen = screenName;
    _screenViews[screenName] = (_screenViews[screenName] ?? 0) + 1;

    trackEvent(
      'screen_view',
      properties: {'screen_name': screenName, ...?properties},
    );
  }

  /// تتبع عرض منتج
  void trackProductView(String productId, {Map<String, dynamic>? properties}) {
    _productViews[productId] = (_productViews[productId] ?? 0) + 1;

    trackEvent(
      'product_view',
      properties: {'product_id': productId, ...?properties},
    );
  }

  /// تتبع بحث
  void trackSearch(
    String query, {
    int? resultsCount,
    Map<String, dynamic>? properties,
  }) {
    _searchQueries[query] = (_searchQueries[query] ?? 0) + 1;

    trackEvent(
      'search',
      properties: {
        'query': query,
        'results_count': resultsCount,
        ...?properties,
      },
    );
  }

  /// تتبع نقرة زر
  void trackButtonClick(String buttonName, {Map<String, dynamic>? properties}) {
    _buttonClicks[buttonName] = (_buttonClicks[buttonName] ?? 0) + 1;

    trackEvent(
      'button_click',
      properties: {'button_name': buttonName, ...?properties},
    );
  }

  /// تتبع شراء
  void trackPurchase({
    required String productId,
    required double amount,
    required String currency,
    Map<String, dynamic>? properties,
  }) {
    trackEvent(
      'purchase',
      properties: {
        'product_id': productId,
        'amount': amount,
        'currency': currency,
        'revenue': amount,
        ...?properties,
      },
    );
  }

  /// تتبع مشاركة
  void trackShare(
    String contentType,
    String contentId, {
    Map<String, dynamic>? properties,
  }) {
    trackEvent(
      'share',
      properties: {
        'content_type': contentType,
        'content_id': contentId,
        ...?properties,
      },
    );
  }

  /// تتبع خطأ
  void trackError(
    String error, {
    String? context,
    Map<String, dynamic>? properties,
  }) {
    trackEvent(
      'error',
      properties: {'error_message': error, 'context': context, ...?properties},
    );
  }

  /// تتبع أداء العملية
  void trackPerformance(
    String operation,
    Duration duration, {
    Map<String, dynamic>? properties,
  }) {
    _performanceMetrics.putIfAbsent(operation, () => []);
    _performanceMetrics[operation]!.add(duration.inMilliseconds.toDouble());

    // الاحتفاظ بآخر 100 قياس فقط
    if (_performanceMetrics[operation]!.length > 100) {
      _performanceMetrics[operation]!.removeAt(0);
    }

    trackEvent(
      'performance',
      properties: {
        'operation': operation,
        'duration_ms': duration.inMilliseconds,
        ...?properties,
      },
    );
  }

  /// تحديث خصائص المستخدم
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    _userProperties.addAll(properties);
    await _saveUserProperties();

    // تحديث المقاييس المشتقة من خصائص المستخدم
    _updateUserMetrics(properties);

    notifyListeners();
    _logger.info('تم تحديث خصائص المستخدم', data: properties);
  }

  /// تحديد معرف المستخدم
  void setUserId(String userId) {
    _userId = userId;
    setUserProperties({'user_id': userId});
  }

  /// إضافة إلى رحلة المستخدم
  void addToUserJourney(String action, {Map<String, dynamic>? data}) {
    _userJourney.add({
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      'screen': _currentScreen,
      'data': data ?? {},
    });

    // الاحتفاظ بآخر 50 إجراء فقط
    if (_userJourney.length > 50) {
      _userJourney.removeAt(0);
    }
  }

  /// الحصول على آخر نشاط للحدث المحدد
  DateTime? getLastEventTime(String eventName) {
    return _lastEventTimes[eventName];
  }

  /// التحقق من مدى حداثة آخر حدث
  bool isEventRecent(String eventName, Duration threshold) {
    final lastTime = _lastEventTimes[eventName];
    if (lastTime == null) return false;
    return DateTime.now().difference(lastTime) <= threshold;
  }

  /// الحصول على مدة البقاء في شاشة معينة
  Duration? getScreenTime(String screenName) {
    return _screenTimeTracking[screenName];
  }

  /// الحصول على إحصائيات شاملة
  Future<UnifiedAnalyticsData> getAnalyticsData() async {
    try {
      // إحصائيات الجلسة الحالية
      final sessionDuration =
          _sessionStart != null
              ? DateTime.now().difference(_sessionStart!).inMinutes
              : 0;

      // إحصائيات من قاعدة البيانات (مع تخزين مؤقت)
      Map<String, dynamic> dbStats = {};
      if (!_isCacheValid()) {
        dbStats = await _fetchDatabaseStats();
        _cacheStats('db_stats', dbStats);
      } else {
        dbStats = _cachedStats['db_stats'] ?? {};
      }

      return UnifiedAnalyticsData(
        // إحصائيات الجلسة
        sessionDuration: sessionDuration,
        currentSessionEvents: _eventQueue.length,

        // إحصائيات الشاشات
        screenViews: Map.from(_screenViews),
        mostViewedScreens: _getMostViewed(_screenViews),

        // إحصائيات المنتجات
        productViews: Map.from(_productViews),
        mostViewedProducts: _getMostViewed(_productViews),

        // إحصائيات البحث
        searchQueries: Map.from(_searchQueries),
        topSearchQueries: _getMostViewed(_searchQueries),

        // إحصائيات التفاعل
        buttonClicks: Map.from(_buttonClicks),
        userInteractions: Map.from(_userInteractions),

        // رحلة المستخدم
        userJourney: List.from(_userJourney),

        // إحصائيات الأداء
        performanceMetrics: _getPerformanceStats(),

        // إحصائيات قاعدة البيانات
        databaseStats: dbStats,

        // معلومات المستخدم
        userProperties: Map.from(_userProperties),
        eventCounts: Map.from(_eventCounts),
        userMetrics: Map.from(_userMetrics),
        screenTimeTracking: Map.from(_screenTimeTracking),
        lastEventTimes: Map.from(_lastEventTimes),
      );
    } catch (e) {
      _logger.error('خطأ في جلب بيانات التحليلات', error: e);
      return UnifiedAnalyticsData.empty();
    }
  }

  /// الحصول على توصيات مخصصة
  Future<List<AnalyticsRecommendation>> getRecommendations() async {
    final data = await getAnalyticsData();
    final recommendations = <AnalyticsRecommendation>[];

    // توصيات بناءً على الشاشات الأكثر زيارة
    if (data.mostViewedScreens.isNotEmpty) {
      final topScreen = data.mostViewedScreens.first;
      recommendations.add(
        AnalyticsRecommendation(
          type: 'feature',
          title: 'ميزة قد تعجبك',
          description: 'بناءً على زياراتك المتكررة لشاشة ${topScreen['name']}',
          priority: 0.8,
        ),
      );
    }

    // توصيات بناءً على الأداء
    if (data.sessionDuration > 30) {
      recommendations.add(
        AnalyticsRecommendation(
          type: 'tip',
          title: 'نصيحة للاستخدام',
          description: 'يمكنك الوصول السريع للميزات المفضلة لديك',
          priority: 0.6,
        ),
      );
    }

    // ترتيب حسب الأولوية
    recommendations.sort((a, b) => b.priority.compareTo(a.priority));

    return recommendations;
  }

  /// بدء جلسة جديدة
  Future<void> _startSession() async {
    try {
      // تحديث الحقول لتتوافق مع هيكل الجدول
      final deviceInfo = await _getDeviceInfo();
      final ipInfo = await _getIpInfo();

      await _supabase.from('user_sessions').insert({
        'session_id': _sessionId,
        'user_id': _userId,
        'started_at': _sessionStart!.toIso8601String(),
        'device_info': deviceInfo,
        'ip_address': ipInfo['ip'],
        'location_data': ipInfo['location'] ?? {},
        'is_active': true,
        'status': 'active',
      });
    } catch (e) {
      _logger.error('خطأ في بدء الجلسة', error: e);
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final Map<String, dynamic> deviceData = {};

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceData['model'] = androidInfo.model;
        deviceData['brand'] = androidInfo.brand;
        deviceData['version'] = androidInfo.version.release;
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceData['model'] = iosInfo.model;
        deviceData['name'] = iosInfo.name;
        deviceData['systemVersion'] = iosInfo.systemVersion;
      }

      return deviceData;
    } catch (e) {
      return {'error': 'فشل في الحصول على معلومات الجهاز'};
    }
  }

  /// الحصول على معلومات IP
  Future<Map<String, dynamic>> _getIpInfo() async {
    try {
      return {
        'ip': '0.0.0.0', // سيتم تحديثه من خلال Supabase
        'location': {},
      };
    } catch (e) {
      return {'ip': '0.0.0.0', 'location': {}};
    }
  }

  /// تحديث الجلسة
  Future<void> _updateSession() async {
    try {
      // حساب المدة بالثواني بدلاً من الدقائق
      final durationSeconds =
          DateTime.now().difference(_sessionStart!).inSeconds;

      await _supabase
          .from('user_sessions')
          .update({
            // تحديث الحقول لتتوافق مع هيكل الجدول
            'duration_seconds': durationSeconds,
            'is_active': true,
            'status': 'active',
          })
          .eq('session_id', _sessionId!);
    } catch (e) {
      _logger.error('خطأ في تحديث الجلسة', error: e);
    }
  }

  /// إرسال الأحداث المؤقتة
  Future<void> _flushEvents() async {
    if (_eventQueue.isEmpty) return;

    try {
      final eventsData =
          _eventQueue
              .map(
                (event) => {
                  'event_name': event.name,
                  'properties': event.properties,
                  'timestamp': event.timestamp.toIso8601String(),
                  'user_id': _userId ?? 'anonymous',
                  'session_id': _sessionId,
                },
              )
              .toList();

      await _supabase.from('user_analytics').insert(eventsData);

      _logger.info('تم إرسال ${_eventQueue.length} حدث تحليلي');
      _eventQueue.clear();
    } catch (e) {
      _logger.error('خطأ في إرسال الأحداث التحليلية', error: e);
    }
  }

  /// جلب الإحصائيات من قاعدة البيانات
  Future<Map<String, dynamic>> _fetchDatabaseStats() async {
    try {
      // جلب إحصائيات مختلفة من الجداول
      final stats = <String, dynamic>{};

      // إحصائيات المنتجات
      final productsCount =
          await _supabase.from('products').select('id').count();
      stats['total_products'] = productsCount.count;

      // إحصائيات الطلبات
      final ordersCount = await _supabase.from('orders').select('id').count();
      stats['total_orders'] = ordersCount.count;

      // إحصائيات المستخدمين
      final usersCount = await _supabase.from('profiles').select('id').count();
      stats['total_users'] = usersCount.count;

      return stats;
    } catch (e) {
      _logger.error('خطأ في جلب إحصائيات قاعدة البيانات', error: e);
      return {};
    }
  }

  /// فحص صحة التخزين المؤقت
  bool _isCacheValid() {
    if (_lastCacheUpdate == null) return false;
    return DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// حفظ البيانات في التخزين المؤقت
  void _cacheStats(String key, dynamic data) {
    _cachedStats[key] = data;
    _lastCacheUpdate = DateTime.now();
  }

  /// الحصول على الأكثر مشاهدة
  List<Map<String, dynamic>> _getMostViewed(Map<String, int> data) {
    final sorted =
        data.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

    return sorted
        .take(5)
        .map((e) => {'name': e.key, 'count': e.value})
        .toList();
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> _getPerformanceStats() {
    final stats = <String, dynamic>{};

    for (final entry in _performanceMetrics.entries) {
      final operation = entry.key;
      final metrics = entry.value;

      if (metrics.isNotEmpty) {
        stats[operation] = {
          'average': metrics.reduce((a, b) => a + b) / metrics.length,
          'min': metrics.reduce((a, b) => a < b ? a : b),
          'max': metrics.reduce((a, b) => a > b ? a : b),
          'count': metrics.length,
        };
      }
    }

    return stats;
  }

  /// التحقق من كون الحدث مهم
  bool _isCriticalEvent(String eventName) {
    const criticalEvents = ['purchase', 'error', 'crash', 'login', 'logout'];
    return criticalEvents.contains(eventName);
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      _isEnabled = _prefs.getBool('analytics_enabled') ?? true;
    } catch (e) {
      _logger.warning(
        'تعذر تحميل إعدادات التحليلات',
        data: {'error': e.toString()},
      );
    }
  }

  /// تحميل خصائص المستخدم
  Future<void> _loadUserProperties() async {
    try {
      final propertiesJson = _prefs.getString('user_properties');
      if (propertiesJson != null) {
        _userProperties.addAll(jsonDecode(propertiesJson));
      }
    } catch (e) {
      _logger.warning(
        'تعذر تحميل خصائص المستخدم',
        data: {'error': e.toString()},
      );
    }
  }

  /// حفظ خصائص المستخدم
  Future<void> _saveUserProperties() async {
    try {
      await _prefs.setString('user_properties', jsonEncode(_userProperties));
    } catch (e) {
      _logger.warning('تعذر حفظ خصائص المستخدم', data: {'error': e.toString()});
    }
  }

  /// تحديث مقاييس المستخدم
  void _updateUserMetrics(Map<String, dynamic> properties) {
    // حساب المقاييس المشتقة من خصائص المستخدم
    _userMetrics['total_events'] = _eventCounts.values.fold(
      0,
      (sum, count) => sum + count,
    );
    _userMetrics['unique_events'] = _eventCounts.length;
    _userMetrics['last_activity'] = DateTime.now().toIso8601String();

    // إضافة مقاييس الشاشات
    if (_screenViews.isNotEmpty) {
      _userMetrics['most_visited_screen'] =
          _screenViews.entries.reduce((a, b) => a.value > b.value ? a : b).key;
      _userMetrics['unique_screens_visited'] = _screenViews.length;
    }

    // إضافة مقاييس أوقات البقاء في الشاشات
    if (_screenTimeTracking.isNotEmpty) {
      final totalTime = _screenTimeTracking.values.fold(
        Duration.zero,
        (sum, duration) => sum + duration,
      );
      _userMetrics['total_screen_time_minutes'] = totalTime.inMinutes;
      _userMetrics['average_screen_time_minutes'] =
          totalTime.inMinutes / _screenTimeTracking.length;
    }

    // إضافة مقاييس مخصصة من خصائص المستخدم
    properties.forEach((key, value) {
      if (value is num) {
        _userMetrics['user_$key'] = value;
      }
    });
  }

  /// إنشاء معرف جلسة
  String _generateSessionId() {
    return 'session_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// تسجيل حدث مخصص (للتوافق مع الكود القديم)
  void logCustomEvent(String eventName, Map<String, dynamic> properties) {
    trackEvent(eventName, properties: properties);
  }

  /// تسجيل خطأ (للتوافق مع الكود القديم)
  void logError(String context, String error) {
    trackError(error, context: context);
  }

  /// تتبع حدث المستخدم (للتوافق مع الكود القديم)
  void trackUserEvent(String eventName, {Map<String, dynamic>? properties}) {
    trackEvent(eventName, properties: properties);
  }

  /// تسجيل عرض منتج (للتوافق مع الكود القديم)
  void logProductView(String productId, {Map<String, dynamic>? properties}) {
    trackProductView(productId, properties: properties);
  }

  /// تنظيف الموارد
  @override
  Future<void> dispose() async {
    _sessionTimer?.cancel();
    _flushTimer?.cancel();
    await _flushEvents();

    // إنهاء الجلسة
    if (_sessionId != null) {
      try {
        // حساب المدة بالثواني بدلاً من الدقائق
        final durationSeconds =
            DateTime.now().difference(_sessionStart!).inSeconds;

        await _supabase
            .from('user_sessions')
            .update({
              'ended_at': DateTime.now().toIso8601String(),
              'duration_seconds': durationSeconds,
              'is_active': false,
              'status': 'terminated',
            })
            .eq('session_id', _sessionId!);
      } catch (e) {
        _logger.error('خطأ في إنهاء الجلسة', error: e);
      }
    }

    super.dispose();
  }
}

/// حدث تحليلي
class AnalyticsEvent {
  final String name;
  final Map<String, dynamic> properties;
  final DateTime timestamp;

  AnalyticsEvent({
    required this.name,
    required this.properties,
    required this.timestamp,
  });
}

/// بيانات التحليلات الموحدة
class UnifiedAnalyticsData {
  final int sessionDuration;
  final int currentSessionEvents;
  final Map<String, int> screenViews;
  final List<Map<String, dynamic>> mostViewedScreens;
  final Map<String, int> productViews;
  final List<Map<String, dynamic>> mostViewedProducts;
  final Map<String, int> searchQueries;
  final List<Map<String, dynamic>> topSearchQueries;
  final Map<String, int> buttonClicks;
  final Map<String, int> userInteractions;
  final List<Map<String, dynamic>> userJourney;
  final Map<String, dynamic> performanceMetrics;
  final Map<String, dynamic> databaseStats;
  final Map<String, dynamic> userProperties;
  final Map<String, int> eventCounts;
  final Map<String, dynamic> userMetrics;
  final Map<String, Duration> screenTimeTracking;
  final Map<String, DateTime> lastEventTimes;

  UnifiedAnalyticsData({
    required this.sessionDuration,
    required this.currentSessionEvents,
    required this.screenViews,
    required this.mostViewedScreens,
    required this.productViews,
    required this.mostViewedProducts,
    required this.searchQueries,
    required this.topSearchQueries,
    required this.buttonClicks,
    required this.userInteractions,
    required this.userJourney,
    required this.performanceMetrics,
    required this.databaseStats,
    required this.userProperties,
    required this.eventCounts,
    required this.userMetrics,
    required this.screenTimeTracking,
    required this.lastEventTimes,
  });

  factory UnifiedAnalyticsData.empty() => UnifiedAnalyticsData(
    sessionDuration: 0,
    currentSessionEvents: 0,
    screenViews: {},
    mostViewedScreens: [],
    productViews: {},
    mostViewedProducts: [],
    searchQueries: {},
    topSearchQueries: [],
    buttonClicks: {},
    userInteractions: {},
    userJourney: [],
    performanceMetrics: {},
    databaseStats: {},
    userProperties: {},
    eventCounts: {},
    userMetrics: {},
    screenTimeTracking: {},
    lastEventTimes: {},
  );
}

/// توصية تحليلية
class AnalyticsRecommendation {
  final String type;
  final String title;
  final String description;
  final double priority;

  AnalyticsRecommendation({
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
  });
}
