-- ===================================================================
-- جداول المستخدمين والملفات الشخصية
-- 
-- هذا الملف يحتوي على:
-- 1. جدول الملفات الشخصية للمستخدمين
-- 2. جدول العناوين
-- 3. دوال مزامنة بيانات المستخدمين
-- 5. الفهارس لتحسين الأداء

-- ===================================================================
-- 1. جدول الملفات الشخصية (المستخدمين)
-- ===================================================================
-- جدول الملفات الشخصية للمستخدمين
-- يخزن المعلومات الأساسية للمستخدمين ويرتبط بجدول auth.users
CREATE TABLE IF NOT EXISTS profiles (
    -- المعرف الفريد للمستخدم (يتطابق مع معرف المستخدم في نظام المصادقة)
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- معلومات المستخدم الأساسية
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    
    -- نوع الحساب وصلاحياته
    profile_type VARCHAR(20) NOT NULL DEFAULT 'customer' 
        CHECK (profile_type IN ('customer', 'admin')),
    
    -- معلومات إضافية للمستخدم
    birth_date DATE,
    governorate VARCHAR(50),
    center VARCHAR(50),
    
    -- إحصائيات وبيانات تتبع
    last_login_at TIMESTAMPTZ,
    order_count INTEGER DEFAULT 0,
    wishlist_count INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    
    -- بيانات وصفية وإعدادات
    raw_user_meta_data JSONB,
    
    -- إحصائيات الإشعارات
    unread_notifications_count INTEGER DEFAULT 0,
    total_notifications_count INTEGER DEFAULT 0,
    last_notification_check TIMESTAMPTZ,
    
    -- حالة الحساب
    is_active BOOLEAN DEFAULT true,
    
    -- طوابع زمنية
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_profile_type ON profiles(profile_type);
CREATE INDEX IF NOT EXISTS idx_profiles_governorate ON profiles(governorate);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- ===================================================================
-- 2. جدول العناوين
-- ===================================================================
CREATE TABLE IF NOT EXISTS addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title VARCHAR(50) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    street_address TEXT NOT NULL,
    city VARCHAR(50) NOT NULL,
    state VARCHAR(50) NOT NULL,
    postal_code VARCHAR(10),
    phone_number VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT false,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_addresses_user_id ON addresses(user_id);
CREATE INDEX IF NOT EXISTS idx_addresses_is_default ON addresses(is_default);

-- ===================================================================
-- 3. جدول إعدادات الإشعارات
-- ===================================================================
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    push_notifications BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT true,
    sms_notifications BOOLEAN DEFAULT false,
    order_updates BOOLEAN DEFAULT true,
    promotional_offers BOOLEAN DEFAULT true,
    new_products BOOLEAN DEFAULT false,
    price_drops BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);

-- ===================================================================
-- 4. جدول مواقع المستخدمين (للتوصيل)
-- ===================================================================
CREATE TABLE IF NOT EXISTS user_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    address_text TEXT,
    location_type VARCHAR(20) DEFAULT 'delivery' CHECK (location_type IN ('delivery', 'pickup', 'current')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_locations_user_id ON user_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_location_type ON user_locations(location_type);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- دالة تنظيف المستخدمين المعلقين (بدون ملف شخصي)
CREATE OR REPLACE FUNCTION cleanup_orphaned_users()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    deleted_count INTEGER := 0;
    user_record RECORD;
BEGIN
    -- البحث عن المستخدمين في auth.users الذين لا يملكون ملف شخصي
    -- والذين تم إنشاؤهم منذ أكثر من ساعة (لتجنب حذف المستخدمين الجدد)
    FOR user_record IN
        SELECT au.id, au.email, au.created_at
        FROM auth.users au
        LEFT JOIN profiles p ON au.id = p.id
        WHERE p.id IS NULL
        AND au.created_at < NOW() - INTERVAL '1 hour'
        AND au.email_confirmed_at IS NULL
    LOOP
        BEGIN
            -- حذف المستخدم من auth.users
            DELETE FROM auth.users WHERE id = user_record.id;
            deleted_count := deleted_count + 1;

            RAISE NOTICE 'تم حذف المستخدم المعلق: % (تم إنشاؤه في: %)',
                user_record.email, user_record.created_at;

        EXCEPTION
            WHEN OTHERS THEN
                RAISE WARNING 'فشل في حذف المستخدم المعلق %: %',
                    user_record.email, SQLERRM;
        END;
    END LOOP;

    RETURN deleted_count;
END;
$$;

-- دالة إنشاء مستخدم وملف تعريف تلقائياً (محدثة)
CREATE OR REPLACE FUNCTION create_user_and_profile()
RETURNS TRIGGER AS $$
DECLARE
    user_email TEXT;
    user_name TEXT;
    user_phone TEXT;
    user_address TEXT;
    user_birth_date DATE;
    user_governorate TEXT;
    user_center TEXT;
    user_meta_data JSONB;
    v_success BOOLEAN := false;
BEGIN
    -- استخراج البيانات من البيانات الوصفية
    user_meta_data := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);
    
    -- التأكد من وجود البريد الإلكتروني
    user_email := COALESCE(NEW.email, user_meta_data->>'email', 'user-' || NEW.id || '@temp.com');

    -- استخراج الاسم من البيانات الوصفية أو البريد الإلكتروني
    user_name := COALESCE(
        user_meta_data->>'name',
        user_meta_data->>'full_name',
        user_meta_data->>'display_name',
        NULLIF(split_part(user_email, '@', 1), ''),
        'مستخدم جديد'
    );

    -- استخراج باقي البيانات
    user_phone := COALESCE(user_meta_data->>'phone', NEW.phone, '');
    user_address := NULLIF(COALESCE(user_meta_data->>'address', ''), '');
    user_governorate := NULLIF(COALESCE(user_meta_data->>'governorate', ''), '');
    user_center := NULLIF(COALESCE(user_meta_data->>'center', ''), '');

    -- معالجة تاريخ الميلاد بأمان
    user_birth_date := NULL;
    IF user_meta_data->>'birth_date' IS NOT NULL THEN
        BEGIN
            user_birth_date := (user_meta_data->>'birth_date')::DATE;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'تنسيق تاريخ الميلاد غير صالح: %', user_meta_data->>'birth_date';
                user_birth_date := NULL;
        END;
    END IF;

    -- فحص البريد الإلكتروني لتجنب التكرار
    IF EXISTS (SELECT 1 FROM profiles WHERE email = user_email AND id != NEW.id) THEN
        RAISE EXCEPTION 'البريد الإلكتروني % مستخدم بالفعل', user_email;
    END IF;

    -- إنشاء ملف تعريف المستخدم
    INSERT INTO profiles (
        id, email, name, phone, address, birth_date,
        governorate, center, profile_type, raw_user_meta_data,
        created_at, updated_at
    ) VALUES (
        NEW.id, user_email, user_name, user_phone, user_address,
        user_birth_date, user_governorate, user_center,
        'customer', user_meta_data, NOW(), NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        name = EXCLUDED.name,
        phone = EXCLUDED.phone,
        address = EXCLUDED.address,
        birth_date = EXCLUDED.birth_date,
        governorate = EXCLUDED.governorate,
        center = EXCLUDED.center,
        updated_at = NOW(),
        raw_user_meta_data = EXCLUDED.raw_user_meta_data;
    
    v_success := true;

    -- إنشاء جميع السجلات المرتبطة في معاملة واحدة
    BEGIN
        -- إنشاء إعدادات إشعارات افتراضية
        INSERT INTO notification_settings (user_id)
        VALUES (NEW.id)
        ON CONFLICT (user_id) DO NOTHING;

        -- إنشاء إعدادات الخصوصية الافتراضية (إذا كان الجدول موجودًا)
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_privacy_settings') THEN
            INSERT INTO user_privacy_settings (user_id)
            VALUES (NEW.id)
            ON CONFLICT (user_id) DO NOTHING;
        END IF;

        -- إنشاء إعدادات التطبيق الافتراضية (إذا كان الجدول موجودًا)
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_app_settings') THEN
            INSERT INTO user_app_settings (user_id)
            VALUES (NEW.id)
            ON CONFLICT (user_id) DO NOTHING;
        END IF;

    EXCEPTION
        WHEN undefined_table THEN
            RAISE NOTICE 'أحد الجداول المطلوبة غير موجود';
        WHEN undefined_function THEN
            RAISE NOTICE 'إحدى الدوال المطلوبة غير موجودة';
        WHEN OTHERS THEN
            RAISE NOTICE 'خطأ في إنشاء السجلات المرتبطة: %', SQLERRM;
            -- تسجيل الخطأ في سجل النظام
            BEGIN
                INSERT INTO system_logs (
                    log_type,
                    details,
                    message,
                    created_at
                ) VALUES (
                    'user_related_records_error',
                    jsonb_build_object(
                        'user_id', NEW.id,
                        'error_code', SQLSTATE,
                        'error_detail', SQLERRM
                    ),
                    'خطأ في إنشاء السجلات المرتبطة: ' || SQLERRM,
                    NOW()
                );
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'فشل في تسجيل الخطأ في system_logs: %', SQLERRM;
            END;
    END;

    RETURN NEW;

EXCEPTION
    WHEN OTHERS THEN
        -- تسجيل الخطأ في سجل النظام باستخدام الأعمدة الصحيحة
        BEGIN
            INSERT INTO system_logs (
                log_type,
                details,
                message,
                created_at
            ) VALUES (
                'user_registration_error',
                jsonb_build_object(
                    'user_id', NEW.id,
                    'email', user_email,
                    'error_code', SQLSTATE,
                    'error_detail', SQLERRM,
                    'success_before_error', v_success,
                    'meta_data', user_meta_data
                ),
                'خطأ في إنشاء ملف تعريف المستخدم: ' || SQLERRM,
                NOW()
            );
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'فشل في تسجيل الخطأ في system_logs: %', SQLERRM;
        END;

        RAISE EXCEPTION 'خطأ في إنشاء ملف تعريف المستخدم: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;


-- دالة مزامنة البيانات الوصفية عند التحديث (محدثة)
CREATE OR REPLACE FUNCTION sync_user_metadata()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_meta_data JSONB;
BEGIN
    -- عند تحديث بيانات auth.users
    IF TG_OP = 'UPDATE' THEN
        user_meta_data := COALESCE(NEW.raw_user_meta_data, '{}'::jsonb);

        -- تحديث البيانات الوصفية في profiles
        UPDATE profiles
        SET
            raw_user_meta_data = user_meta_data,
            email = COALESCE(NEW.email, email),
            phone = COALESCE(
                user_meta_data->>'phone',
                NEW.phone,
                phone
            ),
            name = COALESCE(
                user_meta_data->>'name',
                user_meta_data->>'full_name',
                user_meta_data->>'display_name',
                name
            ),
            address = COALESCE(
                NULLIF(user_meta_data->>'address', ''),
                address
            ),
            birth_date = CASE
                WHEN user_meta_data->>'birth_date' IS NOT NULL
                THEN (user_meta_data->>'birth_date')::DATE
                ELSE birth_date
            END,
            governorate = COALESCE(
                NULLIF(user_meta_data->>'governorate', ''),
                governorate
            ),
            center = COALESCE(
                NULLIF(user_meta_data->>'center', ''),
                center
            ),
            updated_at = NOW()
        WHERE id = NEW.id;

        RETURN NEW;
    END IF;

    RETURN NULL;
END;
$$;

-- دالة مزامنة جميع المستخدمين (محدثة)
CREATE OR REPLACE FUNCTION sync_all_user_metadata()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_record RECORD;
    updated_count INTEGER := 0;
    error_count INTEGER := 0;
    error_messages TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- مزامنة جميع المستخدمين
    FOR user_record IN
        SELECT
            au.id,
            au.email,
            au.phone,
            au.raw_user_meta_data
        FROM auth.users au
        JOIN profiles p ON au.id = p.id
    LOOP
        BEGIN
            -- تحديث البيانات الوصفية
            UPDATE profiles
            SET
                raw_user_meta_data = user_record.raw_user_meta_data,
                email = COALESCE(user_record.email, email),
                phone = COALESCE(
                    user_record.raw_user_meta_data->>'phone',
                    user_record.phone,
                    phone
                ),
                name = COALESCE(
                    user_record.raw_user_meta_data->>'name',
                    user_record.raw_user_meta_data->>'full_name',
                    user_record.raw_user_meta_data->>'display_name',
                    name
                ),
                updated_at = NOW()
            WHERE id = user_record.id;

            updated_count := updated_count + 1;
        EXCEPTION
            WHEN OTHERS THEN
                error_count := error_count + 1;
                error_messages := array_append(
                    error_messages,
                    format('خطأ في تحديث المستخدم %s: %s', user_record.id, SQLERRM)
                );
        END;
    END LOOP;

    -- تسجيل الأخطاء في سجل النظام
    IF error_count > 0 THEN
        BEGIN
            INSERT INTO system_logs (
                log_type,
                details,
                message,
                created_at
            ) VALUES (
                'bulk_sync_error',
                jsonb_build_object(
                    'error_count', error_count,
                    'updated_count', updated_count,
                    'errors', error_messages
                ),
                array_to_string(error_messages, E'\n'),
                NOW()
            );
        EXCEPTION
            WHEN undefined_table THEN
                -- تجاهل الخطأ إذا لم يكن جدول system_logs موجود
                NULL;
        END;
    END IF;

    RETURN format(
        'تم تحديث %s مستخدم. حدثت %s أخطاء.',
        updated_count,
        error_count
    );
END;
$$;



-- 6. جدول سجل المحادثات
-- ===================================================================
CREATE TABLE IF NOT EXISTS chat_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    messages JSONB NOT NULL DEFAULT '[]'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_created_at ON chat_history(created_at);


