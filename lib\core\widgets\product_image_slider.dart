import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/theme/watermark_config.dart';

/// عارض صور المنتج
/// يعرض صور المنتج في شكل شريط متحرك (كاروسيل)
class ProductImageSlider extends StatefulWidget {
  final List<String> images;
  final double height;
  final bool autoPlay;
  final Duration autoPlayInterval;
  final Duration autoPlayAnimationDuration;
  final bool enlargeCenterPage;
  final Function(int)? onPageChanged;
  final BoxFit imageFit;

  const ProductImageSlider({
    super.key,
    required this.images,
    this.height = 250,
    this.autoPlay = false, // ✅ إلغاء التقليب التلقائي
    this.autoPlayInterval = const Duration(seconds: 5),
    this.autoPlayAnimationDuration = const Duration(milliseconds: 800),
    this.enlargeCenterPage = false,
    this.onPageChanged,
    this.imageFit = BoxFit.contain,
  });

  @override
  State<ProductImageSlider> createState() => _ProductImageSliderState();
}

class _ProductImageSliderState extends State<ProductImageSlider> {
  int _currentIndex = 0;
  final CarouselSliderController _carouselController =
      CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    if (widget.images.isEmpty) {
      return _buildEmptyImagePlaceholder();
    }

    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        CarouselSlider.builder(
          carouselController: _carouselController,
          itemCount: widget.images.length,
          options: CarouselOptions(
            height: widget.height,
            viewportFraction: 1.0,
            initialPage: 0,
            enableInfiniteScroll: widget.images.length > 1,
            reverse: false,
            autoPlay: widget.autoPlay && widget.images.length > 1,
            autoPlayInterval: widget.autoPlayInterval,
            autoPlayAnimationDuration: widget.autoPlayAnimationDuration,
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: widget.enlargeCenterPage,
            scrollDirection: Axis.horizontal,
            onPageChanged: (index, reason) {
              setState(() {
                _currentIndex = index;
              });
              if (widget.onPageChanged != null) {
                widget.onPageChanged!(index);
              }
            },
          ),
          itemBuilder: (context, index, realIndex) {
            final imageUrl = widget.images[index];
            return Stack(
              children: [
                Container(
                  width: MediaQuery.of(context).size.width,
                  decoration: BoxDecoration(color: Colors.grey[200]),
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: widget.imageFit,
                    errorWidget:
                        (context, url, error) => const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            size: 50,
                            color: Colors.grey,
                          ),
                        ),
                    placeholder:
                        (context, url) =>
                            const Center(child: CircularProgressIndicator()),
                  ),
                ),
                Center(
                  child: Transform.rotate(
                    angle:
                        WatermarkConfig
                            .defaultConfig
                            .watermarkRotationInRadians,
                    child: Text(
                      WatermarkConfig.defaultConfig.watermarkText,
                      style: WatermarkConfig.defaultConfig.watermarkTextStyle,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
        // مؤشرات الصور (تظهر فقط إذا كان هناك أكثر من صورة)
        if (widget.images.length > 1)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children:
                  widget.images.asMap().entries.map((entry) {
                    return GestureDetector(
                      onTap: () => _carouselController.animateToPage(entry.key),
                      child: Container(
                        width: 8.0,
                        height: 8.0,
                        margin: const EdgeInsets.symmetric(horizontal: 4.0),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color:
                              _currentIndex == entry.key
                                  ? AppTheme.primaryColor
                                  : Colors.grey.withOpacity(0.5),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyImagePlaceholder() {
    return Container(
      height: widget.height,
      color: Colors.grey[200],
      child: const Center(
        child: Icon(Icons.image_not_supported, size: 50, color: Colors.grey),
      ),
    );
  }
}
