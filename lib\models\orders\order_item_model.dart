import "dart:convert";

import "package:motorcycle_parts_shop/models/cart_item_model.dart";
import "package:motorcycle_parts_shop/models/products/product_model.dart";

/// نموذج يمثل عنصر في الطلب
/// يتوافق مع جدول order_items في قاعدة البيانات
class OrderItemModel {
  /// معرف عنصر الطلب (يتوافق مع حقل id في جدول order_items)
  final String id;

  /// معرف المنتج (يتوافق مع حقل product_id في جدول order_items)
  final String productId;

  /// اسم المنتج (يتوافق مع حقل product_name في جدول order_items)
  final String productName;

  /// رمز المنتج (SKU) (يتوافق مع حقل product_sku في جدول order_items)
  final String productSku;

  /// صورة المنتج (يتم استخراجها من حقل product_snapshot في جدول order_items)
  final String productImage;

  /// سعر الوحدة (يتوافق مع حقل unit_price في جدول order_items)
  final double unitPrice;

  /// الكمية (يتوافق مع حقل quantity في جدول order_items)
  final int quantity;

  /// السعر الإجمالي (يتوافق مع حقل total_price في جدول order_items)
  final double totalPrice;

  OrderItemModel({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productSku,
    required this.productImage,
    required this.unitPrice,
    required this.quantity,
    required this.totalPrice,
  });

  /// للتوافق مع الكود القديم - سيتم إزالته في المستقبل
  @Deprecated("استخدم unitPrice بدلاً من ذلك للتوافق مع قاعدة البيانات")
  double get price => unitPrice;

  /// للتوافق مع الكود القديم - سيتم إزالته في المستقبل
  @Deprecated("استخدم totalPrice بدلاً من ذلك للتوافق مع قاعدة البيانات")
  double get subtotal => totalPrice;

  /// تحويل من JSON إلى كائن OrderItemModel
  /// يتعامل مع اختلاف أسماء الحقول بين النموذج وقاعدة البيانات
  factory OrderItemModel.fromJson(Map<String, dynamic> json) {
    // استخراج صورة المنتج من product_snapshot إذا كان متاحاً
    String productImage = json["product_image"] as String? ?? "";

    // محاولة استخراج الصورة من product_snapshot إذا كانت product_image غير متوفرة
    if (productImage.isEmpty && json["product_snapshot"] != null) {
      try {
        final Map<String, dynamic> snapshot =
            json["product_snapshot"] is String
                ? jsonDecode(json["product_snapshot"])
                : json["product_snapshot"];

        if (snapshot.containsKey("image_url") &&
            snapshot["image_url"] != null) {
          productImage = snapshot["image_url"];
        } else if (snapshot.containsKey("image_urls") &&
            snapshot["image_urls"] is List &&
            (snapshot["image_urls"] as List).isNotEmpty) {
          productImage = (snapshot["image_urls"] as List).first.toString();
        }
      } catch (e) {
        // تجاهل أي أخطاء في تحليل product_snapshot
        print("خطأ في استخراج صورة المنتج من product_snapshot: $e");
      }
    }

    // استخراج رمز المنتج (SKU)
    String productSku = json["product_sku"] as String? ?? "";

    // محاولة استخراج SKU من product_snapshot إذا كان product_sku غير متوفر
    if (productSku.isEmpty && json["product_snapshot"] != null) {
      try {
        final Map<String, dynamic> snapshot =
            json["product_snapshot"] is String
                ? jsonDecode(json["product_snapshot"])
                : json["product_snapshot"];

        if (snapshot.containsKey("sku") && snapshot["sku"] != null) {
          productSku = snapshot["sku"];
        }
      } catch (e) {
        // تجاهل أي أخطاء في تحليل product_snapshot
        print("خطأ في استخراج رمز المنتج من product_snapshot: $e");
      }
    }

    return OrderItemModel(
      id: json["id"] as String,
      productId: json["product_id"] as String,
      productName: json["product_name"] as String,
      productSku: productSku,
      productImage: productImage,
      // استخدام unit_price من قاعدة البيانات أو price كبديل
      unitPrice:
          json.containsKey("unit_price")
              ? (json["unit_price"] as num).toDouble()
              : (json["price"] as num).toDouble(),
      quantity: json["quantity"] as int,
      // استخدام total_price من قاعدة البيانات أو subtotal كبديل
      totalPrice:
          json.containsKey("total_price")
              ? (json["total_price"] as num).toDouble()
              : (json["subtotal"] as num).toDouble(),
    );
  }

  /// تحويل الكائن إلى JSON للتخزين في قاعدة البيانات
  /// يستخدم أسماء الحقول المتوافقة مع جدول order_items
  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "product_id": productId,
      "product_name": productName,
      "product_sku": productSku,
      "unit_price": unitPrice,
      "quantity": quantity,
      "total_price": totalPrice,
      // تخزين صورة المنتج في product_snapshot للتوافق مع قاعدة البيانات
      "product_snapshot": jsonEncode({
        "name": productName,
        "sku": productSku,
        "image_url": productImage,
        "price": unitPrice,
      }),
    };
  }

  /// إنشاء نسخة معدلة من الكائن
  OrderItemModel copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productSku,
    String? productImage,
    double? unitPrice,
    int? quantity,
    double? totalPrice,
  }) {
    return OrderItemModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productSku: productSku ?? this.productSku,
      productImage: productImage ?? this.productImage,
      unitPrice: unitPrice ?? this.unitPrice,
      quantity: quantity ?? this.quantity,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  /// إنشاء عنصر طلب من عنصر سلة تسوق
  static OrderItemModel fromCartItem(CartItemModel cartItem, String orderId) {
    return OrderItemModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      productId: cartItem.productId,
      productName: cartItem.productName,
      productSku: _getProductSku(
        cartItem.productId,
      ), // استخراج رمز المنتج من قاعدة البيانات
      productImage: cartItem.productImage,
      unitPrice: cartItem.price, // استخدام سعر الوحدة من عنصر السلة
      quantity: cartItem.quantity,
      totalPrice: cartItem.totalPrice, // استخدام السعر الإجمالي من عنصر السلة
    );
  }

  /// دالة مساعدة للحصول على رمز المنتج (SKU) من قاعدة البيانات
  static String _getProductSku(String productId) {
    try {
      // في التطبيق الفعلي، يجب استخدام ProductModel.getById(productId) للحصول على رمز المنتج
      // لكن هذه الدالة غير متزامنة (async) ولا يمكن استخدامها في دالة متزامنة (sync)
      // لذلك نستخدم قيمة افتراضية مشتقة من معرف المنتج

      // يمكن تحسين هذه الدالة في المستقبل لاستخدام ذاكرة مؤقتة للمنتجات
      // أو تعديل دالة fromCartItem لتكون غير متزامنة (async)

      // تقصير معرف المنتج إلى 16 حرفًا كحد أقصى
      String shortId =
          productId.length > 16 ? productId.substring(0, 16) : productId;
      return "SKU-$shortId"; // قيمة افتراضية مشتقة من معرف المنتج
    } catch (e) {
      print("خطأ في الحصول على رمز المنتج: $e");
      return "SKU-UNKNOWN"; // قيمة افتراضية في حالة حدوث خطأ
    }
  }

  /// إنشاء عنصر طلب من منتج
  static OrderItemModel fromProduct(ProductModel product, int quantity) {
    return OrderItemModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      productId: product.id,
      productName: product.name,
      productSku: product.sku, // استخدام رمز المنتج مباشرة من نموذج المنتج
      productImage: product.mainImage ?? "",
      unitPrice:
          product.discountPrice ??
          product.price, // استخدام سعر الخصم إذا كان متاحًا
      quantity: quantity,
      totalPrice:
          (product.discountPrice ?? product.price) *
          quantity, // حساب السعر الإجمالي
    );
  }
}
