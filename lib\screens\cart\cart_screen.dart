import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:motorcycle_parts_shop/core/services/cart_service.dart';
import 'package:motorcycle_parts_shop/core/services/currency_service.dart';
import 'package:motorcycle_parts_shop/core/theme/app_theme.dart';
import 'package:motorcycle_parts_shop/core/utils/responsive_helper.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final cartService = Provider.of<CartService>(context);
    final cartItems = cartService.items;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          ResponsiveHelper.isMobile(context) ? 75 : 85,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryLightColor,
                AppTheme.accentColor,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
                spreadRadius: 2,
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveHelper.getPadding(
                  context,
                  mobile: 12,
                  tablet: 16,
                  desktop: 20,
                ),
                vertical: 8,
              ),
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: AppTheme.textLightColor,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.shopping_cart_rounded,
                      color: AppTheme.textLightColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'سلة التسوق',
                          style: AppTheme.cardTitle.copyWith(
                            color: AppTheme.textLightColor,
                            fontSize: ResponsiveHelper.getFontSize(context, 18),
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        if (cartItems.isNotEmpty)
                          Text(
                            '${cartItems.length} منتج',
                            style: AppTheme.cardSubtitle.copyWith(
                              color: AppTheme.textLightColor.withOpacity(0.8),
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (cartItems.isNotEmpty) ...[
                    if (cartItems.any((item) => item.selected))
                      _buildHeaderButton(
                        icon: Icons.delete_rounded,
                        onPressed: () => _showDeleteConfirmationDialog(
                          context,
                          cartService,
                        ),
                        tooltip: 'حذف المحدد',
                        color: AppTheme.errorColor,
                      ),
                    const SizedBox(width: 8),
                    _buildHeaderButton(
                      icon: cartItems.every((item) => item.selected)
                          ? Icons.deselect_rounded
                          : Icons.select_all_rounded,
                      onPressed: () => cartService.toggleSelectAll(),
                      tooltip: cartItems.every((item) => item.selected)
                          ? 'إلغاء تحديد الكل'
                          : 'تحديد الكل',
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : cartItems.isEmpty
              ? _buildEmptyCartView(context)
              : _buildCartView(context, cartService),
    );
  }

  Widget _buildEmptyCartView(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 24,
          tablet: 32,
          desktop: 40,
        );
        final iconSize = ResponsiveHelper.isMobile(context) ? 60.0 : 80.0;

        return Center(
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    gradient: AppTheme.cardGradient,
                    shape: BoxShape.circle,
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    size: iconSize,
                    color: AppTheme.textTertiaryColor,
                  ),
                ),
                const SizedBox(height: 32),
                Text(
                  'سلة التسوق فارغة',
                  style: AppTheme.heroTitle.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, 24),
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'ابدأ بإضافة منتجات رائعة إلى سلتك\nواستمتع بتجربة تسوق مميزة',
                  style: AppTheme.cardSubtitle.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, 16),
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                Container(
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: AppTheme.cardShadow,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pushNamed(context, '/home'),
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.shopping_bag_rounded,
                              color: AppTheme.textLightColor,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'تصفح المنتجات',
                              style: AppTheme.buttonText.copyWith(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pushNamed(context, '/categories'),
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.category_rounded,
                              color: AppTheme.primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تصفح الفئات',
                              style: AppTheme.cardTitle.copyWith(
                                color: AppTheme.primaryColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCartView(BuildContext context, CartService cartService) {
    final cartItems = cartService.items;

    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(ResponsiveHelper.getPadding(context)),
            itemCount: cartItems.length,
            itemBuilder: (context, index) {
              final item = cartItems[index];
              return _buildCartServiceItem(item, cartService);
            },
          ),
        ),
        _buildCheckoutSection(cartService),
      ],
    );
  }

  Widget _buildCartServiceItem(CartServiceItem item, CartService cartService) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final imageSize = ResponsiveHelper.isMobile(context) ? 60.0 : 80.0;
        final margin = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );
        final padding = ResponsiveHelper.getPadding(
          context,
          mobile: 8,
          tablet: 12,
          desktop: 16,
        );

        return Card(
          margin: EdgeInsets.only(bottom: margin),
          elevation: 4,
          shadowColor: AppTheme.primaryColor.withOpacity(0.1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: item.selected
                  ? LinearGradient(
                      colors: [
                        AppTheme.primaryColor.withOpacity(0.05),
                        AppTheme.accentColor.withOpacity(0.05),
                      ],
                    )
                  : null,
              border: item.selected
                  ? Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      width: 2,
                    )
                  : null,
            ),
            child: Padding(
              padding: EdgeInsets.all(padding + 4),
              child: ListTile(
                selected: item.selected,
                onLongPress: () => cartService.toggleItemSelection(item.id),
                leading: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    item.product.imageUrlList.isNotEmpty
                        ? item.product.imageUrlList.first
                        : '',
                    width: imageSize,
                    height: imageSize,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: imageSize,
                      height: imageSize,
                      color: Colors.grey[200],
                      child: const Icon(Icons.error),
                    ),
                  ),
                ),
                title: Text(
                  item.product.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      CurrencyService().formatPrice(item.product.price),
                      style: const TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: () {
                            if (item.quantity > 1) {
                              cartService.updateQuantity(
                                item.id,
                                item.quantity - 1,
                              );
                            }
                          },
                        ),
                        Text(
                          '${item.quantity}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () {
                            cartService.updateQuantity(
                              item.id,
                              item.quantity + 1,
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.delete, color: AppTheme.errorColor),
                  onPressed: () => _showDeleteConfirmationDialog(
                    context,
                    cartService,
                    item.id,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCheckoutSection(CartService cartService) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        final padding = ResponsiveHelper.getPadding(context);
        final buttonPadding = ResponsiveHelper.getPadding(
          context,
          mobile: 12,
          tablet: 16,
          desktop: 20,
        );

        return Container(
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'المجموع:',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${cartService.total} جم',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _proceedToCheckout(context, cartService);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.all(buttonPadding),
                  ),
                  child: const Text('متابعة الشراء'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(
    BuildContext context,
    CartService cartService, [
    String? itemId,
  ]) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('حذف العناصر المحددة'),
          content: const Text('هل أنت متأكد أنك تريد حذف العناصر المحددة؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                if (itemId != null) {
                  cartService.removeFromCart(itemId);
                } else {
                  cartService.removeSelectedItems();
                }
                setState(() {});
                Navigator.pop(context);
              },
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _proceedToCheckout(BuildContext context, CartService cartService) {
    if (cartService.items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('السلة فارغة، أضف منتجات للمتابعة')),
      );
      return;
    }

    final totalAmount = cartService.total;
    final depositAmount = (totalAmount * 0.1).toStringAsFixed(2);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الطلب'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('المجموع: $totalAmount جم'),
              const SizedBox(height: 16),
              const Text(
                'لضمان مصداقية الطلب، يرجى تحويل 10% من قيمة الطلب كعربون',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'قيمة العربون: $depositAmount جم',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('سيتم احتساب هذا المبلغ من إجمالي قيمة الطلب'),
              const SizedBox(height: 16),
              const Text(
                'اختر طريقة التحويل:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Row(
                                children: [
                                  Image.asset(
                                    'assets/images/vodafone_cash.png',
                                    width: 30,
                                    height: 30,
                                    errorBuilder: (context, error, stackTrace) =>
                                        const Icon(
                                      Icons.receipt,
                                      color: Colors.red,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('فودافون كاش'),
                                ],
                              ),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Text('رقم فودافون كاش للتحويل:'),
                                  const SizedBox(height: 8),
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      children: [
                                        const Expanded(
                                          child: SelectableText(
                                            '01207075721',
                                            style: TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.red,
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.copy),
                                          onPressed: () {
                                            Clipboard.setData(
                                              const ClipboardData(
                                                  text: '01207075721'),
                                            );
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                  content: Text('تم نسخ الرقم')),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('إغلاق'),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt,
                              color: Colors.red.shade700,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'فودافون كاش',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Row(
                                children: [
                                  Image.asset(
                                    'assets/images/instapay.png',
                                    width: 30,
                                    height: 30,
                                    errorBuilder: (context, error, stackTrace) =>
                                        const Icon(
                                      Icons.receipt,
                                      color: Colors.blue,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text('انستا باي'),
                                ],
                              ),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Text('رقم انستا باي للتحويل:'),
                                  const SizedBox(height: 8),
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      children: [
                                        const Expanded(
                                          child: SelectableText(
                                            '01207075721',
                                            style: TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.blue,
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(Icons.copy),
                                          onPressed: () {
                                            Clipboard.setData(
                                              const ClipboardData(
                                                  text: '01207075721'),
                                            );
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                  content: Text('تم نسخ الرقم')),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('إغلاق'),
                                ),
                              ],
                            );
                          },
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt,
                              color: Colors.blue.shade700,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'انستا باي',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'بعد التحويل، يرجى إرسال صورة الإيصال على الواتساب:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Row(
                  children: [
                    const Expanded(
                      child: SelectableText(
                        '01012312312',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () {
                        Clipboard.setData(
                          const ClipboardData(text: '01012312312'),
                        );
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('تم نسخ الرقم')),
                        );
                      },
                    ),
                    IconButton(
                      icon: const FaIcon(
                        FontAwesomeIcons.whatsapp,
                        color: Colors.green,
                      ),
                      onPressed: () async {
                        const whatsappUrl = 'https://wa.me/201012312312';
                        if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
                          await launchUrl(Uri.parse(whatsappUrl));
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('لا يمكن فتح الواتساب'),
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'تم تأكيد الطلب، يرجى تحويل العربون وإرسال صورة الإيصال',
                    ),
                    duration: Duration(seconds: 3),
                  ),
                );

                Future.delayed(const Duration(seconds: 2), () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إنشاء الطلب بنجاح!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  cartService.removeSelectedItems();
                });
              },
              child: const Text('تأكيد الطلب'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    Color? color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: IconButton(
        icon: Icon(icon, color: color ?? AppTheme.textLightColor, size: 22),
        onPressed: onPressed,
        tooltip: tooltip,
        splashRadius: 20,
        padding: const EdgeInsets.all(8),
      ),
    );
  }
}