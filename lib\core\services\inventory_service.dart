import 'package:flutter/material.dart';
import 'package:motorcycle_parts_shop/core/services/auth_supabase_service.dart';
import 'package:motorcycle_parts_shop/core/services/product_service.dart';
import 'package:motorcycle_parts_shop/models/inventory/inventory_movement_model.dart';
import 'package:motorcycle_parts_shop/models/products/product_model.dart'; // Import ProductService


/// خدمة إدارة المخزون
/// تستخدم للتعامل مع حركة المخزون وتحديث كميات المنتجات
class InventoryService extends ChangeNotifier {
  static final InventoryService _instance = InventoryService._internal();
  final AuthSupabaseService _authSupabaseService =
      AuthSupabaseService(); // Keep AuthSupabaseService for user info
  late final ProductService _productService; // Add ProductService instance
  bool _isInitialized = false;

  factory InventoryService() => _instance;

  InventoryService._internal();

  bool get isInitialized => _isInitialized;

  /// تهيئة خدمة المخزون
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (!_authSupabaseService.isInitialized) {
        await _authSupabaseService.initialize();
      }
      // Initialize ProductService
      _productService = ProductService(
        _authSupabaseService.client,
      ); // Pass Supabase client

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة المخزون: $e');
      rethrow;
    }
  }

  /// إضافة حركة مخزون جديدة
  /// [productId] معرف المنتج
  /// [quantityChange] التغيير في الكمية (موجب للإضافة، سالب للسحب)
  /// [reason] سبب التغيير (مثل: شراء، بيع، تعديل، إرجاع)
  /// [referenceId] معرف مرجعي (مثل معرف الطلب)
  /// [notes] ملاحظات إضافية
  Future<InventoryMovementModel> addInventoryMovement({
    required String productId,
    required int quantityChange,
    required String reason,
    String? referenceId,
    String? notes,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      final userId = _authSupabaseService.currentUser?.id;

      final data = {
        'product_id': productId,
        'quantity_change': quantityChange,
        'reason': reason,
        'reference_id': referenceId,
        'notes': notes,
        'created_by': userId,
      };

      final response =
          await _authSupabaseService.client
              .from('inventory_movements')
              .insert(data)
              .select()
              .single();

      return InventoryMovementModel.fromJson(response);
    } catch (e) {
      debugPrint('خطأ في إضافة حركة المخزون: $e');
      rethrow;
    }
  }

  /// الحصول على سجل حركة المخزون لمنتج معين
  Future<List<InventoryMovementModel>> getInventoryMovementsByProduct(
    String productId,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final response = await _authSupabaseService.client
          .from('inventory_movements')
          .select()
          .eq('product_id', productId)
          .order('created_at', ascending: false);

      return response
          .map((item) => InventoryMovementModel.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل حركة المخزون: $e');
      return [];
    }
  }

  /// الحصول على سجل حركة المخزون بناءً على السبب
  Future<List<InventoryMovementModel>> getInventoryMovementsByReason(
    String reason,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final response = await _authSupabaseService.client
          .from('inventory_movements')
          .select()
          .eq('reason', reason)
          .order('created_at', ascending: false);

      return response
          .map((item) => InventoryMovementModel.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل حركة المخزون: $e');
      return [];
    }
  }

  /// الحصول على سجل حركة المخزون بناءً على المعرف المرجعي
  Future<List<InventoryMovementModel>> getInventoryMovementsByReference(
    String referenceId,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final response = await _authSupabaseService.client
          .from('inventory_movements')
          .select()
          .eq('reference_id', referenceId)
          .order('created_at', ascending: false);

      return response
          .map((item) => InventoryMovementModel.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على سجل حركة المخزون: $e');
      return [];
    }
  }

  /// تحديث كمية المنتج مباشرة (للاستخدام الإداري فقط)
  /// يقوم بإنشاء حركة مخزون جديدة لتعكس التغيير
  Future<bool> updateProductQuantity({
    required String productId,
    required int newQuantity,
    String? notes,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      // الحصول على المنتج الحالي لمعرفة الكمية الحالية
      final product = await _productService.getProductDetails(
        productId,
      ); // Use ProductService
      if (product == null) return false;

      // حساب التغيير في الكمية
      final quantityChange =
          newQuantity -
          product.stockQuantity; // Assuming product.quantity is int

      // إضافة حركة مخزون جديدة
      await addInventoryMovement(
        productId: productId,
        quantityChange: quantityChange,
        reason: 'admin_adjustment',
        notes: notes ?? 'تعديل إداري للكمية',
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث كمية المنتج: $e');
      return false;
    }
  }

  /// التحقق من توفر كمية كافية من المنتج
  Future<bool> isProductAvailable(
    String productId,
    int requiredQuantity,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final product = await _productService.getProductDetails(
        productId,
      ); // Use ProductService
      if (product == null) return false;

      return product.stockQuantity >=
          requiredQuantity; // Assuming product.quantity is int
    } catch (e) {
      debugPrint('خطأ في التحقق من توفر المنتج: $e');
      return false;
    }
  }

  /// الحصول على المنتجات التي تحتاج إلى إعادة الطلب (الكمية أقل من الحد الأدنى)
  Future<List<ProductModel>> getLowStockProducts(int threshold) async {
    if (!_isInitialized) await initialize();

    try {
      final response = await _authSupabaseService.client
          .from('products')
          .select()
          .lte(
            'stock_quantity',
            threshold,
          ) // Assuming 'stock_quantity' is the correct column name based on models and other services
          .eq('is_available', true);

      return response.map((item) => ProductModel.fromJson(item)).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المنتجات منخفضة المخزون: $e');
      return [];
    }
  }
}
