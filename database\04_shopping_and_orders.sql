-- ===================================================================
-- جداول التسوق والطلبات

-- ===================================================================
-- 1. جدول طرق الشحن
-- ===================================================================
CREATE TABLE IF NOT EXISTS shipping_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    cost DECIMAL(8,2) NOT NULL DEFAULT 0.00,
    estimated_days INTEGER,
    rules JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================================================
-- 3. جدول سلة التسوق
-- ===================================================================
-- جدول سلات التسوق
CREATE TABLE IF NOT EXISTS carts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'converted', 'abandoned')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_carts_user_id ON carts(user_id);
CREATE INDEX IF NOT EXISTS idx_carts_status ON carts(status);
CREATE INDEX IF NOT EXISTS idx_carts_updated_at ON carts(updated_at);

-- ===================================================================
-- جدول عناصر سلة التسوق
-- ===================================================================
CREATE TABLE IF NOT EXISTS cart_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cart_id UUID NOT NULL REFERENCES carts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    product_name VARCHAR(200) NOT NULL,
    product_image TEXT,
    price DECIMAL(12,2) NOT NULL,
    discount_price DECIMAL(12,2),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    selected BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(cart_id, product_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON cart_items(product_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_updated ON cart_items(updated_at);
-- فهرس مفقود لتحسين عمليات checkout
CREATE INDEX IF NOT EXISTS idx_cart_items_selected ON cart_items(selected) WHERE selected = true;
-- فهرس مركب للسلة النشطة والعناصر المختارة
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_selected ON cart_items(cart_id, selected);

-- ===================================================================
-- 4. جدول قائمة الرغبات
-- ===================================================================
CREATE TABLE IF NOT EXISTS wishlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_wishlists_user_id ON wishlists(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlists_product_id ON wishlists(product_id);

CREATE INDEX IF NOT EXISTS idx_wishlists_created ON wishlists(user_id, created_at);

-- ===================================================================
-- 5. جدول الطلبات
-- ===================================================================
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(20) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE RESTRICT,
    address_id UUID NOT NULL REFERENCES addresses(id) ON DELETE RESTRICT,
    shipping_method_id UUID REFERENCES shipping_methods(id) ON DELETE SET NULL,
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    shipping_cost DECIMAL(8,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
    notes TEXT,
    tracking_number VARCHAR(100),
    estimated_delivery TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    cancellation_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس محسنة للأداء
-- فهرس فريد لرقم الطلب مع تضمين معلومات أساسية للعرض السريع
CREATE UNIQUE INDEX IF NOT EXISTS idx_orders_order_number 
ON orders(order_number) 
INCLUDE (user_id, status, total_amount, created_at);

-- فهرس مركب للمستخدم والحالة - يغني عن idx_orders_user_id
CREATE INDEX IF NOT EXISTS idx_orders_user_status 
ON orders(user_id, status) 
INCLUDE (order_number, total_amount, created_at);

-- فهرس مركب للحالة والتاريخ - يغني عن idx_orders_status و idx_orders_created_at
CREATE INDEX IF NOT EXISTS idx_orders_status_date 
ON orders(status, created_at) 
INCLUDE (order_number, total_amount);

-- فهرس للطلبات المكتملة - يغني عن idx_orders_total_amount_simple
CREATE INDEX IF NOT EXISTS idx_orders_completed 
ON orders(total_amount, status) 
INCLUDE (order_number, created_at)
WHERE status = 'delivered';

-- فهرس للتقارير المالية - يغني عن idx_orders_amount_date
CREATE INDEX IF NOT EXISTS idx_orders_financial 
ON orders(created_at, total_amount) 
INCLUDE (status, user_id);

-- فهرس للبحث بتاريخ التسليم - محسن
CREATE INDEX IF NOT EXISTS idx_orders_delivery_date 
ON orders(delivered_at) 
INCLUDE (order_number, user_id, total_amount)
WHERE delivered_at IS NOT NULL;

-- فهرس على عمود shipping_method_id لتحسين أداء المفتاح الأجنبي
CREATE INDEX IF NOT EXISTS idx_orders_shipping_method_id ON orders(shipping_method_id);

-- ===================================================================
-- 6. جدول عناصر الطلبات
-- ===================================================================
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    product_name VARCHAR(200) NOT NULL,
    product_sku VARCHAR(50) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    product_snapshot JSONB, -- نسخة من بيانات المنتج وقت الطلب
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_price ON order_items(unit_price, quantity);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- محفز إنشاء رقم الطلب - محسن مع معالجة أفضل للأخطاء
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_order_number TEXT;
    v_retry_count INTEGER := 0;
    v_max_retries CONSTANT INTEGER := 3;
BEGIN
    -- التحقق مما إذا كان رقم الطلب موجودًا بالفعل
    IF NEW.order_number IS NOT NULL AND NEW.order_number != '' THEN
        -- التحقق من تنسيق رقم الطلب
        IF NOT NEW.order_number ~ '^[0-9]{8}[0-9]{4}$' THEN
            RAISE WARNING 'تنسيق رقم الطلب % غير صالح، سيتم إنشاء رقم جديد', NEW.order_number;
            -- سنقوم بإنشاء رقم جديد
        ELSE
            -- التحقق من تفرد رقم الطلب
            IF EXISTS (SELECT 1 FROM orders WHERE order_number = NEW.order_number AND id != NEW.id) THEN
                RAISE WARNING 'رقم الطلب % موجود بالفعل، سيتم إنشاء رقم جديد', NEW.order_number;
                -- سنقوم بإنشاء رقم جديد
            ELSE
                -- رقم الطلب صالح وفريد، نستخدمه كما هو
                RETURN NEW;
            END IF;
        END IF;
    END IF;
    
    -- محاولة إنشاء رقم طلب فريد مع إعادة المحاولة في حالة الفشل
    LOOP
        BEGIN
            -- استدعاء دالة إنشاء رقم الطلب
            v_order_number := generate_order_number();
            
            -- التحقق من أن الرقم ليس فارغًا
            IF v_order_number IS NULL OR v_order_number = '' THEN
                RAISE EXCEPTION 'فشل في إنشاء رقم طلب صالح';
            END IF;
            
            -- التحقق من تفرد الرقم
            IF NOT EXISTS (SELECT 1 FROM orders WHERE order_number = v_order_number) THEN
                -- تعيين رقم الطلب الجديد
                NEW.order_number := v_order_number;
                EXIT; -- الخروج من الحلقة
            END IF;
            
            -- زيادة عداد المحاولات
            v_retry_count := v_retry_count + 1;
            
            -- التحقق من تجاوز الحد الأقصى للمحاولات
            IF v_retry_count >= v_max_retries THEN
                RAISE EXCEPTION 'تجاوز الحد الأقصى لمحاولات إنشاء رقم طلب فريد (%)، آخر رقم تم إنشاؤه: %', 
                    v_max_retries, v_order_number;
            END IF;
            
            -- الانتظار لفترة قصيرة قبل المحاولة التالية
            PERFORM pg_sleep(0.1);
            
        EXCEPTION WHEN OTHERS THEN
            -- تسجيل الخطأ
            RAISE WARNING 'خطأ في محاولة إنشاء رقم الطلب (المحاولة %): %', v_retry_count, SQLERRM;
            
            -- التحقق من تجاوز الحد الأقصى للمحاولات
            IF v_retry_count >= v_max_retries THEN
                -- إنشاء رقم طلب بديل في حالة الفشل
                NEW.order_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
                                   LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
                RAISE WARNING 'تم إنشاء رقم طلب بديل بعد فشل المحاولات: %', NEW.order_number;
                EXIT; -- الخروج من الحلقة
            END IF;
            
            -- زيادة عداد المحاولات
            v_retry_count := v_retry_count + 1;
            
            -- الانتظار لفترة قصيرة قبل المحاولة التالية
            PERFORM pg_sleep(0.1);
        END;
    END LOOP;
    
    RETURN NEW;
END;
$$;
