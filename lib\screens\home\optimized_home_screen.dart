import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/services/error_handler_service.dart';
import '../../core/services/optimized_product_service.dart';
import '../../core/widgets/product_card.dart';
import '../../core/widgets/shimmer_loading.dart';
import '../../models/category_model.dart';
import '../../models/products/product_model.dart';

/// شاشة الرئيسية المحسنة مع تحميل تدريجي وأداء محسن
class OptimizedHomeScreen extends StatefulWidget {
  const OptimizedHomeScreen({super.key});

  @override
  State<OptimizedHomeScreen> createState() => _OptimizedHomeScreenState();
}

class _OptimizedHomeScreenState extends State<OptimizedHomeScreen>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // الحفاظ على حالة الشاشة
  @override
  bool get wantKeepAlive => true;

  // المتغيرات
  late AnimationController _animationController;
  late OptimizedProductService _productService;

  // البيانات الأساسية
  List<CategoryModel> _categories = [];
  List<ProductModel> _featuredProducts = [];

  // البيانات الثانوية
  List<ProductModel> _bestSellingProducts = [];
  List<ProductModel> _offerProducts = [];

  // حالة التحميل
  bool _isLoading = true;
  bool _isSecondaryDataLoaded = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _productService = context.read<OptimizedProductService>();
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل البيانات بشكل تدريجي
  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل البيانات الأساسية أولاً
      await _loadEssentialData();

      // تحميل البيانات الثانوية في الخلفية
      _loadSecondaryDataInBackground();
    } catch (e) {
      _handleError(e);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        _animationController.forward();
      }
    }
  }

  /// تحميل البيانات الأساسية (الفئات والمنتجات المميزة)
  Future<void> _loadEssentialData() async {
    final futures = await Future.wait([
      _loadCategories(),
      _loadFeaturedProducts(),
    ]);

    if (mounted) {
      setState(() {
        // تحديث البيانات الأساسية
      });
    }
  }

  /// تحميل الفئات
  Future<void> _loadCategories() async {
    try {
      // تحميل الفئات من الخدمة (يمكن تطبيقها لاحقاً)
      _categories = []; // مؤقت - سيتم استبداله بخدمة الفئات
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفئات: $e');
    }
  }

  /// تحميل المنتجات المميزة
  Future<void> _loadFeaturedProducts() async {
    try {
      _featuredProducts = await _productService.getFeaturedProducts(limit: 6);
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المنتجات المميزة: $e');
    }
  }

  /// تحميل البيانات الثانوية في الخلفية
  void _loadSecondaryDataInBackground() {
    Future.delayed(const Duration(milliseconds: 500), () async {
      if (!mounted) return;

      try {
        final futures = await Future.wait([
          _productService.getBestSelling(limit: 10),
          _productService.getOffers(limit: 8),
        ]);

        if (mounted) {
          setState(() {
            _bestSellingProducts = futures[0];
            _offerProducts = futures[1];
            _isSecondaryDataLoaded = true;
          });
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات الثانوية: $e');
      }
    });
  }

  /// معالجة الأخطاء
  void _handleError(dynamic error) {
    ErrorHandler.handleError(
      error,
      context: context,
      contextInfo: 'HomeScreen._loadData',
    );

    if (mounted) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل البيانات';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // مطلوب لـ AutomaticKeepAliveClientMixin

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            if (_isLoading)
              SliverFillRemaining(child: _buildLoadingShimmer())
            else if (_errorMessage != null)
              SliverFillRemaining(child: _buildErrorWidget())
            else ...[
              _buildSearchSection(),
              if (_categories.isNotEmpty) _buildCategoriesSection(),
              if (_featuredProducts.isNotEmpty) _buildFeaturedProductsSection(),
              if (_isSecondaryDataLoaded) ...[
                if (_bestSellingProducts.isNotEmpty) _buildBestSellingSection(),
                if (_offerProducts.isNotEmpty) _buildOffersSection(),
              ] else
                _buildSecondaryLoadingSection(),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء شريط التطبيق
  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'متجر قطع غيار الدراجات النارية',
          style: TextStyle(fontSize: 16),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withOpacity(0.8),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _navigateToNotifications(),
        ),
        IconButton(
          icon: const Icon(Icons.shopping_cart_outlined),
          onPressed: () => _navigateToCart(),
        ),
      ],
    );
  }

  /// بناء قسم البحث
  Widget _buildSearchSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: GestureDetector(
          onTap: () => _navigateToSearch(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                Icon(Icons.search, color: Colors.grey[600]),
                const SizedBox(width: 12),
                Text(
                  'ابحث عن قطع الغيار...',
                  style: TextStyle(color: Colors.grey[600], fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم الفئات
  Widget _buildCategoriesSection() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'الفئات',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return _buildCategoryCard(category);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم المنتجات المميزة
  Widget _buildFeaturedProductsSection() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المنتجات المميزة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _navigateToProducts('featured'),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _featuredProducts.length,
              itemBuilder: (context, index) {
                final product = _featuredProducts[index];
                return SizedBox(
                  width: 200,
                  child: ProductCard(product: product),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الأكثر مبيعاً
  Widget _buildBestSellingSection() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'الأكثر مبيعاً',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(
            height: 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _bestSellingProducts.length,
              itemBuilder: (context, index) {
                final product = _bestSellingProducts[index];
                return SizedBox(
                  width: 200,
                  child: ProductCard(product: product),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم العروض
  Widget _buildOffersSection() {
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'العروض الخاصة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),
          SizedBox(
            height: 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _offerProducts.length,
              itemBuilder: (context, index) {
                final product = _offerProducts[index];
                return SizedBox(
                  width: 200,
                  child: ProductCard(product: product),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تحميل البيانات الثانوية
  Widget _buildSecondaryLoadingSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 8),
            Text(
              'جاري تحميل المزيد من المنتجات...',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شيمر التحميل
  Widget _buildLoadingShimmer() {
    return ListView(
      children: List.generate(
        5,
        (index) => Padding(
          padding: const EdgeInsets.all(16),
          child: ShimmerLoading(width: double.infinity, height: 200),
        ),
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? 'حدث خطأ غير متوقع',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الفئة
  Widget _buildCategoryCard(CategoryModel category) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(left: 12),
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(Icons.category, color: Theme.of(context).primaryColor),
          ),
          const SizedBox(height: 8),
          Text(
            category.name,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // دوال التنقل
  void _navigateToNotifications() {
    Navigator.pushNamed(context, '/notifications');
  }

  void _navigateToCart() {
    Navigator.pushNamed(context, '/cart');
  }

  void _navigateToSearch() {
    Navigator.pushNamed(context, '/search');
  }

  void _navigateToProducts(String type) {
    Navigator.pushNamed(context, '/products', arguments: {'type': type});
  }
}
