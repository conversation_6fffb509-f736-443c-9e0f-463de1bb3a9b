
class InventoryMovementModel {
  final String id;
  final String productId;
  final int quantityChange;
  final String reason;
  final String? referenceId;
  final String? notes;
  final String? createdBy;
  final DateTime createdAt;

  InventoryMovementModel({
    required this.id,
    required this.productId,
    required this.quantityChange,
    required this.reason,
    this.referenceId,
    this.notes,
    this.createdBy,
    required this.createdAt,
  });

  factory InventoryMovementModel.fromJson(Map<String, dynamic> json) {
    return InventoryMovementModel(
      id: json['id'] as String,
      productId: json['product_id'] as String,
      quantityChange: json['quantity_change'] as int,
      reason: json['reason'] as String,
      referenceId: json['reference_id'] as String?,
      notes: json['notes'] as String?,
      createdBy: json['created_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'quantity_change': quantityChange,
      'reason': reason,
      'reference_id': referenceId,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
    };
  }

  InventoryMovementModel copyWith({
    String? id,
    String? productId,
    int? quantityChange,
    String? reason,
    String? referenceId,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
  }) {
    return InventoryMovementModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      quantityChange: quantityChange ?? this.quantityChange,
      reason: reason ?? this.reason,
      referenceId: referenceId ?? this.referenceId,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  bool get isAddition => quantityChange > 0;
  bool get isReduction => quantityChange < 0;
  bool get hasReference => referenceId != null;
  bool get hasNotes => notes != null && notes!.trim().isNotEmpty;
}
